#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=40720, tid=35748
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Thu Jan  2 15:33:43 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.175404 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001ec9085f5f0):  JavaThread "main"             [_thread_in_vm, id=35748, stack(0x0000009810c00000,0x0000009810d00000) (1024K)]

Stack: [0x0000009810c00000,0x0000009810d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x8bba1e]
V  [jvm.dll+0x684b95]
V  [jvm.dll+0x684bfa]
V  [jvm.dll+0x687416]
V  [jvm.dll+0x6872e2]
V  [jvm.dll+0x68554e]
V  [jvm.dll+0x3cb99f]
V  [jvm.dll+0x214abb]
V  [jvm.dll+0x5c2c4f]
V  [jvm.dll+0x22444b]
V  [jvm.dll+0x837a6c]
V  [jvm.dll+0x838b12]
V  [jvm.dll+0x839302]
V  [jvm.dll+0x21b1ab]
V  [jvm.dll+0x21da21]
V  [jvm.dll+0x212b29]
V  [jvm.dll+0x5c2c23]
V  [jvm.dll+0x22444b]
V  [jvm.dll+0x837a6c]
V  [jvm.dll+0x838b12]
V  [jvm.dll+0x8390d4]
V  [jvm.dll+0x838d68]
V  [jvm.dll+0x27530b]
V  [jvm.dll+0x275525]
V  [jvm.dll+0x5e5991]
V  [jvm.dll+0x5e80c4]
V  [jvm.dll+0x3e5912]
V  [jvm.dll+0x3e53b0]
C  0x000001ec9c0b773c

The last pc belongs to getstatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.time.ZoneId.ofWithPrefix(Ljava/lang/String;IZ)Ljava/time/ZoneId;+16 java.base@21.0.4
j  java.time.ZoneId.of(Ljava/lang/String;Z)Ljava/time/ZoneId;+59 java.base@21.0.4
j  java.time.ZoneId.of(Ljava/lang/String;)Ljava/time/ZoneId;+2 java.base@21.0.4
j  sun.security.util.DisabledAlgorithmConstraints$DenyAfterConstraint.<init>(Ljava/lang/String;III)V+70 java.base@21.0.4
j  sun.security.util.DisabledAlgorithmConstraints$Constraints.<init>(Ljava/lang/String;Ljava/util/Set;)V+624 java.base@21.0.4
j  sun.security.util.DisabledAlgorithmConstraints.<init>(Ljava/lang/String;Lsun/security/util/AlgorithmDecomposer;)V+119 java.base@21.0.4
j  sun.security.ssl.SSLAlgorithmConstraints.<clinit>()V+33 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000001ec9c0a100d
j  sun.security.ssl.ProtocolVersion.<init>(Ljava/lang/String;IILjava/lang/String;Z)V+47 java.base@21.0.4
j  sun.security.ssl.ProtocolVersion.<clinit>()V+13 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000001ec9c0a100d
j  sun.security.ssl.SSLContextImpl$AbstractTLSContext.<clinit>()V+7 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000001ec9c0a100d
j  java.lang.Class.forName0(Ljava/lang/String;ZLjava/lang/ClassLoader;Ljava/lang/Class;)Ljava/lang/Class;+0 java.base@21.0.4
j  java.lang.Class.forName(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Class;+19 java.base@21.0.4
j  java.lang.Class.forName(Ljava/lang/String;)Ljava/lang/Class;+6 java.base@21.0.4
j  java.security.Provider$Service.getImplClass()Ljava/lang/Class;+64 java.base@21.0.4
j  java.security.Provider$Service.getDefaultConstructor()Ljava/lang/reflect/Constructor;+46 java.base@21.0.4
j  java.security.Provider$Service.newInstanceOf()Ljava/lang/Object;+1 java.base@21.0.4
j  java.security.Provider$Service.newInstanceUtil(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;+5 java.base@21.0.4
j  java.security.Provider$Service.newInstance(Ljava/lang/Object;)Ljava/lang/Object;+216 java.base@21.0.4
j  sun.security.jca.GetInstance.getInstance(Ljava/security/Provider$Service;Ljava/lang/Class;)Lsun/security/jca/GetInstance$Instance;+2 java.base@21.0.4
j  sun.security.jca.GetInstance.getInstance(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/String;)Lsun/security/jca/GetInstance$Instance;+56 java.base@21.0.4
j  javax.net.ssl.SSLContext.getInstance(Ljava/lang/String;)Ljavax/net/ssl/SSLContext;+12 java.base@21.0.4
j  externalApp.ExternalAppUtil.sendIdeRequest(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)LexternalApp/ExternalAppUtil$Result;+34
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+37
v  ~StubRoutines::call_stub 0x000001ec9c0a100d
getstatic  178 getstatic  [0x000001ec9c0b76a0, 0x000001ec9c0b78a0]  512 bytes
[MachCode]
  0x000001ec9c0b76a0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001ec9c0b76c0: 4424 0800 | 0000 00eb | 0150 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d138 c1eb | 1081 e3ff 
  0x000001ec9c0b76e0: 0000 0081 | fbb2 0000 | 000f 84b4 | 0000 00bb | b200 0000 | e805 0000 | 00e9 9900 | 0000 488b 
  0x000001ec9c0b7700: d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 
  0x000001ec9c0b7720: ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 5053 | eab0 ff7f | 0000 ffd0 | 4883 c408 
  0x000001ec9c0b7740: e90c 0000 | 0048 b850 | 53ea b0ff | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 
  0x000001ec9c0b7760: 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f | 0800 0f84 
  0x000001ec9c0b7780: 0500 0000 | e977 97fe | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c341 | 0fb7 5501 | 488b 4dd0 
  0x000001ec9c0b77a0: c1e2 0248 | 8b5c d148 | 8b44 d150 | 4c8b 4cd1 | 404d 8b49 | 704d 8b09 | c1e8 1c83 | e00f 0f85 
  0x000001ec9c0b77c0: 0b00 0000 | 410f be04 | 1950 e9b5 | 0000 0083 | f801 0f85 | 0b00 0000 | 410f b604 | 1950 e9a1 
  0x000001ec9c0b77e0: 0000 0083 | f808 0f85 | 0e00 0000 | 418b 0419 | 48c1 e003 | 50e9 8a00 | 0000 83f8 | 040f 850a 
  0x000001ec9c0b7800: 0000 0041 | 8b04 1950 | e977 0000 | 0083 f802 | 0f85 0b00 | 0000 410f | b704 1950 | e963 0000 
  0x000001ec9c0b7820: 0083 f803 | 0f85 0b00 | 0000 410f | bf04 1950 | e94f 0000 | 0083 f805 | 0f85 1a00 | 0000 498b 
  0x000001ec9c0b7840: 0419 4883 | ec10 4889 | 0424 48c7 | 4424 0800 | 0000 00e9 | 2c00 0000 | 83f8 060f | 8514 0000 
  0x000001ec9c0b7860: 00c4 c17a | 1004 1948 | 83ec 08c5 | fa11 0424 | e90f 0000 | 00c4 c17b | 1004 1948 | 83ec 10c5 
  0x000001ec9c0b7880: fb11 0424 | 410f b65d | 0349 83c5 | 0349 ba40 | b079 b1ff | 7f00 0041 | ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ecb16767f0, length=11, elements={
0x000001ec9085f5f0, 0x000001ecb14518f0, 0x000001ecb1454620, 0x000001ecb14d25a0,
0x000001ecb14c5b90, 0x000001ecb14c69e0, 0x000001ecb14c7440, 0x000001ecb14ac520,
0x000001ecb1499680, 0x000001ecb168c290, 0x000001ecb16a4880
}

Java Threads: ( => current thread )
=>0x000001ec9085f5f0 JavaThread "main"                              [_thread_in_vm, id=35748, stack(0x0000009810c00000,0x0000009810d00000) (1024K)]
  0x000001ecb14518f0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=18816, stack(0x0000009811400000,0x0000009811500000) (1024K)]
  0x000001ecb1454620 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17904, stack(0x0000009811500000,0x0000009811600000) (1024K)]
  0x000001ecb14d25a0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=30376, stack(0x0000009811600000,0x0000009811700000) (1024K)]
  0x000001ecb14c5b90 JavaThread "Attach Listener"            daemon [_thread_blocked, id=39728, stack(0x0000009811700000,0x0000009811800000) (1024K)]
  0x000001ecb14c69e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=40020, stack(0x0000009811800000,0x0000009811900000) (1024K)]
  0x000001ecb14c7440 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=3716, stack(0x0000009811900000,0x0000009811a00000) (1024K)]
  0x000001ecb14ac520 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=32572, stack(0x0000009811a00000,0x0000009811b00000) (1024K)]
  0x000001ecb1499680 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=24912, stack(0x0000009811b00000,0x0000009811c00000) (1024K)]
  0x000001ecb168c290 JavaThread "Notification Thread"        daemon [_thread_blocked, id=36576, stack(0x0000009811c00000,0x0000009811d00000) (1024K)]
  0x000001ecb16a4880 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=21852, stack(0x0000009811d00000,0x0000009811e00000) (1024K)]
Total: 11

Other Threads:
  0x000001ecae8ad5e0 VMThread "VM Thread"                           [id=24360, stack(0x0000009811300000,0x0000009811400000) (1024K)]
  0x000001ecae8743c0 WatcherThread "VM Periodic Task Thread"        [id=23764, stack(0x0000009811200000,0x0000009811300000) (1024K)]
  0x000001ecae6f0980 WorkerThread "GC Thread#0"                     [id=40644, stack(0x0000009810d00000,0x0000009810e00000) (1024K)]
  0x000001ecae7321c0 ConcurrentGCThread "G1 Main Marker"            [id=17524, stack(0x0000009810e00000,0x0000009810f00000) (1024K)]
  0x000001ecae732bd0 WorkerThread "G1 Conc#0"                       [id=12256, stack(0x0000009810f00000,0x0000009811000000) (1024K)]
  0x000001ecae7b6520 ConcurrentGCThread "G1 Refine#0"               [id=33952, stack(0x0000009811000000,0x0000009811100000) (1024K)]
  0x000001ecae7b7090 ConcurrentGCThread "G1 Service"                [id=36492, stack(0x0000009811100000,0x0000009811200000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffb1784748] Metaspace_lock - owner thread: 0x000001ec9085f5f0

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 0K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 8550K, committed 8640K, reserved 1114112K
  class space    used 710K, committed 768K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 125|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 126|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000001eca5720000,0x000001eca6710000] _byte_map_base: 0x000001eca2702000

Marking Bits: (CMBitMap*) 0x000001ec908c10c0
 Bits: [0x000001eca6710000, 0x000001ecae620000)

Polling page: 0x000001ec8efa0000

Metaspace:

Usage:
  Non-class:      7.66 MB used.
      Class:    710.77 KB used.
       Both:      8.35 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.69 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     768.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.44 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.20 MB
       Class:  15.24 MB
        Both:  23.44 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 10.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 135.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 35.
num_chunk_merges: 0.
num_chunk_splits: 15.
num_chunks_enlarged: 9.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=68Kb max_used=68Kb free=119931Kb
 bounds [0x000001ec9c640000, 0x000001ec9c8b0000, 0x000001eca3b70000]
CodeHeap 'profiled nmethods': size=120000Kb used=370Kb max_used=370Kb free=119629Kb
 bounds [0x000001ec94b70000, 0x000001ec94de0000, 0x000001ec9c0a0000]
CodeHeap 'non-nmethods': size=5760Kb used=1311Kb max_used=1326Kb free=4448Kb
 bounds [0x000001ec9c0a0000, 0x000001ec9c310000, 0x000001ec9c640000]
 total_blobs=672 nmethods=249 adapters=328
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.163 Thread 0x000001ecb1499680 nmethod 234 0x000001ec9c64e890 code [0x000001ec9c64ea20, 0x000001ec9c64eaf0]
Event: 0.164 Thread 0x000001ecb14ac520 nmethod 221 0x000001ec9c64f190 code [0x000001ec9c64f320, 0x000001ec9c64f688]
Event: 0.164 Thread 0x000001ecb14ac520  231       4       java.util.HashMap::afterNodeInsertion (1 bytes)
Event: 0.164 Thread 0x000001ecb14ac520 nmethod 231 0x000001ec9c64f890 code [0x000001ec9c64fa00, 0x000001ec9c64fa88]
Event: 0.166 Thread 0x000001ecb1499680  237       3       java.lang.invoke.MemberName::isInvocable (8 bytes)
Event: 0.166 Thread 0x000001ecb1499680 nmethod 237 0x000001ec94bc9b90 code [0x000001ec94bc9d20, 0x000001ec94bc9ef0]
Event: 0.166 Thread 0x000001ecb1499680  238       3       java.lang.invoke.MemberName::anyFlagSet (15 bytes)
Event: 0.166 Thread 0x000001ecb1499680 nmethod 238 0x000001ec94bc9f90 code [0x000001ec94bca120, 0x000001ec94bca288]
Event: 0.166 Thread 0x000001ecb1499680  240       1       java.lang.invoke.MethodType::ptypes (5 bytes)
Event: 0.166 Thread 0x000001ecb1499680 nmethod 240 0x000001ec9c64fe90 code [0x000001ec9c650020, 0x000001ec9c6500f0]
Event: 0.166 Thread 0x000001ecb1499680  241       1       java.lang.invoke.MethodType::form (5 bytes)
Event: 0.166 Thread 0x000001ecb1499680 nmethod 241 0x000001ec9c650190 code [0x000001ec9c650320, 0x000001ec9c6503f0]
Event: 0.168 Thread 0x000001ecb1499680  242       3       java.util.TreeMap::setColor (10 bytes)
Event: 0.168 Thread 0x000001ecb1499680 nmethod 242 0x000001ec94bca310 code [0x000001ec94bca4a0, 0x000001ec94bca5d0]
Event: 0.170 Thread 0x000001ecb1499680  243       3       jdk.internal.org.objectweb.asm.ByteVector::putUTF8 (144 bytes)
Event: 0.171 Thread 0x000001ecb1499680 nmethod 243 0x000001ec94bca690 code [0x000001ec94bca900, 0x000001ec94bcb3c8]
Event: 0.171 Thread 0x000001ecb1499680  244       3       jdk.internal.org.objectweb.asm.SymbolTable::get (13 bytes)
Event: 0.171 Thread 0x000001ecb1499680 nmethod 244 0x000001ec94bcb890 code [0x000001ec94bcba20, 0x000001ec94bcbb80]
Event: 0.171 Thread 0x000001ecb1499680  246       3       jdk.internal.util.ReferencedKeyMap::get (26 bytes)
Event: 0.171 Thread 0x000001ecb1499680 nmethod 246 0x000001ec94bcbc90 code [0x000001ec94bcbec0, 0x000001ec94bcc6b0]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.013 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.170 Loading class java/util/regex/Pattern$Qtype
Event: 0.170 Loading class java/util/regex/Pattern$Qtype done
Event: 0.170 Loading class java/util/regex/Pattern$BmpCharPropertyGreedy
Event: 0.170 Loading class java/util/regex/Pattern$CharPropertyGreedy
Event: 0.170 Loading class java/util/regex/Pattern$CharPropertyGreedy done
Event: 0.170 Loading class java/util/regex/Pattern$BmpCharPropertyGreedy done
Event: 0.170 Loading class java/util/regex/Pattern$Curly
Event: 0.170 Loading class java/util/regex/Pattern$Curly done
Event: 0.171 Loading class java/util/regex/Pattern$BnM
Event: 0.171 Loading class java/util/regex/Pattern$BnM done
Event: 0.171 Loading class java/util/regex/Pattern$SliceS
Event: 0.171 Loading class java/util/regex/Pattern$SliceS done
Event: 0.171 Loading class sun/security/util/DisabledAlgorithmConstraints$DenyAfterConstraint
Event: 0.171 Loading class sun/security/util/DisabledAlgorithmConstraints$DenyAfterConstraint done
Event: 0.171 Loading class java/time/ZoneId
Event: 0.171 Loading class java/time/ZoneId done
Event: 0.171 Loading class java/time/ZoneOffset
Event: 0.171 Loading class java/time/temporal/TemporalAccessor
Event: 0.171 Loading class java/time/temporal/TemporalAccessor done
Event: 0.171 Loading class java/time/temporal/TemporalAdjuster

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.094 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.094 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (16 events):
Event: 0.012 Thread 0x000001ec9085f5f0 Thread added: 0x000001ec9085f5f0
Event: 0.053 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14518f0
Event: 0.053 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb1454620
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14d25a0
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14c5b90
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14c69e0
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14c7440
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb14ac520
Event: 0.054 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb1499680
Event: 0.074 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb168c290
Event: 0.078 Thread 0x000001ec9085f5f0 Thread added: 0x000001ecb16a4880
Event: 0.084 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.086 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.091 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.145 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.152 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll


Dynamic libraries:
0x00007ff79d830000 - 0x00007ff79d83a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ff873250000 - 0x00007ff873467000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff872310000 - 0x00007ff8723d4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff870b60000 - 0x00007ff870f1a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff870780000 - 0x00007ff870891000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff849e90000 - 0x00007ff849eab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ff831b50000 - 0x00007ff831b68000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ff8718d0000 - 0x00007ff871a7e000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8709c0000 - 0x00007ff8709e6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff872e70000 - 0x00007ff872e99000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8708a0000 - 0x00007ff8709bb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff870520000 - 0x00007ff8705ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff833d90000 - 0x00007ff834022000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007ff871670000 - 0x00007ff871717000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff872dc0000 - 0x00007ff872df1000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000071990000 - 0x000000007199d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ff8715b0000 - 0x00007ff871662000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8717a0000 - 0x00007ff871847000 	C:\WINDOWS\System32\sechost.dll
0x00007ff870f20000 - 0x00007ff870f48000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff871390000 - 0x00007ff8714a4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff85e110000 - 0x00007ff85e213000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ff871a90000 - 0x00007ff872308000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff872eb0000 - 0x00007ff872f0e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ff86fe70000 - 0x00007ff86fe7a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff861130000 - 0x00007ff86113c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ff860ad0000 - 0x00007ff860b5d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007fffb0ac0000 - 0x00007fffb1877000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ff871720000 - 0x00007ff871791000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8703f0000 - 0x00007ff87043d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff861390000 - 0x00007ff8613c4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8703d0000 - 0x00007ff8703e3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff86f490000 - 0x00007ff86f4a8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff861110000 - 0x00007ff86111a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ff86dc50000 - 0x00007ff86de82000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff871000000 - 0x00007ff87138f000 	C:\WINDOWS\System32\combase.dll
0x00007ff872f10000 - 0x00007ff872fe7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff862940000 - 0x00007ff862972000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff870700000 - 0x00007ff87077b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff831a90000 - 0x00007ff831aaf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ff824760000 - 0x00007ff824778000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ff86e3c0000 - 0x00007ff86ecc8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff86e280000 - 0x00007ff86e3bf000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff872aa0000 - 0x00007ff872b9a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff870450000 - 0x00007ff87047b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff849e40000 - 0x00007ff849e50000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ff86aec0000 - 0x00007ff86aff6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff86f910000 - 0x00007ff86f979000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff80bd20000 - 0x00007ff80bd36000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ff833400000 - 0x00007ff83340e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ff8709f0000 - 0x00007ff870b56000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff86fe30000 - 0x00007ff86fe5d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff86fdf0000 - 0x00007ff86fe27000 	C:\WINDOWS\SYSTEM32\NTASN1.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 9, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 45884K (0% of 33293192K total physical memory with 5635424K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 8490K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 45696B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 12 days 10:47 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (5503M free)
TotalPageFile size 42752M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 44M, peak: 44M
current process commit charge ("private bytes"): 604M, peak: 606M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
