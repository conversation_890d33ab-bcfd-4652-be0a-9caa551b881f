#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit area from 0x000001b381290000 to 0x000001b3812a0000 of length 65536.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (c:/BuildAgent/work/1eae4dac1d648407/src/hotspot/os/windows/os_windows.cpp:3296), pid=72216, tid=75448
#
# JRE version:  (11.0.6+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 22000 (10.0.22000.1335)
Time: Sun Feb 12 09:52:01 2023 �й���׼ʱ�� elapsed time: 0 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001b3ea1c5000):  JavaThread "Unknown thread" [_thread_in_vm, id=75448, stack(0x000000a514300000,0x000000a514400000)]

Stack: [0x000000a514300000,0x000000a514400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5de3ca]
V  [jvm.dll+0x710ae5]
V  [jvm.dll+0x712005]
V  [jvm.dll+0x7126b3]
V  [jvm.dll+0x23e708]
V  [jvm.dll+0x5db874]
V  [jvm.dll+0x5d0aa5]
V  [jvm.dll+0x2fb0fb]
V  [jvm.dll+0x2fb06a]
V  [jvm.dll+0x2faf42]
V  [jvm.dll+0x2fff25]
V  [jvm.dll+0x348735]
V  [jvm.dll+0x348e26]
V  [jvm.dll+0x348823]
V  [jvm.dll+0x2d58a8]
V  [jvm.dll+0x2d6a47]
V  [jvm.dll+0x6ef917]
V  [jvm.dll+0x6f110c]
V  [jvm.dll+0x355d69]
V  [jvm.dll+0x6d4afe]
V  [jvm.dll+0x3bf3d3]
V  [jvm.dll+0x3c1921]
C  [jli.dll+0x5363]
C  [ucrtbase.dll+0x26c0c]
C  [KERNEL32.DLL+0x155a0]
C  [ntdll.dll+0x485b]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b3e7e72b60, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001b3ea1d8800 GCTaskThread "GC Thread#0" [stack: 0x000000a514400000,0x000000a514500000] [id=48516]
  0x000001b3ea257000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000a514500000,0x000000a514600000] [id=62044]
  0x000001b3ea259000 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000a514600000,0x000000a514700000] [id=69684]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff784a6227]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001b3ea1c0e30] Heap_lock - owner thread: 0x000001b3ea1c5000

Heap address: 0x0000000603e00000, size: 8130 MB, Compressed Oops mode: Non-zero based: 0x0000000603e00000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (2 events):
Event: 0.004 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
Event: 0.005 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff79e0f0000 - 0x00007ff79e0fa000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.exe
0x00007fffd5160000 - 0x00007fffd5369000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffd44a0000 - 0x00007fffd455e000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffd2bc0000 - 0x00007fffd2f3b000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffd2960000 - 0x00007fffd2a71000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fffb40b0000 - 0x00007fffb40c9000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jli.dll
0x00007fffcf700000 - 0x00007fffcf717000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\VCRUNTIME140.dll
0x00007fffd4d40000 - 0x00007fffd4eed000 	C:\WINDOWS\System32\USER32.dll
0x00007fffd2930000 - 0x00007fffd2956000 	C:\WINDOWS\System32\win32u.dll
0x00007fffa7610000 - 0x00007fffa78b5000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007fffd5030000 - 0x00007fffd5059000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffd4af0000 - 0x00007fffd4b93000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffd2630000 - 0x00007fffd2748000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffd2f40000 - 0x00007fffd2fdd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffd3a60000 - 0x00007fffd3a92000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fffb23b0000 - 0x00007fffb244d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\msvcp140.dll
0x00007fff781c0000 - 0x00007fff78c7d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server\jvm.dll
0x00007fffd5060000 - 0x00007fffd510e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffd4250000 - 0x00007fffd42ee000 	C:\WINDOWS\System32\sechost.dll
0x00007fffd4ba0000 - 0x00007fffd4cc0000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffd4490000 - 0x00007fffd4498000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fffcf660000 - 0x00007fffcf669000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fffc2de0000 - 0x00007fffc2e13000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffd4560000 - 0x00007fffd45cf000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffc3b20000 - 0x00007fffc3b2a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffd1660000 - 0x00007fffd1678000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffcb990000 - 0x00007fffcb9a1000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\verify.dll
0x00007fffcfd20000 - 0x00007fffcff41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffc3b30000 - 0x00007fffc3b61000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffd2a80000 - 0x00007fffd2aff000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffcb960000 - 0x00007fffcb989000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.dll
0x00007fffcb940000 - 0x00007fffcb958000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
0x00007fffcb8f0000 - 0x00007fffcb8fb000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/software/IntelliJ IDEA Community Edition 2020.1/plugins/git4idea/lib/git4idea-rt.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/xmlrpc-2.0.1.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/commons-codec-1.14.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/util.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 534773760                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8524922880                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\software\jdk-17.0.2\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=EDY
DISPLAY=:0.0
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.1335)
HyperV virtualization detected

CPU:total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 32512M (8417M free)
TotalPageFile size 43356M (AvailPageFile size 141M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 578M, peak: 578M

vm_info: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25) for windows-amd64 JRE (11.0.6+8-b765.25), built on Mar 30 2020 12:57:22 by "" with MS VC++ 14.0 (VS2015)

END.
