#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 534773760 bytes for Failed to commit area from 0x0000000603e00000 to 0x0000000623c00000 of length 534773760.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (c:/BuildAgent/work/1eae4dac1d648407/src/hotspot/os/windows/os_windows.cpp:3296), pid=56368, tid=5768
#
# JRE version:  (11.0.6+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 22621 (10.0.22621.2506)
Time: Fri Jan  5 12:39:20 2024 �й���׼ʱ�� elapsed time: 0 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000167fbade000):  JavaThread "Unknown thread" [_thread_in_vm, id=5768, stack(0x0000002fe3300000,0x0000002fe3400000)]

Stack: [0x0000002fe3300000,0x0000002fe3400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5de3ca]
V  [jvm.dll+0x710ae5]
V  [jvm.dll+0x712005]
V  [jvm.dll+0x7126b3]
V  [jvm.dll+0x23e708]
V  [jvm.dll+0x5db874]
V  [jvm.dll+0x5d0aa5]
V  [jvm.dll+0x2fb0fb]
V  [jvm.dll+0x2fb06a]
V  [jvm.dll+0x2faf42]
V  [jvm.dll+0x2ffe16]
V  [jvm.dll+0x348723]
V  [jvm.dll+0x348e26]
V  [jvm.dll+0x348823]
V  [jvm.dll+0x2d58a8]
V  [jvm.dll+0x2d6a47]
V  [jvm.dll+0x6ef917]
V  [jvm.dll+0x6f110c]
V  [jvm.dll+0x355d69]
V  [jvm.dll+0x6d4afe]
V  [jvm.dll+0x3bf3d3]
V  [jvm.dll+0x3c1921]
C  [jli.dll+0x5363]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000167f9fe5b50, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000167fbaf8800 GCTaskThread "GC Thread#0" [stack: 0x0000002fe3400000,0x0000002fe3500000] [id=55744]
  0x00000167fbb7a000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002fe3500000,0x0000002fe3600000] [id=48636]
  0x00000167fbb7b000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002fe3600000,0x0000002fe3700000] [id=55004]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffdc6fa6227]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000167fbadd830] Heap_lock - owner thread: 0x00000167fbade000

Heap address: 0x0000000603e00000, size: 8130 MB, Compressed Oops mode: Non-zero based: 0x0000000603e00000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (2 events):
Event: 0.010 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
Event: 0.010 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff723500000 - 0x00007ff72350a000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.exe
0x00007ffe823f0000 - 0x00007ffe82607000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe80e10000 - 0x00007ffe80ed4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe7f8b0000 - 0x00007ffe7fc56000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe7fe90000 - 0x00007ffe7ffa1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe13df0000 - 0x00007ffe13e07000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\VCRUNTIME140.dll
0x00007ffe14730000 - 0x00007ffe14749000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jli.dll
0x00007ffe81980000 - 0x00007ffe81b2e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe7f880000 - 0x00007ffe7f8a6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe80140000 - 0x00007ffe80169000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe7f760000 - 0x00007ffe7f878000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe800a0000 - 0x00007ffe8013a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe567e0000 - 0x00007ffe56a73000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100\COMCTL32.dll
0x00007ffe80170000 - 0x00007ffe80217000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe81c20000 - 0x00007ffe81c51000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000073190000 - 0x000000007319d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffe80290000 - 0x00007ffe80341000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe82300000 - 0x00007ffe823a5000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe81860000 - 0x00007ffe81977000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe6d260000 - 0x00007ffe6d30c000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffe803e0000 - 0x00007ffe80c3a000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe80220000 - 0x00007ffe8027e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffe7f0f0000 - 0x00007ffe7f0fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffde6ce0000 - 0x00007ffde6d7d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\msvcp140.dll
0x00007ffdc6cc0000 - 0x00007ffdc777d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server\jvm.dll
0x00007ffe80280000 - 0x00007ffe80288000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe6cdf0000 - 0x00007ffe6cdf9000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffe76c10000 - 0x00007ffe76c44000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe80d90000 - 0x00007ffe80e01000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe7e6d0000 - 0x00007ffe7e6e8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe70c60000 - 0x00007ffe70c71000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\verify.dll
0x00007ffe7cea0000 - 0x00007ffe7d0d3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe80ee0000 - 0x00007ffe81269000 	C:\WINDOWS\System32\combase.dll
0x00007ffe81680000 - 0x00007ffe81757000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe6f8b0000 - 0x00007ffe6f8e2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe80020000 - 0x00007ffe8009a000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe70ab0000 - 0x00007ffe70ad9000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.dll
0x00007ffe70b30000 - 0x00007ffe70b48000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
0x00007ffe70c50000 - 0x00007ffe70c5b000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100;C:\Program Files (x86)\360\360Safe\safemon;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/software/IntelliJ IDEA Community Edition 2020.1/plugins/git4idea/lib/git4idea-rt.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/xmlrpc-2.0.1.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/commons-codec-1.14.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/util.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 534773760                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8524922880                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI
USERNAME=EDY
DISPLAY=:0.0
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22621 (10.0.22621.2506)

CPU:total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 32512M (4057M free)
TotalPageFile size 42752M (AvailPageFile size 58M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 65M, peak: 575M

vm_info: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25) for windows-amd64 JRE (11.0.6+8-b765.25), built on Mar 30 2020 12:57:22 by "" with MS VC++ 14.0 (VS2015)

END.
