# 签到次数计算逻辑详细示例

## 计算规则说明

### 院外活动 (OUTSIDE_PCLASS)
- **小型活动**：独立计算签到次数
- **中型活动**：按活动类型（高端妈妈班/豪华妈妈班/精品妈妈班）分别独立计算
- **大型活动**：独立计算签到次数

### 院内活动 (INSIDE_PCLASS)
- 所有院内活动统一计算签到次数

### CS活动 (CS)
- 所有CS活动统一计算签到次数

## 具体示例

### 示例1：院外活动 - 小型活动和大型活动独立计算

**用户签到历史**：
1. 2024-01-15：院外小型高端妈妈班活动
2. 2024-01-20：院外小型豪华妈妈班活动

**当前签到**：院外大型高端妈妈班活动

**计算结果**：
- 大型活动签到次数 = 0（之前没有签到过大型活动）
- 本次签到为大型活动的第1次
- 返回提示语：「恭喜您首次签到大型高端妈妈班活动！」

### 示例2：院外活动 - 中型活动按活动类型分别计算

**用户签到历史**：
1. 2024-01-15：院外中型高端妈妈班活动
2. 2024-01-20：院外中型高端妈妈班活动
3. 2024-01-25：院外中型豪华妈妈班活动

**当前签到**：院外中型豪华妈妈班活动

**计算结果**：
- 中型豪华妈妈班签到次数 = 1（之前签到过1次中型豪华妈妈班）
- 本次签到为中型豪华妈妈班的第2次
- 返回提示语：「欢迎您再次参加中型豪华妈妈班活动！」

**注意**：虽然用户签到过2次中型高端妈妈班，但豪华妈妈班的签到次数是独立计算的。

### 示例3：院外活动 - 小型活动和中型活动独立计算

**用户签到历史**：
1. 2024-01-15：院外小型高端妈妈班活动
2. 2024-01-20：院外小型高端妈妈班活动
3. 2024-01-25：院外小型豪华妈妈班活动

**当前签到**：院外中型高端妈妈班活动

**计算结果**：
- 中型高端妈妈班签到次数 = 0（之前没有签到过中型活动）
- 本次签到为中型高端妈妈班的第1次
- 返回提示语：「恭喜您首次签到中型高端妈妈班活动！」

### 示例4：院内活动统一计算

**用户签到历史**：
1. 2024-01-15：院内高端妈妈班活动
2. 2024-01-20：院内豪华妈妈班活动

**当前签到**：院内精品妈妈班活动

**计算结果**：
- 院内活动签到次数 = 2（之前签到过2次院内活动）
- 本次签到为院内活动的第3次（按第2次及以上处理）
- 返回提示语：「欢迎您再次参加院内精品妈妈班活动！」

### 示例5：CS活动统一计算

**用户签到历史**：
1. 2024-01-15：CS活动
2. 2024-01-20：专家讲座

**当前签到**：健康咨询

**计算结果**：
- CS活动签到次数 = 2（之前签到过2次CS类活动）
- 本次签到为CS活动的第3次
- 返回提示语：「感谢您的持续参与健康咨询活动！」

### 示例6：跨类型活动独立计算

**用户签到历史**：
1. 2024-01-15：院外小型高端妈妈班活动
2. 2024-01-20：院内豪华妈妈班活动
3. 2024-01-25：CS活动

**当前签到**：院外中型高端妈妈班活动

**计算结果**：
- 中型高端妈妈班签到次数 = 0（院外、院内、CS活动的签到次数相互独立）
- 本次签到为中型高端妈妈班的第1次
- 返回提示语：「恭喜您首次签到中型高端妈妈班活动！」

## 代码实现逻辑

```java
private int calculateSpecificSignInCount(String signInType, String pclassType, String activityType, 
                                       List<SignInHistory> signInHistoryList) {
    if ("OUTSIDE_PCLASS".equals(signInType)) {
        if ("中型活动".equals(pclassType)) {
            // 中型活动：按活动类型分别计算
            return (int) signInHistoryList.stream()
                    .filter(history -> "OUTSIDE_PCLASS".equals(history.getChannel())
                            && "中型活动".equals(history.getPclassType())
                            && activityType.equals(history.getActivityType()))
                    .count();
        } else {
            // 小型活动、大型活动：按课程类型计算
            return (int) signInHistoryList.stream()
                    .filter(history -> "OUTSIDE_PCLASS".equals(history.getChannel())
                            && pclassType.equals(history.getPclassType()))
                    .count();
        }
    } else if ("INSIDE_PCLASS".equals(signInType)) {
        // 院内活动：统一计算
        return (int) signInHistoryList.stream()
                .filter(history -> "INSIDE_PCLASS".equals(history.getChannel()))
                .count();
    } else if ("CS".equals(signInType)) {
        // CS活动：统一计算
        return (int) signInHistoryList.stream()
                .filter(history -> "CS".equals(history.getChannel()))
                .count();
    }
    return 0;
}
```

## 测试验证

建议针对以上各种场景编写测试用例，确保签到次数计算的准确性：

1. **院外小型活动独立计算测试**
2. **院外中型活动按活动类型分别计算测试**
3. **院外大型活动独立计算测试**
4. **院内活动统一计算测试**
5. **CS活动统一计算测试**
6. **跨类型活动独立性测试**

通过这些测试用例，可以确保系统能够正确识别用户的签到历史，并返回准确的个性化提示语。
