#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 175536 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=39360, tid=4968
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Mon Dec 30 18:53:40 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.542905 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000018a31c604b0):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=4968, stack(0x0000003fe0800000,0x0000003fe0900000) (1024K)]


Current CompileTask:
C2:    542 1081       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

Stack: [0x0000003fe0800000,0x0000003fe0900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0x2fda81]
V  [jvm.dll+0x60acc9]
V  [jvm.dll+0x258b52]
V  [jvm.dll+0x2515ca]
V  [jvm.dll+0x24f03e]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000018a31bc7a40, length=16, elements={
0x0000018a10737180, 0x0000018a3132fbc0, 0x0000018a31356c40, 0x0000018a313808e0,
0x0000018a313b2790, 0x0000018a313b2e00, 0x0000018a313b3860, 0x0000018a313a9d90,
0x0000018a313b49e0, 0x0000018a31552b40, 0x0000018a315559e0, 0x0000018a31b66580,
0x0000018a31bc6270, 0x0000018a31bd82d0, 0x0000018a31d1e750, 0x0000018a31c604b0
}

Java Threads: ( => current thread )
  0x0000018a10737180 JavaThread "main"                              [_thread_blocked, id=39708, stack(0x0000003fdf100000,0x0000003fdf200000) (1024K)]
  0x0000018a3132fbc0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=23228, stack(0x0000003fdf900000,0x0000003fdfa00000) (1024K)]
  0x0000018a31356c40 JavaThread "Finalizer"                  daemon [_thread_blocked, id=28592, stack(0x0000003fdfa00000,0x0000003fdfb00000) (1024K)]
  0x0000018a313808e0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=20232, stack(0x0000003fdfb00000,0x0000003fdfc00000) (1024K)]
  0x0000018a313b2790 JavaThread "Attach Listener"            daemon [_thread_blocked, id=40424, stack(0x0000003fdfc00000,0x0000003fdfd00000) (1024K)]
  0x0000018a313b2e00 JavaThread "Service Thread"             daemon [_thread_blocked, id=29876, stack(0x0000003fdfd00000,0x0000003fdfe00000) (1024K)]
  0x0000018a313b3860 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=23512, stack(0x0000003fdfe00000,0x0000003fdff00000) (1024K)]
  0x0000018a313a9d90 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=16360, stack(0x0000003fdff00000,0x0000003fe0000000) (1024K)]
  0x0000018a313b49e0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=40748, stack(0x0000003fe0000000,0x0000003fe0100000) (1024K)]
  0x0000018a31552b40 JavaThread "Notification Thread"        daemon [_thread_blocked, id=35604, stack(0x0000003fe0100000,0x0000003fe0200000) (1024K)]
  0x0000018a315559e0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5948, stack(0x0000003fe0200000,0x0000003fe0300000) (1024K)]
  0x0000018a31b66580 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=36892, stack(0x0000003fe0300000,0x0000003fe0400000) (1024K)]
  0x0000018a31bc6270 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=27556, stack(0x0000003fe0500000,0x0000003fe0600000) (1024K)]
  0x0000018a31bd82d0 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=20124, stack(0x0000003fe0600000,0x0000003fe0700000) (1024K)]
  0x0000018a31d1e750 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=27172, stack(0x0000003fe0700000,0x0000003fe0800000) (1024K)]
=>0x0000018a31c604b0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=4968, stack(0x0000003fe0800000,0x0000003fe0900000) (1024K)]
Total: 16

Other Threads:
  0x0000018a2e78df40 VMThread "VM Thread"                           [id=14056, stack(0x0000003fdf800000,0x0000003fdf900000) (1024K)]
  0x0000018a312c0390 WatcherThread "VM Periodic Task Thread"        [id=34724, stack(0x0000003fdf700000,0x0000003fdf800000) (1024K)]
  0x0000018a2e5cf910 WorkerThread "GC Thread#0"                     [id=18212, stack(0x0000003fdf200000,0x0000003fdf300000) (1024K)]
  0x0000018a107af620 ConcurrentGCThread "G1 Main Marker"            [id=35348, stack(0x0000003fdf300000,0x0000003fdf400000) (1024K)]
  0x0000018a107b0080 WorkerThread "G1 Conc#0"                       [id=22184, stack(0x0000003fdf400000,0x0000003fdf500000) (1024K)]
  0x0000018a2e688660 ConcurrentGCThread "G1 Refine#0"               [id=40324, stack(0x0000003fdf500000,0x0000003fdf600000) (1024K)]
  0x0000018a2e689070 ConcurrentGCThread "G1 Service"                [id=37336, stack(0x0000003fdf600000,0x0000003fdf700000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0      584 1088       4       sun.security.ec.ECOperations::setDouble (463 bytes)
C1 CompilerThread0      584 1104       3       jdk.internal.org.objectweb.asm.MethodWriter::putMethodInfo (1274 bytes)
C2 CompilerThread1      584 1091       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
C2 CompilerThread2      584 1081       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffb1784748] Metaspace_lock - owner thread: 0x0000018a31bc6270

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 12288K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 17002K, committed 17216K, reserved 1114112K
  class space    used 1665K, committed 1792K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622fda478, 0x0000000623000000| 96%| E|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x0000018a25600000,0x0000018a265f0000] _byte_map_base: 0x0000018a225e2000

Marking Bits: (CMBitMap*) 0x0000018a1079ef30
 Bits: [0x0000018a265f0000, 0x0000018a2e500000)

Polling page: 0x0000018a0ee10000

Metaspace:

Usage:
  Non-class:     14.98 MB used.
      Class:      1.63 MB used.
       Both:     16.60 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.06 MB ( 24%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.75 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      16.81 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  882.00 KB
       Class:  14.20 MB
        Both:  15.06 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 224.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 269.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 399.
num_chunk_merges: 0.
num_chunk_splits: 224.
num_chunks_enlarged: 103.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=355Kb max_used=355Kb free=119644Kb
 bounds [0x0000018a1c460000, 0x0000018a1c6d0000, 0x0000018a23990000]
CodeHeap 'profiled nmethods': size=120000Kb used=1749Kb max_used=1749Kb free=118250Kb
 bounds [0x0000018a14990000, 0x0000018a14c00000, 0x0000018a1bec0000]
CodeHeap 'non-nmethods': size=5760Kb used=1431Kb max_used=1460Kb free=4328Kb
 bounds [0x0000018a1bec0000, 0x0000018a1c130000, 0x0000018a1c460000]
 total_blobs=1613 nmethods=1097 adapters=421
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.535 Thread 0x0000018a313b49e0 nmethod 1085 0x0000018a14b40990 code [0x0000018a14b40b40, 0x0000018a14b40c78]
Event: 0.535 Thread 0x0000018a313b49e0 1086       3       sun.security.util.math.intpoly.IntegerPolynomial$Element::<init> (40 bytes)
Event: 0.535 Thread 0x0000018a313b49e0 nmethod 1086 0x0000018a14b40d10 code [0x0000018a14b40ec0, 0x0000018a14b41218]
Event: 0.535 Thread 0x0000018a31d1e750 nmethod 1069 0x0000018a1c4b5d90 code [0x0000018a1c4b5f20, 0x0000018a1c4b6070]
Event: 0.535 Thread 0x0000018a31d1e750 1070       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSquare (52 bytes)
Event: 0.535 Thread 0x0000018a31d1e750 nmethod 1070 0x0000018a1c4b6190 code [0x0000018a1c4b6320, 0x0000018a1c4b6470]
Event: 0.535 Thread 0x0000018a31d1e750 1078       4       sun.security.util.math.intpoly.IntegerPolynomialP256::square (613 bytes)
Event: 0.536 Thread 0x0000018a313b49e0 1087       3       sun.security.util.ArrayUtil::swap (15 bytes)
Event: 0.536 Thread 0x0000018a313b49e0 nmethod 1087 0x0000018a14b41310 code [0x0000018a14b414a0, 0x0000018a14b415e8]
Event: 0.537 Thread 0x0000018a31c604b0 nmethod 1071 0x0000018a1c4b6590 code [0x0000018a1c4b6760, 0x0000018a1c4b6bf8]
Event: 0.537 Thread 0x0000018a31c604b0 1081       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
Event: 0.539 Thread 0x0000018a313a9d90 nmethod 1072 0x0000018a1c4b6f10 code [0x0000018a1c4b70e0, 0x0000018a1c4b7578]
Event: 0.539 Thread 0x0000018a313a9d90 1084       4       sun.security.util.math.intpoly.IntegerPolynomialP256::reduce (40 bytes)
Event: 0.539 Thread 0x0000018a31d1e750 nmethod 1078 0x0000018a1c4b7810 code [0x0000018a1c4b79a0, 0x0000018a1c4b7f30]
Event: 0.539 Thread 0x0000018a31d1e750 1010       4       sun.security.util.math.intpoly.IntegerPolynomial25519::carryReduce (639 bytes)
Event: 0.540 Thread 0x0000018a313a9d90 nmethod 1084 0x0000018a1c4b8090 code [0x0000018a1c4b8220, 0x0000018a1c4b8358]
Event: 0.540 Thread 0x0000018a313a9d90 1088       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Event: 0.540 Thread 0x0000018a313b49e0 1089       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (10 bytes)
Event: 0.540 Thread 0x0000018a313b49e0 nmethod 1089 0x0000018a14b41710 code [0x0000018a14b418c0, 0x0000018a14b41ac8]
Event: 0.540 Thread 0x0000018a313b49e0 1090       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (5 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.038 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.048 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.226 Thread 0x0000018a10737180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018a1c4718fc relative=0x000000000000007c
Event: 0.227 Thread 0x0000018a10737180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018a1c4718fc method=java.lang.CharacterDataLatin1.toLowerCase(I)I @ 16 c2
Event: 0.227 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a1c4718fc sp=0x0000003fdf1fb0c0
Event: 0.227 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf146a2 sp=0x0000003fdf1fb050 mode 2
Event: 0.290 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a149d7523 sp=0x0000003fdf1fde80
Event: 0.290 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf14e42 sp=0x0000003fdf1fd318 mode 0
Event: 0.291 Thread 0x0000018a10737180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018a1c47c2e8 relative=0x0000000000000588
Event: 0.291 Thread 0x0000018a10737180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018a1c47c2e8 method=java.lang.AbstractStringBuilder.inflateIfNeededFor(Ljava/lang/String;)V @ 14 c2
Event: 0.291 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a1c47c2e8 sp=0x0000003fdf1fdd90
Event: 0.291 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf146a2 sp=0x0000003fdf1fdc40 mode 2
Event: 0.295 Thread 0x0000018a10737180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018a1c47eeb0 relative=0x0000000000000090
Event: 0.295 Thread 0x0000018a10737180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018a1c47eeb0 method=jdk.internal.misc.Unsafe.convEndian(ZI)I @ 4 c2
Event: 0.295 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a1c47eeb0 sp=0x0000003fdf1fde80
Event: 0.295 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf146a2 sp=0x0000003fdf1fddb0 mode 2
Event: 0.356 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a149acef8 sp=0x0000003fdf1fc600
Event: 0.356 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf14e42 sp=0x0000003fdf1fba20 mode 0
Event: 0.444 Thread 0x0000018a10737180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018a1c498254 relative=0x0000000000000194
Event: 0.444 Thread 0x0000018a10737180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018a1c498254 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.444 Thread 0x0000018a10737180 DEOPT PACKING pc=0x0000018a1c498254 sp=0x0000003fdf1fef10
Event: 0.444 Thread 0x0000018a10737180 DEOPT UNPACKING pc=0x0000018a1bf146a2 sp=0x0000003fdf1feee0 mode 2

Classes loaded (20 events):
Event: 0.510 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry
Event: 0.510 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry done
Event: 0.510 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession
Event: 0.510 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession done
Event: 0.511 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384
Event: 0.511 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384 done
Event: 0.511 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521
Event: 0.512 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521 done
Event: 0.512 Loading class sun/security/util/math/intpoly/P384OrderField
Event: 0.512 Loading class sun/security/util/math/intpoly/P384OrderField done
Event: 0.512 Loading class sun/security/util/math/intpoly/P521OrderField
Event: 0.512 Loading class sun/security/util/math/intpoly/P521OrderField done
Event: 0.513 Loading class java/security/interfaces/ECPrivateKey
Event: 0.513 Loading class java/security/interfaces/ECPrivateKey done
Event: 0.513 Loading class sun/security/util/ArrayUtil
Event: 0.513 Loading class sun/security/util/ArrayUtil done
Event: 0.514 Loading class java/util/ImmutableCollections$Map1
Event: 0.514 Loading class java/util/ImmutableCollections$Map1 done
Event: 0.516 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 0.516 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.326 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623724ca0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623724ca0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.326 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623729048}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000623729048) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.326 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623730658}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623730658) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.327 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237370f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000006237370f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.327 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062373db80}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x000000062373db80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.328 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623742330}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623742330) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.331 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062376f110}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000062376f110) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.332 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062377e888}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062377e888) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.335 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b0530}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237b0530) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.335 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b6e00}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237b6e00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.335 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237ba220}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237ba220) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.436 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006231e32b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006231e32b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.455 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233dec08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233dec08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.456 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233e2570}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233e2570) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.460 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c10f30}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c10f30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.470 Thread 0x0000018a10737180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c92c58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c92c58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.470 Thread 0x0000018a31b66580 Exception <a 'java/lang/NoSuchMethodError'{0x000000062322d1a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x000000062322d1a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.476 Thread 0x0000018a31b66580 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623266ee0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000623266ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.477 Thread 0x0000018a31bc6270 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1ae58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1ae58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.481 Thread 0x0000018a31bc6270 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6cd10}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6cd10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (6 events):
Event: 0.144 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.144 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.152 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.152 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.279 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.279 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.089 Thread 0x0000018a10737180 Thread added: 0x0000018a31356c40
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313808e0
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313b2790
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313b2e00
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313b3860
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313a9d90
Event: 0.090 Thread 0x0000018a10737180 Thread added: 0x0000018a313b49e0
Event: 0.111 Thread 0x0000018a10737180 Thread added: 0x0000018a31552b40
Event: 0.115 Thread 0x0000018a10737180 Thread added: 0x0000018a315559e0
Event: 0.127 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.135 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.140 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.193 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.381 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.427 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.432 Thread 0x0000018a10737180 Thread added: 0x0000018a31b66580
Event: 0.475 Thread 0x0000018a31b66580 Thread added: 0x0000018a31bc6270
Event: 0.485 Thread 0x0000018a31b66580 Thread added: 0x0000018a31bd82d0
Event: 0.519 Thread 0x0000018a313b49e0 Thread added: 0x0000018a31d1e750
Event: 0.519 Thread 0x0000018a313b49e0 Thread added: 0x0000018a31c604b0


Dynamic libraries:
0x00007ff79d830000 - 0x00007ff79d83a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ff873250000 - 0x00007ff873467000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff872310000 - 0x00007ff8723d4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff870b60000 - 0x00007ff870f1a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff870780000 - 0x00007ff870891000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff849e90000 - 0x00007ff849eab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ff831b50000 - 0x00007ff831b68000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ff8718d0000 - 0x00007ff871a7e000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8709c0000 - 0x00007ff8709e6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff872e70000 - 0x00007ff872e99000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8708a0000 - 0x00007ff8709bb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff870520000 - 0x00007ff8705ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff833d90000 - 0x00007ff834022000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007ff871670000 - 0x00007ff871717000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff872dc0000 - 0x00007ff872df1000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000071990000 - 0x000000007199d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ff8715b0000 - 0x00007ff871662000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8717a0000 - 0x00007ff871847000 	C:\WINDOWS\System32\sechost.dll
0x00007ff870f20000 - 0x00007ff870f48000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff871390000 - 0x00007ff8714a4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff85e110000 - 0x00007ff85e213000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ff871a90000 - 0x00007ff872308000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff872eb0000 - 0x00007ff872f0e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ff86fe70000 - 0x00007ff86fe7a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff861130000 - 0x00007ff86113c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ff860ad0000 - 0x00007ff860b5d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007fffb0ac0000 - 0x00007fffb1877000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ff871720000 - 0x00007ff871791000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8703f0000 - 0x00007ff87043d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff861390000 - 0x00007ff8613c4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8703d0000 - 0x00007ff8703e3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff86f490000 - 0x00007ff86f4a8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff861110000 - 0x00007ff86111a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ff86dc50000 - 0x00007ff86de82000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff871000000 - 0x00007ff87138f000 	C:\WINDOWS\System32\combase.dll
0x00007ff872f10000 - 0x00007ff872fe7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff862940000 - 0x00007ff862972000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff870700000 - 0x00007ff87077b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff831a90000 - 0x00007ff831aaf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ff824760000 - 0x00007ff824778000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ff86e3c0000 - 0x00007ff86ecc8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff86e280000 - 0x00007ff86e3bf000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff872aa0000 - 0x00007ff872b9a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff870450000 - 0x00007ff87047b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff849e40000 - 0x00007ff849e50000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ff86aec0000 - 0x00007ff86aff6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff86f910000 - 0x00007ff86f979000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff80bd20000 - 0x00007ff80bd36000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ff86fb70000 - 0x00007ff86fb8b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff86f3f0000 - 0x00007ff86f425000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff86fa00000 - 0x00007ff86fa28000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff86fb60000 - 0x00007ff86fb6c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff86fe80000 - 0x00007ff86fead000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff871a80000 - 0x00007ff871a89000 	C:\WINDOWS\System32\NSI.dll
0x00007ff833400000 - 0x00007ff83340e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ff8709f0000 - 0x00007ff870b56000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff86fe30000 - 0x00007ff86fe5d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff86fdf0000 - 0x00007ff86fe27000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff8230f0000 - 0x00007ff8230f8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff8229a0000 - 0x00007ff8229a9000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 23, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 80988K (0% of 33293192K total physical memory with 5551760K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
