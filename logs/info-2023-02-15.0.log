2023-02-15 06:38:45.592[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2023-02-15 06:38:48.613[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2023-02-15 06:38:52.776[com.alibaba.nacos.client.Worker.longPolling.fixed-*************_8848-loyalty-activity-service] [com.alibaba.nacos.client.config.http.ServerHttpAgent:169] [ERROR] - [NACOS SocketTimeoutException httpPost] currentServerAddr: http://*************:8848， err : Read timed out
2023-02-15 06:38:52.778[com.alibaba.nacos.client.Worker.longPolling.fixed-*************_8848-loyalty-activity-service] [com.alibaba.nacos.client.config.http.ServerHttpAgent:189] [ERROR] - no available server, currentServerAddr : http://*************:8848
2023-02-15 06:38:52.779[com.alibaba.nacos.client.Worker.longPolling.fixed-*************_8848-loyalty-activity-service] [com.alibaba.nacos.client.config.impl.ClientWorker:454] [ERROR] - [fixed-*************_8848-loyalty-activity-service] [check-update] get changed dataId exception
java.net.ConnectException: no available server, currentServerAddr : http://*************:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:68)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:441)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:408)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:596)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2023-02-15 06:38:52.781[com.alibaba.nacos.client.Worker.longPolling.fixed-*************_8848-loyalty-activity-service] [com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable:640] [ERROR] - longPolling error : 
java.net.ConnectException: no available server, currentServerAddr : http://*************:8848
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpPost(ServerHttpAgent.java:190)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpPost(MetricsHttpAgent.java:68)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateConfigStr(ClientWorker.java:441)
	at com.alibaba.nacos.client.config.impl.ClientWorker.checkUpdateDataIds(ClientWorker.java:408)
	at com.alibaba.nacos.client.config.impl.ClientWorker$LongPollingRunnable.run(ClientWorker.java:596)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2023-02-15 09:56:24.335[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:88] [INFO] - Start to execute method=【/ncp/PNECClass/signIn/list/{activityId}】 with ip=【127.0.0.1】, params=【"arg0":23023,"arg1":SearchCriteria(criterias=[SearchCriteria.Criteria(key=id, value=1, minValue=string, maxValue=string)])】
2023-02-15 09:56:24.551[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:101] [INFO] - End to excecute method=【/ncp/PNECClass/signIn/list/{activityId}】 with exectime=【215】,ip=【127.0.0.1】, result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PageResponse(currentPageNumber=1, count=3, results=Page{count=true, pageNum=1, pageSize=20, startRow=0, endRow=20, total=3, pages=1, reasonable=false, pageSizeZero=false}[PClassSignInData(activityId=23023, classesCode=test-class-23023, pnec=80404, startTime=Fri Feb 03 12:00:00 CST 2023, endTime=Fri Feb 03 22:56:50 CST 2023, status=PLAYED, cellphone=132****2352, userName=测试, triggerTime=2023-02-03 21:58:50, classTime=2023-02-03 12:00-2023-02-03 22:56), PClassSignInData(activityId=23023, classesCode=test-class-23023, pnec=80404, startTime=Fri Feb 03 12:00:00 CST 2023, endTime=Fri Feb 03 22:56:50 CST 2023, status=PLAYED, cellphone=188****8770, userName=测试小潘, triggerTime=2023-02-03 21:50:47, classTime=2023-02-03 12:00-2023-02-03 22:56), PClassSignInData(activityId=23023, classesCode=test-class-23023, pnec=80404, startTime=Fri Feb 03 12:00:00 CST 2023, endTime=Fri Feb 03 22:56:50 CST 2023, status=PLAYED, cellphone=195****0223, userName=哈哈哈, triggerTime=2023-02-03 21:51:59, classTime=2023-02-03 12:00-2023-02-03 22:56)])}】
2023-02-15 10:48:01.538[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-02-15 10:48:01.538[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-02-15 10:48:01.538[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-02-15 10:48:01.539[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-02-15 10:48:01.826[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-02-15 10:48:01.837[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-02-15 10:48:01.838[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-02-15 10:48:01.838[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-02-15 10:48:01.887[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-02-15 10:48:01.888[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-02-15 10:48:02.527[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-02-15 10:48:02.527[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-02-15 10:48:05.544[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-02-15 10:48:08.589[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-02-15 10:48:08.590[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-02-15 10:48:08.591[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-02-15 10:48:08.592[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-02-15 10:48:08.592[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-02-15 10:48:08.593[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-02-15 10:48:08.593[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-02-15 10:48:08.594[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-02-15 10:48:08.594[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-02-15 10:48:08.594[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-02-15 10:48:08.595[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-02-15 10:48:08.596[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-02-15 10:48:08.640[SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2023-02-15 10:48:08.643[SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
2023-02-15 10:48:13.044[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-02-15 10:48:13.913[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-02-15 10:48:14.218[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-02-15 10:48:14.350[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-02-15 10:48:14.406[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-02-15 10:48:14.457[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-02-15 10:48:14.485[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-02-15 10:48:14.511[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-02-15 10:48:15.629[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-15 10:48:15.632[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-02-15 10:48:15.670[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.887[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.888[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.889[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.889[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.889[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.889[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.889[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.890[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.891[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.891[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.891[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.891[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.891[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:48:15.892[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-02-15 10:48:15.994[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=ac511a4a-3e86-3066-ad3e-89bc06043cf7
2023-02-15 10:48:16.015[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-02-15 10:48:16.054[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:48:16.054[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:48:16.055[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:48:16.075[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-02-15 10:48:16.076[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:48:16.076[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:48:16.076[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:48:16.076[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:48:16.076[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:48:16.077[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:48:16.077[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:48:16.077[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:48:16.077[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:48:16.133[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-02-15 10:48:16.540[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-02-15 10:48:16.542[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-02-15 10:48:16.770[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-02-15 10:48:16.780[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-02-15 10:48:16.780[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-02-15 10:48:16.781[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-02-15 10:48:16.890[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-02-15 10:48:16.890[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2364 ms
2023-02-15 10:48:17.069[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-02-15 10:48:17.402[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-02-15 10:48:17.411[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-02-15 10:48:17.411[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-02-15 10:48:17.412[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-02-15 10:48:17.412[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-02-15 10:48:17.412[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-02-15 10:48:17.412[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-02-15 10:48:17.413[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-02-15 10:48:17.414[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-02-15 10:48:20.022[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-02-15 10:48:20.883[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-02-15 10:48:20.884[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-02-15 10:48:20.885[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-02-15 10:48:21.382[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-15 10:48:21.702[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-02-15 10:48:21.702[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-02-15 10:48:21.702[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-02-15 10:48:21.702[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-02-15 10:48:21.703[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-02-15 10:48:21.703[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-02-15 10:48:21.703[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-02-15 10:48:21.703[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-02-15 10:48:21.729[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5946e540, org.springframework.security.web.context.SecurityContextPersistenceFilter@59d8a968, org.springframework.security.web.header.HeaderWriterFilter@20dfe8b2, org.springframework.security.web.authentication.logout.LogoutFilter@46e32574, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@6f45a2fd), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b42d1c8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6971c6b7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@651a399, org.springframework.security.web.session.SessionManagementFilter@5d9639bb, org.springframework.security.web.access.ExceptionTranslationFilter@81f9b01]
2023-02-15 10:48:21.743[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48a32c4f, org.springframework.security.web.context.SecurityContextPersistenceFilter@77c7d3f4, org.springframework.security.web.header.HeaderWriterFilter@d9ff1cd, org.springframework.security.web.authentication.logout.LogoutFilter@26c349fc, loyalty.activity.service.common.security.UserTokenFilter@25af2afe, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@162c89ac, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@644f033d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21a1554, org.springframework.security.web.session.SessionManagementFilter@2c64ee53, org.springframework.security.web.access.ExceptionTranslationFilter@8fd3eee, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@641001c2]
2023-02-15 10:48:21.936[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-02-15 10:48:22.086[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-02-15 10:48:22.495[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-02-15 10:48:23.284[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:48:23.289[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:48:23.292[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-02-15 10:48:23.315[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-02-15 10:48:23.740[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-02-15 10:48:23.741[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-02-15 10:48:23.794[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-02-15 10:48:24.805[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 12.698 seconds (JVM running for 13.24)
2023-02-15 10:48:24.836[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:48:24.837[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:48:24.838[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-uat.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:48:24.838[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:48:24.838[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:48:24.838[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:48:34.419[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:48:34.423[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:49:55.481[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-15 10:49:55.481[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-02-15 10:49:55.484[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 3 ms
2023-02-15 10:49:55.557[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:88] [INFO] - Start to execute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with ip=【127.0.0.1】, params=【"arg0":23023,"arg1":<EMAIL>】
2023-02-15 10:49:55.561[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:117] [ERROR] - Runtime exception occurred when excecute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with exectime=【18】, params=【"arg0":23023,"arg1":<EMAIL>】
2023-02-15 10:49:55.563[http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:28] [WARN] - Internal error occurred while execute /loyalty-activity-service/ncp/PNECClass/signIn/sendEmail/23023 with errorCode=37, msg=
2023-02-15 10:57:22.186[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-02-15 10:57:22.186[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-02-15 10:57:22.186[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-02-15 10:57:22.187[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-02-15 10:57:22.466[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-02-15 10:57:22.474[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-02-15 10:57:22.475[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-02-15 10:57:22.475[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-02-15 10:57:22.523[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-02-15 10:57:22.524[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-02-15 10:57:25.253[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-02-15 10:57:25.255[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-02-15 10:57:27.744[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:57:27.747[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-02-15 10:57:27.750[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-02-15 10:57:30.780[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-02-15 10:57:30.781[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-02-15 10:57:30.781[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-02-15 10:57:30.782[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-02-15 10:57:30.782[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-02-15 10:57:30.783[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-02-15 10:57:30.784[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-02-15 10:57:30.784[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-02-15 10:57:30.784[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-02-15 10:57:30.784[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-02-15 10:57:30.785[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-02-15 10:57:30.785[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-02-15 10:57:36.320[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-02-15 10:57:37.188[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-02-15 10:57:37.495[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-02-15 10:57:37.624[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-02-15 10:57:37.676[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-02-15 10:57:37.733[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-02-15 10:57:37.742[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-02-15 10:57:37.758[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-02-15 10:57:38.857[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-15 10:57:38.860[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-02-15 10:57:38.901[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-02-15 10:57:39.124[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.124[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.125[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.125[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.125[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.126[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.127[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.128[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.129[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.129[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-02-15 10:57:39.129[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-02-15 10:57:39.231[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=ac511a4a-3e86-3066-ad3e-89bc06043cf7
2023-02-15 10:57:39.252[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-02-15 10:57:39.293[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:57:39.294[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:57:39.294[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-02-15 10:57:39.314[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:57:39.315[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:57:39.316[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:57:39.316[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-02-15 10:57:39.316[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-02-15 10:57:39.364[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-02-15 10:57:39.794[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-02-15 10:57:39.796[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-02-15 10:57:40.023[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-02-15 10:57:40.033[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-02-15 10:57:40.033[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-02-15 10:57:40.033[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-02-15 10:57:40.149[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-02-15 10:57:40.150[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2376 ms
2023-02-15 10:57:40.323[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-02-15 10:57:40.651[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-02-15 10:57:40.660[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-02-15 10:57:40.661[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-02-15 10:57:40.661[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-02-15 10:57:40.662[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-02-15 10:57:40.662[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-02-15 10:57:40.662[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-02-15 10:57:40.663[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-02-15 10:57:40.663[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-02-15 10:57:43.279[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-02-15 10:57:44.111[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-02-15 10:57:44.112[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-02-15 10:57:44.113[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-02-15 10:57:44.637[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-15 10:57:44.973[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-02-15 10:57:44.973[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-02-15 10:57:44.973[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-02-15 10:57:44.973[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-02-15 10:57:44.974[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-02-15 10:57:44.974[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-02-15 10:57:44.974[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-02-15 10:57:44.974[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-02-15 10:57:44.998[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1c12f0f4, org.springframework.security.web.context.SecurityContextPersistenceFilter@3e455d01, org.springframework.security.web.header.HeaderWriterFilter@24df4805, org.springframework.security.web.authentication.logout.LogoutFilter@618d2191, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@4707d60a), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1314ccc1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43851574, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@149bdb4e, org.springframework.security.web.session.SessionManagementFilter@768d8a25, org.springframework.security.web.access.ExceptionTranslationFilter@676f70dc]
2023-02-15 10:57:45.012[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5680f009, org.springframework.security.web.context.SecurityContextPersistenceFilter@755c29b0, org.springframework.security.web.header.HeaderWriterFilter@81198ed, org.springframework.security.web.authentication.logout.LogoutFilter@32fb4a87, loyalty.activity.service.common.security.UserTokenFilter@3a4e6da6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3dc70aa0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@39153b05, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@73e0c775, org.springframework.security.web.session.SessionManagementFilter@2806fe58, org.springframework.security.web.access.ExceptionTranslationFilter@42e14655, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4dbeae45]
2023-02-15 10:57:45.212[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-02-15 10:57:45.357[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-02-15 10:57:45.752[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-02-15 10:57:46.525[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-02-15 10:57:46.532[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-02-15 10:57:46.534[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-02-15 10:57:46.554[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-02-15 10:57:46.965[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-02-15 10:57:46.966[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-02-15 10:57:47.024[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-02-15 10:57:47.582[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-02-15 10:57:47.583[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-02-15 10:57:47.979[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 12.623 seconds (JVM running for 13.209)
2023-02-15 10:57:48.007[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:57:48.008[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:57:48.009[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-uat.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:57:48.009[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:57:48.009[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-02-15 10:57:48.009[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-02-15 10:58:21.529[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-15 10:58:21.530[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-02-15 10:58:21.531[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-02-15 10:58:21.591[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:88] [INFO] - Start to execute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with ip=【127.0.0.1】, params=【"arg0":23023,"arg1":<EMAIL>】
2023-02-15 10:58:27.961[http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2023-02-15 10:58:28.630[http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2023-02-15 10:58:29.355[http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.MailService:31] [INFO] - Start to <NAME_EMAIL> for params=[{"classCode":"test-class-23023","description":"课程编码【test-class-23023】签到明细如邮件附件，如有疑问，请联系（Caitlin）<EMAIL>。","attachment":{"path":"d:\\test-class-23023签到明细**************.xlsx"}}]
2023-02-15 10:58:34.246[http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.MailService:40] [INFO] - end to sending email with account = <EMAIL> 
2023-02-15 10:58:34.249[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:101] [INFO] - End to excecute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with exectime=【12670】,ip=【127.0.0.1】, result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2023-02-15 11:04:02.390[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:88] [INFO] - Start to execute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with ip=【127.0.0.1】, params=【"arg0":23023,"arg1":jimmy_pansinobasedm.com】
2023-02-15 11:04:02.391[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:117] [ERROR] - Runtime exception occurred when excecute method=【/ncp/PNECClass/signIn/sendEmail/{activityId}】 with exectime=【1】, params=【"arg0":23023,"arg1":jimmy_pansinobasedm.com】
2023-02-15 11:04:02.394[http-nio-9072-exec-3] [loyalty.activity.service.common.controller.BaseExceptionController:28] [WARN] - Internal error occurred while execute /loyalty-activity-service/ncp/PNECClass/signIn/sendEmail/23023 with errorCode=37, msg=
