2023-11-02 17:21:20.284[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-11-02 17:21:21.361[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-11-02 17:21:21.768[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-11-02 17:21:22.802[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:23.815[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:24.822[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:24.822[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-11-02 17:21:24.826[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-11-02 17:21:24.827[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-11-02 17:21:24.828[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-11-02 17:21:24.829[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-11-02 17:21:25.843[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:26.854[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:27.865[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:27.867[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-11-02 17:21:27.868[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:144)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-11-02 17:21:27.870[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-11-02 17:21:27.871[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-11-02 17:21:27.872[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-11-02 17:21:28.880[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:29.894[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:30.902[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-11-02 17:21:30.902[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-11-02 17:21:30.903[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:149)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-11-02 17:21:30.903[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-11-02 17:21:30.903[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-11-02 17:21:30.905[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-11-02 17:21:30.919[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-11-02 17:21:30.955[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-11-02 17:21:31.484[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-11-02 17:21:31.485[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-11-02 17:21:32.455[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-11-02 17:21:32.458[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-11-02 17:21:32.505[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.788[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.789[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.790[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.791[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.792[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.792[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.792[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-11-02 17:21:32.792[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-11-02 17:21:32.906[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=f2196d4f-5c56-3ee1-8a46-5644badf325e
2023-11-02 17:21:32.936[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-11-02 17:21:32.973[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-11-02 17:21:32.973[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-11-02 17:21:32.973[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-11-02 17:21:32.992[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-11-02 17:21:32.993[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-11-02 17:21:32.994[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-11-02 17:21:32.994[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-11-02 17:21:33.041[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-11-02 17:21:33.504[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.509[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.513[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.517[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.562[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.565[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.568[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-02 17:21:33.639[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-11-02 17:21:33.642[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-11-02 17:21:33.902[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-11-02 17:21:34.061[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3086 ms
2023-11-02 17:21:34.259[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-11-02 17:21:34.641[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-11-02 17:21:34.649[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-11-02 17:21:34.651[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-11-02 17:21:34.651[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-11-02 17:21:34.652[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-11-02 17:21:34.652[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-11-02 17:21:34.652[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-11-02 17:21:34.653[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-11-02 17:21:34.654[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-11-02 17:21:37.994[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-11-02 17:21:39.032[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-11-02 17:21:39.033[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-11-02 17:21:39.034[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-11-02 17:21:39.057[][main] [org.springframework.context.support.AbstractApplicationContext:596] [WARN] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'documentationPluginsBootstrapper' defined in URL [jar:file:/D:/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/D:/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'webEndpointServletHandlerMapping' defined in class path resource [org/springframework/boot/actuate/autoconfigure/endpoint/web/servlet/WebMvcEndpointManagementContextConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping]: Factory method 'webEndpointServletHandlerMapping' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'healthEndpoint' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Unsatisfied dependency expressed through method 'healthEndpoint' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'healthContributorRegistry' defined in class path resource [org/springframework/boot/actuate/autoconfigure/health/HealthEndpointConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthContributorRegistry]: Factory method 'healthContributorRegistry' threw exception; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'nacosDiscoveryHealthIndicator' defined in class path resource [com/alibaba/cloud/nacos/endpoint/NacosDiscoveryEndpointAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.boot.actuate.health.HealthIndicator]: Factory method 'nacosDiscoveryHealthIndicator' threw exception; nested exception is java.lang.RuntimeException: ErrCode:-400, ErrMsg:Shutdown in progress
