2023-04-18 11:03:29.517[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-18 11:03:29.574[background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-18 11:03:29.983[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-18 11:03:29.983[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-18 11:03:29.983[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1681787009982
2023-04-18 11:03:30.658[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-18 11:03:30.659[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-18 11:03:30.664[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-18 11:03:30.665[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-18 11:03:30.665[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1681787010664
2023-04-18 11:03:30.698[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-18 11:03:30.819[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-18 11:03:30.824[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-18 11:03:30.894[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-18 11:03:30.947[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-18 11:03:30.955[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-18 11:03:31.089[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-04-18 11:03:31.090[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-18 11:03:31.094[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-18 11:03:31.094[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-18 11:03:31.094[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1681787011094
2023-04-18 11:03:31.254[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-18 11:03:32.237[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-18 11:03:32.241[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-18 11:03:32.297[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2023-04-18 11:03:32.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.488[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.488[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.488[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.489[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.489[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.489[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.489[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.490[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.490[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.490[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.491[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.491[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.491[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.491[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.492[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.492[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.492[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.492[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.493[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.493[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.493[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.494[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.494[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-18 11:03:32.494[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-18 11:03:32.597[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=4ae7a84e-b430-3e03-9e97-90e75eec041a
2023-04-18 11:03:32.618[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-18 11:03:32.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-18 11:03:32.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-18 11:03:32.653[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-18 11:03:32.675[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-18 11:03:32.676[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-18 11:03:32.678[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-18 11:03:32.717[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-18 11:03:33.154[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-18 11:03:33.157[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-18 11:03:33.409[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-18 11:03:33.421[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-18 11:03:33.422[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-18 11:03:33.423[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-18 11:03:33.571[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-18 11:03:33.571[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2459 ms
2023-04-18 11:03:33.850[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-18 11:03:34.189[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-18 11:03:34.197[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-18 11:03:34.198[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-18 11:03:34.198[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-18 11:03:34.199[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-18 11:03:34.199[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-18 11:03:34.199[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-18 11:03:34.200[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-18 11:03:34.201[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-18 11:03:37.047[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-18 11:03:37.869[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-18 11:03:37.870[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-18 11:03:37.871[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-18 11:03:38.433[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-18 11:03:38.882[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-18 11:03:38.882[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-18 11:03:38.882[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-18 11:03:38.883[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-18 11:03:38.883[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-18 11:03:38.883[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-18 11:03:38.883[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-18 11:03:38.883[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-18 11:03:38.911[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12ec5e73, org.springframework.security.web.context.SecurityContextPersistenceFilter@567412db, org.springframework.security.web.header.HeaderWriterFilter@2fedf25b, org.springframework.security.web.authentication.logout.LogoutFilter@2b55ac77, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@495c4ce7), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@34dc40d5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@466a5b80, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@145c6e4d, org.springframework.security.web.session.SessionManagementFilter@582b6362, org.springframework.security.web.access.ExceptionTranslationFilter@582ce329]
2023-04-18 11:03:38.925[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19d75d3c, org.springframework.security.web.context.SecurityContextPersistenceFilter@42130a5c, org.springframework.security.web.header.HeaderWriterFilter@4c343088, org.springframework.security.web.authentication.logout.LogoutFilter@6f54d397, loyalty.activity.service.common.security.UserTokenFilter@5c3cc103, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e925a46, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34f876c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5af08232, org.springframework.security.web.session.SessionManagementFilter@504ccf42, org.springframework.security.web.access.ExceptionTranslationFilter@cc41d2d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5ded2dfc]
2023-04-18 11:03:39.123[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-18 11:03:39.274[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-18 11:03:39.789[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-18 11:03:40.599[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-18 11:03:40.611[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-18 11:03:41.028[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-18 11:03:41.030[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-18 11:03:41.083[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-18 11:03:41.671[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-18 11:03:41.714[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-18 11:03:41.971[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.698 seconds (JVM running for 14.345)
2023-04-18 11:03:41.981[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-18 11:03:41.982[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-18 11:03:41.983[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-uat.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-18 11:03:41.984[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-18 11:03:41.984[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-18 11:03:41.984[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-18 11:19:02.582[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-18 11:19:02.582[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-18 11:19:02.586[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 3 ms
2023-04-18 11:19:25.210[http-nio-9072-exec-5] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/researchDetail】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":ade896340f594cc69701491db3bf0833】
2023-04-18 11:19:25.214[http-nio-9072-exec-5] [loyalty.activity.service.pnec.service.PNECClassService:220] [INFO] - userId = null
2023-04-18 11:19:25.237[http-nio-9072-exec-5] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2023-04-18 11:19:25.973[http-nio-9072-exec-5] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2023-04-18 11:19:25.989[http-nio-9072-exec-5] [org.apache.ibatis.logging.jdbc.BaseJdbcLogger:137] [DEBUG] - ==>  Preparing: SELECT t1.id, t1.hcp_id hcpId, t1.hcp_name hcpName, t1.research_id researchId, t1.research_institution_name researchInstitutionName, t1.qrcode_url qrcodeUrl, t1.pnr_id pnrId, t1.pnr_name pnrName, t1.pnr_mobile pnrMobile, DATE_FORMAT(now(),'%Y-%m') `month`, t2.count FROM pclass_research t1 LEFT JOIN pclass_research_statistics t2 ON t1.research_id = t2.research_id WHERE t1.research_id = ? AND t2.month = DATE_FORMAT(now(),'%Y%c') AND t2.statistics_type = 'SURVEY_RECORD'
2023-04-18 11:19:26.014[http-nio-9072-exec-5] [org.apache.ibatis.logging.jdbc.BaseJdbcLogger:137] [DEBUG] - ==> Parameters: ade896340f594cc69701491db3bf0833(String)
2023-04-18 11:19:26.103[http-nio-9072-exec-5] [org.apache.ibatis.logging.jdbc.BaseJdbcLogger:137] [DEBUG] - <==      Total: 1
2023-04-18 11:19:26.114[http-nio-9072-exec-5] [loyalty.activity.service.sharelib.rest.client.AbstractRestClient:79] [INFO] - sending to url http://uat-loyalty-mall.icyanstone.com/mall/tm/pgCep/getRecruitmentCount?pnecId={pnecId}&surveyId={surveyId} with param {surveyId=ade896340f594cc69701491db3bf0833, pnecId=null}
2023-04-18 11:19:26.871[http-nio-9072-exec-5] [loyalty.activity.service.sharelib.rest.client.AbstractRestClient:88] [INFO] - response for url http://uat-loyalty-mall.icyanstone.com/mall/tm/pgCep/getRecruitmentCount?pnecId={pnecId}&surveyId={surveyId} is {"status":200,"headers":{"Server":["nginx/1.18.0 (Ubuntu)"],"Date":["Tue, 18 Apr 2023 03:19:26 GMT"],"Content-Type":["application/json"],"Transfer-Encoding":["chunked"],"Connection":["keep-alive"],"X-Application-Context":["mall-internal-gateway:uat:9069"]},"body":{"code":1,"message":"操作成功","body":0}}
2023-04-18 11:19:29.760[http-nio-9072-exec-5] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/cby/researchDetail】 with exectime=【4565】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PclassResearchInfoDto(hcpId=2365d026663b4135b93fdee01f3dd9cb, hcpName=范琦慧, researchId=ade896340f594cc69701491db3bf0833, researchInstitutionName=明州人民医院, month=2023-04, qrcodeUrl=https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQGc8TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyQjhFQ3AtNWtjY2sxMDAwMGcwN20AAgQRwb1iAwQAAAAA, pnrId=5007244, pnrName=马俊, pnrMobile=13736048460, count=0, recruitmentCount=0)}】
2023-04-18 11:28:24.464[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-18 11:28:24.463[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-18 11:28:24.464[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-18 11:28:24.465[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-18 11:28:24.762[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-18 11:28:24.771[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-18 11:28:24.771[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-18 11:28:24.772[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-18 11:28:24.823[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-18 11:28:24.824[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-18 11:28:26.374[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-18 11:28:26.375[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-18 11:28:29.403[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-18 11:28:32.422[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-18 11:28:32.423[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-18 11:28:32.424[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-18 11:28:32.424[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-18 11:28:32.424[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-18 11:28:32.424[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-18 11:28:32.425[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-18 11:28:32.426[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-18 11:28:32.426[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-18 11:28:32.426[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-18 11:28:32.426[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-18 11:28:32.427[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-18 11:28:32.450[SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2023-04-18 11:28:32.457[SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
