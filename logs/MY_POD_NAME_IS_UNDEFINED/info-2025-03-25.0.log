2025-03-25 09:43:44.977[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-03-25 09:43:45.023[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2025-03-25 09:43:45.029[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
2025-03-25 09:44:06.630[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-03-25 09:44:06.665[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-03-25 09:44:07.297[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2025-03-25 09:44:07.298[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2025-03-25 09:44:07.298[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1742867047295
2025-03-25 09:44:08.293[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2025-03-25 09:44:08.294[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-03-25 09:44:08.299[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2025-03-25 09:44:08.300[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2025-03-25 09:44:08.300[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1742867048299
2025-03-25 09:44:08.445[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2025-03-25 09:44:09.838[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-25 09:44:09.864[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 09:44:09.922[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-03-25 09:44:10.253[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.254[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.254[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.255[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.255[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.255[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.255[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.255[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.256[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.256[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.256[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.256[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.257[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.257[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.258[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.258[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.258[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.259[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.259[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.259[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.259[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.260[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.260[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.260[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.260[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.260[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.261[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.262[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.262[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.263[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-03-25 09:44:10.264[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-03-25 09:44:10.391[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=ef392cec-6f82-36b4-9223-21c5518a946b
2025-03-25 09:44:10.416[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-03-25 09:44:10.489[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-03-25 09:44:10.490[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-03-25 09:44:10.491[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-03-25 09:44:10.492[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.492[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.494[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-03-25 09:44:10.495[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.495[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.496[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.496[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.497[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.497[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.498[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-03-25 09:44:10.498[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-03-25 09:44:10.577[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-03-25 09:44:11.039[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.044[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.048[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.052[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.094[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.096[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.098[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 09:44:11.190[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-03-25 09:44:11.194[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-03-25 09:44:11.505[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-03-25 09:44:11.516[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-03-25 09:44:11.517[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-03-25 09:44:11.517[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-03-25 09:44:11.760[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-03-25 09:44:11.761[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3440 ms
2025-03-25 09:44:12.015[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-03-25 09:44:12.534[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-03-25 09:44:12.544[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-03-25 09:44:12.544[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-03-25 09:44:12.545[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-03-25 09:44:12.545[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-03-25 09:44:12.545[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-03-25 09:44:12.545[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-03-25 09:44:12.546[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-03-25 09:44:12.547[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-03-25 09:44:16.245[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-03-25 09:44:17.612[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-25 09:44:18.082[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-03-25 09:44:18.082[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-03-25 09:44:18.083[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-03-25 09:44:18.125[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a72b2c9, org.springframework.security.web.context.SecurityContextPersistenceFilter@4e7d77ec, org.springframework.security.web.header.HeaderWriterFilter@761d679f, org.springframework.security.web.authentication.logout.LogoutFilter@8829ecd, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@61a369cf), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@53115ee0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4540aade, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@269a37bc, org.springframework.security.web.session.SessionManagementFilter@4d7a4317, org.springframework.security.web.access.ExceptionTranslationFilter@2c78771b]
2025-03-25 09:44:18.149[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56ed9270, org.springframework.security.web.context.SecurityContextPersistenceFilter@18d76c14, org.springframework.security.web.header.HeaderWriterFilter@6fc9c0cc, org.springframework.security.web.authentication.logout.LogoutFilter@76629f82, loyalty.activity.service.common.security.UserTokenFilter@551fefe8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@517f9ba3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7011eee0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6affa355, org.springframework.security.web.session.SessionManagementFilter@21a09c5a, org.springframework.security.web.access.ExceptionTranslationFilter@bc85538, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a1143cc]
2025-03-25 09:44:18.386[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-03-25 09:44:19.061[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-03-25 09:44:20.081[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-03-25 09:44:20.200[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-03-25 09:44:20.218[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-03-25 09:44:22.696[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 17.552 seconds (JVM running for 18.636)
2025-03-25 09:44:27.697[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 09:44:27.698[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-03-25 09:44:27.700[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2025-03-25 09:44:30.298[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-03-25 09:44:41.993[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-03-25 09:44:48.662[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:98] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":525,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"活动已变更\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-03-25 09:45:08.771[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:117] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已变更"
    }
  }
}
2025-03-25 09:45:10.491[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-03-25 09:45:10.707[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Tue, 25 Mar 2025 01:45:10 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_avYlj9-b2-qs6-byOmfA7wDrFkN-D1or-RU8X8BnNMWep06oY4X08e0sn-hwcAbjerrhRJuDU_zK_7Qdi3v9QOyGyquUF8in6B6-VvUwxxJHgv2PJbe1IaB7PL8JNVjADAPTB"}
2025-03-25 09:45:10.709[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_avYlj9-b2-qs6-byOmfA7wDrFkN-D1or-RU8X8BnNMWep06oY4X08e0sn-hwcAbjerrhRJuDU_zK_7Qdi3v9QOyGyquUF8in6B6-VvUwxxJHgv2PJbe1IaB7PL8JNVjADAPTB】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已变更"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-03-25 09:45:11.533[1d46ad5c97b1485fa3c0c974fcf50766][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67e20aa7-2661b90a-069d5407"}】
2025-03-25 10:24:36.000[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-03-25 10:24:36.029[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2025-03-25 10:24:36.034[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
