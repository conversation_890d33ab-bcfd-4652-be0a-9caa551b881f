2024-06-19 14:19:55.902[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 14:19:55.970[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 14:19:56.514[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 14:19:56.514[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 14:19:56.515[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718777996513
2024-06-19 14:19:57.394[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 14:19:57.394[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 14:19:57.400[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 14:19:57.401[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 14:19:57.401[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718777997400
2024-06-19 14:19:57.823[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 14:19:58.830[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 14:19:58.846[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 14:19:58.894[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2024-06-19 14:19:59.162[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.163[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.164[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.164[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.164[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.165[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.165[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.166[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.166[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.166[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.167[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.167[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.167[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.168[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.168[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.168[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.169[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.169[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.169[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.169[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.170[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.170[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.170[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.171[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.171[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.171[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.172[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.172[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.172[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.173[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.173[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.173[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.174[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 14:19:59.175[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 14:19:59.282[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 14:19:59.314[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 14:19:59.379[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 14:19:59.380[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 14:19:59.381[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 14:19:59.381[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.381[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.381[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 14:19:59.382[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.382[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.382[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.383[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.383[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.383[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 14:19:59.384[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 14:19:59.429[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 14:19:59.829[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.832[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.836[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.839[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.880[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.882[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.884[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 14:19:59.957[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 14:19:59.959[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 14:20:00.230[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 14:20:00.242[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 14:20:00.243[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 14:20:00.243[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 14:20:00.403[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 14:20:00.403[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2984 ms
2024-06-19 14:20:00.586[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 14:20:01.042[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 14:20:01.051[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 14:20:01.052[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 14:20:01.052[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 14:20:01.052[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 14:20:01.053[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 14:20:01.053[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 14:20:01.054[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 14:20:01.054[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 14:20:04.225[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 14:20:05.277[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 14:20:05.698[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 14:20:05.699[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 14:20:05.699[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 14:20:05.699[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 14:20:05.699[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 14:20:05.700[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 14:20:05.700[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 14:20:05.700[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 14:20:05.729[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56929ce, org.springframework.security.web.context.SecurityContextPersistenceFilter@713323a3, org.springframework.security.web.header.HeaderWriterFilter@34c21c34, org.springframework.security.web.authentication.logout.LogoutFilter@3868c92e, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@70ba01be), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2083bcfd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4821e1a3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7bb6a1f4, org.springframework.security.web.session.SessionManagementFilter@52db9036, org.springframework.security.web.access.ExceptionTranslationFilter@796434a3]
2024-06-19 14:20:05.743[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59ffb0af, org.springframework.security.web.context.SecurityContextPersistenceFilter@2b8106f, org.springframework.security.web.header.HeaderWriterFilter@15974f4e, org.springframework.security.web.authentication.logout.LogoutFilter@161974d1, loyalty.activity.service.common.security.UserTokenFilter@d6d88b1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4b391e45, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@647bd553, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1268ac94, org.springframework.security.web.session.SessionManagementFilter@38911759, org.springframework.security.web.access.ExceptionTranslationFilter@25bd8eab, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b61a646]
2024-06-19 14:20:05.932[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 14:20:06.481[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 14:20:07.308[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 14:20:07.369[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 14:20:07.386[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 14:20:08.964[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 14.42 seconds (JVM running for 15.133)
2024-06-19 14:21:48.018[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 14:21:48.018[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 14:21:48.020[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2024-06-19 14:21:48.512[c92902eb24384268b3e02e495cc75dd1][http-nio-9072-exec-1] [io.swagger.models.parameters.AbstractSerializableParameter:421] [WARN] - Illegal DefaultValue null for parameter type number
java.lang.NumberFormatException: empty String
	at sun.misc.FloatingDecimal.readJavaFormatString(FloatingDecimal.java:1842)
	at sun.misc.FloatingDecimal.parseDouble(FloatingDecimal.java:110)
	at java.lang.Double.parseDouble(Double.java:538)
	at java.lang.Double.valueOf(Double.java:502)
	at io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:414)
	at sun.reflect.GeneratedMethodAccessor109.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:119)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeOptionalFields(MapSerializer.java:786)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeWithoutTypeInfo(MapSerializer.java:677)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:637)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:33)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:480)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:319)
	at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4409)
	at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsString(ObjectMapper.java:3663)
	at springfox.documentation.spring.web.json.JsonSerializer.toJson(JsonSerializer.java:38)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc.getDocumentation(Swagger2ControllerWebMvc.java:106)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc$$FastClassBySpringCGLIB$$c7040fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc$$EnhancerBySpringCGLIB$$b5865f9f.getDocumentation(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:204)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 14:21:48.514[c92902eb24384268b3e02e495cc75dd1][http-nio-9072-exec-1] [io.swagger.models.parameters.AbstractSerializableParameter:421] [WARN] - Illegal DefaultValue null for parameter type number
java.lang.NumberFormatException: empty String
	at sun.misc.FloatingDecimal.readJavaFormatString(FloatingDecimal.java:1842)
	at sun.misc.FloatingDecimal.parseDouble(FloatingDecimal.java:110)
	at java.lang.Double.parseDouble(Double.java:538)
	at java.lang.Double.valueOf(Double.java:502)
	at io.swagger.models.parameters.AbstractSerializableParameter.getExample(AbstractSerializableParameter.java:414)
	at sun.reflect.GeneratedMethodAccessor109.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:689)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:119)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeOptionalFields(MapSerializer.java:786)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeWithoutTypeInfo(MapSerializer.java:677)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:637)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:33)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:755)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:480)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:319)
	at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4409)
	at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsString(ObjectMapper.java:3663)
	at springfox.documentation.spring.web.json.JsonSerializer.toJson(JsonSerializer.java:38)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc.getDocumentation(Swagger2ControllerWebMvc.java:106)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc$$FastClassBySpringCGLIB$$c7040fea.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at springfox.documentation.swagger2.web.Swagger2ControllerWebMvc$$EnhancerBySpringCGLIB$$b5865f9f.getDocumentation(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:204)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 14:24:26.961[dbcf8db88a034c90835faf6c7efbae6e][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassById】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":NOChenan2024061900001】
2024-06-19 14:24:26.995[dbcf8db88a034c90835faf6c7efbae6e][http-nio-9072-exec-4] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2024-06-19 14:24:39.081[dbcf8db88a034c90835faf6c7efbae6e][http-nio-9072-exec-4] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2024-06-19 14:24:39.210[dbcf8db88a034c90835faf6c7efbae6e][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/pclass/info/getPclassById】 with user=【-1】, exectime=【12264】, params=【"arg0":NOChenan2024061900001】
2024-06-19 14:24:39.212[dbcf8db88a034c90835faf6c7efbae6e][http-nio-9072-exec-4] [loyalty.activity.service.common.controller.BaseExceptionController:28] [WARN] - Internal error occurred while execute /loyalty-activity-service/pclass/info/getPclassById with errorCode=16, msg=
2024-06-19 14:25:45.617[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 14:25:45.641[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2024-06-19 14:25:45.649[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
2024-06-19 15:51:12.585[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 15:51:12.673[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 15:51:13.446[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 15:51:13.447[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 15:51:13.447[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718783473444
2024-06-19 15:51:14.377[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 15:51:14.378[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 15:51:14.384[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 15:51:14.384[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 15:51:14.384[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718783474384
2024-06-19 15:51:14.586[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 15:51:16.207[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 15:51:16.226[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 15:51:16.302[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2024-06-19 15:51:16.604[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.606[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.607[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.607[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.607[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.607[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.607[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.608[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.608[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.608[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.608[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.609[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.609[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.609[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.609[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.610[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.610[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.610[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.610[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.611[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.611[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.611[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.612[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.612[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.613[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.613[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.613[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.613[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.614[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.614[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.615[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.615[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.615[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 15:51:16.616[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 15:51:16.754[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 15:51:16.779[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 15:51:16.850[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 15:51:16.851[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 15:51:16.851[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 15:51:16.852[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.852[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.852[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 15:51:16.853[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.853[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.853[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.854[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.854[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.854[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 15:51:16.855[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 15:51:16.903[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 15:51:17.267[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.270[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.274[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.277[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.316[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.320[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.322[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 15:51:17.397[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 15:51:17.399[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 15:51:17.685[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 15:51:17.696[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 15:51:17.697[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 15:51:17.697[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 15:51:17.834[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 15:51:17.835[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3433 ms
2024-06-19 15:51:18.015[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 15:51:18.444[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 15:51:18.458[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 15:51:18.459[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 15:51:18.459[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 15:51:18.460[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 15:51:18.460[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 15:51:18.461[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 15:51:18.462[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 15:51:18.462[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 15:51:22.051[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 15:51:23.077[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 15:51:23.545[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 15:51:23.546[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 15:51:23.546[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 15:51:23.546[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 15:51:23.547[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 15:51:23.547[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 15:51:23.547[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 15:51:23.547[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 15:51:23.577[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@318d69ea, org.springframework.security.web.context.SecurityContextPersistenceFilter@38f704f4, org.springframework.security.web.header.HeaderWriterFilter@4e396d1c, org.springframework.security.web.authentication.logout.LogoutFilter@7bb78381, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@12581426), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@140449d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c418f0b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@76614fd1, org.springframework.security.web.session.SessionManagementFilter@1ac9c3cc, org.springframework.security.web.access.ExceptionTranslationFilter@343b3399]
2024-06-19 15:51:23.595[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6e6c723b, org.springframework.security.web.context.SecurityContextPersistenceFilter@65448932, org.springframework.security.web.header.HeaderWriterFilter@7882379b, org.springframework.security.web.authentication.logout.LogoutFilter@2ceb722e, loyalty.activity.service.common.security.UserTokenFilter@66859669, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@731a5a39, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1acd952e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6e591c03, org.springframework.security.web.session.SessionManagementFilter@119cd026, org.springframework.security.web.access.ExceptionTranslationFilter@3b75b7b4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b6b4177]
2024-06-19 15:51:23.794[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 15:51:24.378[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 15:51:25.174[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 15:51:25.270[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 15:51:25.287[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 15:51:27.031[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 16.208 seconds (JVM running for 16.957)
2024-06-19 15:55:16.541[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 15:55:16.542[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 15:55:16.544[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2024-06-19 15:55:16.630[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassById】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":EOCfujian2024061900278】
2024-06-19 15:55:16.661[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2024-06-19 15:55:28.423[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2024-06-19 15:55:32.125[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:42] [INFO] - 【NCP】 sending to url https://cdp-bk.mjngc.com/sales-api/api/web/portal/getcontactwayqrcode with param={"code":"101110","qrcodeType":"RecruitCode"},header={"Content-type":["application/json;charset\u003dUTF-8"],"signature":["86D92F381B6FC884C2F9BA7211DDDF93"],"timestamp":["1718783732112"],"access_key_id":["SCRMNPC_QRCODE"]}
2024-06-19 15:55:32.730[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:53] [INFO] - 【NCP】 response for url /api/web/portal/getcontactwayqrcode is {"status":200,"headers":{"Date":["Wed, 19 Jun 2024 07:55:32 GMT"],"Content-Type":["application/json"],"Content-Length":["142"],"Connection":["keep-alive"],"X-Frame-Options":["sameorigin"],"X-Xss-Protection":["1; mode\u003dblock"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy-Report-Only":["Content-Security-Policy: frame-ancestors \u0027self\u0027"],"Set-Cookie":["cookiesession1\u003d678A3E4B3B1EB30ADBCC303EF61DC9AB;Expires\u003dThu, 19 Jun 2025 07:55:32 GMT;Path\u003d/;HttpOnly"]},"body":{"code":"0","msg":"SUCCESS","qrCodeInfo":{"qrCode":"https://wework.qpic.cn/wwpic/1802_1Lq0x-K_T4yg6ON_1653365627/0"}}}
2024-06-19 15:55:32.733[3c1e9cfa881a47c0a6ee22680a11c2ec][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/info/getPclassById】 with exectime=【16117】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PclassInfoResponse(address=福建省泉州市丰泽区东湖街278号, city=泉州市, classesCode=EOCfujian2024061900278, classesType=OUTSIDE_PCLASS, content=null, courseName=全线产品主题（牛奶）, endTime=Thu Jun 20 20:00:00 CST 2024, expertIntroduce=null, expertName=null, hotline=, id=923221780, isSignIn=false, isSignUp=false, isDeleted=false, isEnabled=true, ownerId=101110, place=null, province=福建省, remark=null, startTime=Thu Jun 20 08:30:00 CST 2024, topic=全线产品主题（牛奶）, pclassId=EOCfujian2024061900278, latitude=24.9096480, longitude=118.6181070, ncQrcode=https://wework.qpic.cn/wwpic/1802_1Lq0x-K_T4yg6ON_1653365627/0)}】
2024-06-19 16:00:31.549[][http-nio-9072-exec-3] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:02:54.176[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 16:02:54.203[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2024-06-19 16:02:54.212[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
2024-06-19 16:03:59.473[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 16:03:59.532[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:04:00.050[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:04:00.050[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:04:00.050[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784240048
2024-06-19 16:04:00.906[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 16:04:00.907[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:04:00.914[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:04:00.914[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:04:00.914[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784240914
2024-06-19 16:04:01.056[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 16:04:02.106[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 16:04:02.120[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 16:04:02.162[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2024-06-19 16:04:02.429[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.430[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.431[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.431[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.431[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.431[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.432[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.432[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.432[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.432[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.433[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.433[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.433[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.433[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.433[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.434[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.434[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.434[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.434[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.435[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.435[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.435[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.435[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.435[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.436[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.436[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.436[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.436[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.436[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.437[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.437[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.437[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.437[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:04:02.438[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 16:04:02.550[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 16:04:02.572[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 16:04:02.632[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 16:04:02.633[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:04:02.633[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:04:02.634[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.634[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.634[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:04:02.634[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.635[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.635[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.635[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.635[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.635[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:04:02.636[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:04:02.677[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 16:04:03.027[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.030[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.034[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.038[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.071[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.074[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.075[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:04:03.144[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 16:04:03.147[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 16:04:03.387[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 16:04:03.398[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 16:04:03.399[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 16:04:03.400[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 16:04:03.555[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 16:04:03.556[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2625 ms
2024-06-19 16:04:03.730[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 16:04:04.149[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 16:04:04.158[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 16:04:04.159[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 16:04:04.159[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 16:04:04.160[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 16:04:04.160[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 16:04:04.160[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 16:04:04.161[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 16:04:04.162[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 16:04:07.340[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 16:04:08.382[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 16:04:08.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 16:04:08.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 16:04:08.787[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 16:04:08.787[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 16:04:08.787[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 16:04:08.787[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 16:04:08.788[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 16:04:08.788[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 16:04:08.813[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2eebb07, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d0bbc49, org.springframework.security.web.header.HeaderWriterFilter@3b802baa, org.springframework.security.web.authentication.logout.LogoutFilter@10cb1b89, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@1d0b4b69), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9198fe3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4dfe14d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ab1ff80, org.springframework.security.web.session.SessionManagementFilter@45f20384, org.springframework.security.web.access.ExceptionTranslationFilter@4f61c7e1]
2024-06-19 16:04:08.828[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@20c7a91d, org.springframework.security.web.context.SecurityContextPersistenceFilter@6193a9e2, org.springframework.security.web.header.HeaderWriterFilter@1b9785aa, org.springframework.security.web.authentication.logout.LogoutFilter@21b508a0, loyalty.activity.service.common.security.UserTokenFilter@2048255a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@347074a9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@136d1d58, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7e00a43e, org.springframework.security.web.session.SessionManagementFilter@340f4723, org.springframework.security.web.access.ExceptionTranslationFilter@210e971e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@161974d1]
2024-06-19 16:04:09.019[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 16:04:09.577[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 16:04:10.449[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 16:04:10.518[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 16:04:10.533[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 16:04:12.177[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 14.006 seconds (JVM running for 14.683)
2024-06-19 16:04:21.735[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 16:04:21.735[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 16:04:21.737[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2024-06-19 16:04:21.778[][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:04:27.471[][http-nio-9072-exec-4] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:04:28.358[][http-nio-9072-exec-5] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:04:28.806[][http-nio-9072-exec-3] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:07:14.087[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 16:07:22.259[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 16:07:22.318[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:07:22.838[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:07:22.838[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:07:22.839[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784442837
2024-06-19 16:07:23.668[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 16:07:23.668[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:07:23.674[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:07:23.674[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:07:23.675[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784443674
2024-06-19 16:07:23.817[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 16:07:24.877[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 16:07:24.893[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 16:07:24.938[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2024-06-19 16:07:25.209[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.210[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.211[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.211[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.211[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.211[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.212[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.212[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.212[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.212[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.213[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.213[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.213[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.213[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.213[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.214[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.214[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.214[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.214[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.215[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.215[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.215[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.215[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.216[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.216[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.216[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.216[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.216[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.217[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.217[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.217[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.218[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.218[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:07:25.219[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 16:07:25.326[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 16:07:25.348[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 16:07:25.407[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 16:07:25.407[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:07:25.408[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:07:25.408[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.408[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.408[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:07:25.409[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.409[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.409[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.409[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.409[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.410[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:07:25.410[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:07:25.454[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 16:07:25.820[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.825[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.830[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.835[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.875[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.878[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.880[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:07:25.946[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 16:07:25.949[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 16:07:26.192[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 16:07:26.202[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 16:07:26.203[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 16:07:26.203[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 16:07:26.358[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 16:07:26.359[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2669 ms
2024-06-19 16:07:26.530[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 16:07:26.955[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 16:07:26.963[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 16:07:26.964[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 16:07:26.965[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 16:07:26.965[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 16:07:26.965[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 16:07:26.965[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 16:07:26.966[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 16:07:26.967[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 16:07:30.112[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 16:07:31.139[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 16:07:31.541[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 16:07:31.541[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 16:07:31.541[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 16:07:31.542[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 16:07:31.542[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 16:07:31.542[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 16:07:31.542[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 16:07:31.543[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 16:07:31.569[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27215154, org.springframework.security.web.context.SecurityContextPersistenceFilter@48c5a07d, org.springframework.security.web.header.HeaderWriterFilter@22912968, org.springframework.security.web.authentication.logout.LogoutFilter@56929ce, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@6a40e660), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d2bcf0a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@f1b0658, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6bca3b27, org.springframework.security.web.session.SessionManagementFilter@7fe75649, org.springframework.security.web.access.ExceptionTranslationFilter@4951dc8c]
2024-06-19 16:07:31.584[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55c901b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@54602d5a, org.springframework.security.web.header.HeaderWriterFilter@4b391e45, org.springframework.security.web.authentication.logout.LogoutFilter@7ae5f795, loyalty.activity.service.common.security.UserTokenFilter@7197b96c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@161974d1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15d18b61, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@105e9fef, org.springframework.security.web.session.SessionManagementFilter@647bd553, org.springframework.security.web.access.ExceptionTranslationFilter@2b8106f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@295e9aef]
2024-06-19 16:07:31.769[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 16:07:32.308[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 16:07:33.156[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 16:07:33.221[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 16:07:33.235[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 16:07:34.816[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.885 seconds (JVM running for 14.555)
2024-06-19 16:07:37.059[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 16:07:37.059[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 16:07:37.061[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2024-06-19 16:07:37.108[][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/external/inside/pclass/signIn 
org.springframework.web.HttpMediaTypeNotSupportedException: Content type '' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:264)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:417)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:364)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:123)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:66)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:491)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1254)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1036)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:09:40.325[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 16:09:47.073[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 16:09:47.106[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:09:47.589[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:09:47.589[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:09:47.589[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784587588
2024-06-19 16:09:48.378[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 16:09:48.378[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:09:48.385[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:09:48.386[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:09:48.386[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718784588385
2024-06-19 16:09:48.618[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 16:09:49.605[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 16:09:49.620[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 16:09:49.662[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2024-06-19 16:09:49.932[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.933[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.933[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.933[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.934[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.934[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.934[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.934[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.935[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.935[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.935[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.935[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.935[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.936[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.936[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.936[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.936[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.936[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.937[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.937[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.937[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.937[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.938[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.938[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.938[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.938[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.939[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.939[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.939[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.939[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.939[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.940[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.940[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:09:49.941[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 16:09:50.050[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 16:09:50.070[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 16:09:50.128[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 16:09:50.129[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:09:50.129[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:09:50.130[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.130[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.130[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:09:50.130[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.130[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.131[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.131[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.131[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.131[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:09:50.132[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:09:50.174[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 16:09:50.530[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.534[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.540[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.544[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.586[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.588[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.590[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:09:50.658[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 16:09:50.661[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 16:09:50.931[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 16:09:50.942[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 16:09:50.943[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 16:09:50.944[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 16:09:51.103[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 16:09:51.103[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2700 ms
2024-06-19 16:09:51.280[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 16:09:51.701[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 16:09:51.710[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 16:09:51.711[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 16:09:51.711[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 16:09:51.711[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 16:09:51.712[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 16:09:51.712[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 16:09:51.713[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 16:09:51.714[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 16:09:54.837[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 16:09:55.845[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 16:09:56.210[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 16:09:56.211[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 16:09:56.211[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 16:09:56.211[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 16:09:56.211[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 16:09:56.212[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 16:09:56.212[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 16:09:56.212[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 16:09:56.237[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a8316fa, org.springframework.security.web.context.SecurityContextPersistenceFilter@54aacbc7, org.springframework.security.web.header.HeaderWriterFilter@1c4335c8, org.springframework.security.web.authentication.logout.LogoutFilter@64f34c91, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@60abe420), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7b80ac30, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@27115d45, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@46a5aff, org.springframework.security.web.session.SessionManagementFilter@44b99f09, org.springframework.security.web.access.ExceptionTranslationFilter@efe75a2]
2024-06-19 16:09:56.266[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e0bae92, org.springframework.security.web.context.SecurityContextPersistenceFilter@58f27c37, org.springframework.security.web.header.HeaderWriterFilter@d4962bd, org.springframework.security.web.authentication.logout.LogoutFilter@52dfd7f5, loyalty.activity.service.common.security.UserTokenFilter@123fd460, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@78ae34f7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3eb4fdd5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5037f491, org.springframework.security.web.session.SessionManagementFilter@76fa3dd8, org.springframework.security.web.access.ExceptionTranslationFilter@1d47b761, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@18b8518b]
2024-06-19 16:09:56.464[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 16:09:57.009[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 16:09:57.809[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 16:09:57.879[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 16:09:57.898[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 16:09:59.442[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.677 seconds (JVM running for 14.273)
2024-06-19 16:10:04.647[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 16:10:04.647[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 16:10:04.650[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2024-06-19 16:10:04.759[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/inside/pclass/signIn】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":InsidePClassSignInRequest(mobile=18316552259, code=SOCguangzhou2023062900706, latitude=null, longitude=null)】
2024-06-19 16:10:04.801[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2024-06-19 16:10:16.815[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2024-06-19 16:10:16.914[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn:66] [INFO] - 院内课程签到，param=UserSignInRequest(activityId=923071388, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=null, pnecId=null, signInType=INSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=null, pnaId=null, ncCode=null, tsrId=null)
2024-06-19 16:10:17.020[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator:34] [INFO] - 课程已签到，param=PclassInfoBase(id=923071388, startTime=Fri Jul 14 06:44:00 CST 2023, endTime=Fri Jul 14 23:59:00 CST 2023, isEnabled=true, limitCount=null, applyCount=15, status=Active, isDeleted=false, ClassCode=SOCguangzhou2023062900706, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=23.1691640, targetLongitude=113.4670010, isOnline=null)
2024-06-19 16:10:17.021[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn:75] [INFO] - user=[UserSignInRequest(activityId=923071388, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=null, pnecId=null, signInType=INSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=null, pnaId=null, ncCode=null, tsrId=null)] has already signed
2024-06-19 16:10:25.377[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:42] [INFO] - 【NCP】 sending to url https://cdp-bk.mjngc.com/sales-api/api/web/portal/getcontactwayqrcode with param={"code":"822229","qrcodeType":"RecruitCode"},header={"Content-type":["application/json;charset\u003dUTF-8"],"signature":["B660F50D2B3EFF4551FBFBC8D10827A8"],"timestamp":["1718784625364"],"access_key_id":["SCRMNPC_QRCODE"]}
2024-06-19 16:10:37.197[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:53] [INFO] - 【NCP】 response for url /api/web/portal/getcontactwayqrcode is {"status":200,"headers":{"Date":["Wed, 19 Jun 2024 08:10:37 GMT"],"Content-Type":["application/json"],"Content-Length":["210"],"Connection":["keep-alive"],"X-Frame-Options":["sameorigin"],"X-Xss-Protection":["1; mode\u003dblock"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy-Report-Only":["Content-Security-Policy: frame-ancestors \u0027self\u0027"],"Set-Cookie":["cookiesession1\u003d678A3E4B0CD3294CFA918110AF2D20C6;Expires\u003dThu, 19 Jun 2025 08:10:37 GMT;Path\u003d/;HttpOnly"]},"body":{"code":"100001","msg":"invalid string value `822229`. userid not found, hint: [1718784637338730317199207], from ip: *************, more info at https://open.work.weixin.qq.com/devtool/query?e\u003d60111"}}
2024-06-19 16:10:37.202[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.pnec.service.PNECClassService:250] [ERROR] - ncp client getContactWayQrCode error = 
loyalty.activity.service.error.NcClientException: null
	at loyalty.activity.service.error.handler.NCPResponseHandler.handleNCPV2Response(NCPResponseHandler.java:23)
	at loyalty.activity.service.pnec.service.PNECClassService.getPersonQrCode(PNECClassService.java:247)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:490)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at loyalty.activity.service.pnec.service.PNECClassService$$EnhancerBySpringCGLIB$$3c9817e3.getPersonQrCode(<generated>)
	at loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn.signIn(InsidePclassSignIn.java:124)
	at loyalty.activity.service.policy.pclass.signIn.SignInPolicy.doSignIn(SignInPolicy.java:30)
	at loyalty.activity.service.pclass.service.PclassSignInService.userSignIn(PclassSignInService.java:120)
	at loyalty.activity.service.external.service.InsidePclassService.signIn(InsidePclassService.java:73)
	at loyalty.activity.service.external.service.InsidePclassService$$FastClassBySpringCGLIB$$7b3177e5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at loyalty.activity.service.external.service.InsidePclassService$$EnhancerBySpringCGLIB$$7b9cfcc5.signIn(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:490)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at loyalty.activity.service.external.service.InsidePclassService$$EnhancerBySpringCGLIB$$c6843c74.signIn(<generated>)
	at loyalty.activity.service.external.controller.InsidePclassController.signIn(InsidePclassController.java:78)
	at loyalty.activity.service.external.controller.InsidePclassController$$FastClassBySpringCGLIB$$9cb5369f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at loyalty.activity.service.external.controller.InsidePclassController$$EnhancerBySpringCGLIB$$db7d28db.signIn(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2024-06-19 16:10:37.207[10e67e449f9542a99ba374c1b8e6646a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/inside/pclass/signIn】 with exectime=【32463】,user=【-1】,ip=【127.0.0.1】,result=【InsidePClassInternalResponse(status=true, message=签到成功！)】
2024-06-19 16:17:09.928[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 16:17:09.957[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2024-06-19 16:17:09.967[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
2024-06-19 16:17:16.227[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2024-06-19 16:17:16.290[][background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:17:16.856[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:17:16.856[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:17:16.856[][background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718785036854
2024-06-19 16:17:17.696[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local-nacos,attacher
2024-06-19 16:17:17.696[][main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2024-06-19 16:17:17.703[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2024-06-19 16:17:17.704[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2024-06-19 16:17:17.704[][main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1718785037703
2024-06-19 16:17:17.843[][kafka-producer-network-thread | WINDOWS-QGI1E3E-default-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-default-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2024-06-19 16:17:18.925[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-06-19 16:17:18.941[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-06-19 16:17:18.983[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2024-06-19 16:17:19.262[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.263[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.263[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.263[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.263[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.264[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.264[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.264[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.264[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.265[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.265[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.265[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.266[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.266[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.266[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.266[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.267[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.267[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.267[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.268[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.268[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.268[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.268[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.269[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.269[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.269[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.270[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.270[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.270[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.270[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.271[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.271[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.271[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2024-06-19 16:17:19.272[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2024-06-19 16:17:19.379[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5411f5bb-606c-396e-8c63-d1c2917b32bf
2024-06-19 16:17:19.401[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2024-06-19 16:17:19.463[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2024-06-19 16:17:19.463[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:17:19.464[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:17:19.464[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.464[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.464[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:17:19.464[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource localconfig [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.465[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.465[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [demo-local-properties.yaml]' via location 'classpath:/demo-local-properties.yaml' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.465[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.465[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #5) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.465[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2024-06-19 16:17:19.466[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2024-06-19 16:17:19.516[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2024-06-19 16:17:19.900[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.903[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.908[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.912[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.950[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.953[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:19.955[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-06-19 16:17:20.025[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2024-06-19 16:17:20.028[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2024-06-19 16:17:20.287[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2024-06-19 16:17:20.299[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2024-06-19 16:17:20.299[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2024-06-19 16:17:20.300[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2024-06-19 16:17:20.466[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2024-06-19 16:17:20.467[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2747 ms
2024-06-19 16:17:20.647[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2024-06-19 16:17:21.091[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2024-06-19 16:17:21.100[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2024-06-19 16:17:21.100[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2024-06-19 16:17:21.101[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2024-06-19 16:17:21.101[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2024-06-19 16:17:21.101[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2024-06-19 16:17:21.101[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2024-06-19 16:17:21.102[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2024-06-19 16:17:21.103[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2024-06-19 16:17:24.295[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2024-06-19 16:17:25.399[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 3 endpoint(s) beneath base path '/actuator'
2024-06-19 16:17:25.807[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2024-06-19 16:17:25.808[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2024-06-19 16:17:25.808[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2024-06-19 16:17:25.808[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2024-06-19 16:17:25.809[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2024-06-19 16:17:25.809[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2024-06-19 16:17:25.809[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2024-06-19 16:17:25.809[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2024-06-19 16:17:25.833[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2eebb07, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d0bbc49, org.springframework.security.web.header.HeaderWriterFilter@3b802baa, org.springframework.security.web.authentication.logout.LogoutFilter@10cb1b89, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@1d0b4b69), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9198fe3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4dfe14d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ab1ff80, org.springframework.security.web.session.SessionManagementFilter@45f20384, org.springframework.security.web.access.ExceptionTranslationFilter@4f61c7e1]
2024-06-19 16:17:25.847[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@20c7a91d, org.springframework.security.web.context.SecurityContextPersistenceFilter@6193a9e2, org.springframework.security.web.header.HeaderWriterFilter@1b9785aa, org.springframework.security.web.authentication.logout.LogoutFilter@21b508a0, loyalty.activity.service.common.security.UserTokenFilter@2048255a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@347074a9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@136d1d58, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7e00a43e, org.springframework.security.web.session.SessionManagementFilter@340f4723, org.springframework.security.web.access.ExceptionTranslationFilter@210e971e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@161974d1]
2024-06-19 16:17:26.035[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2024-06-19 16:17:26.586[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2024-06-19 16:17:27.430[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2024-06-19 16:17:27.495[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2024-06-19 16:17:27.512[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2024-06-19 16:17:29.241[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 14.407 seconds (JVM running for 15.115)
2024-06-19 16:17:37.672[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-06-19 16:17:37.672[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2024-06-19 16:17:37.675[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2024-06-19 16:17:37.759[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassById】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":EOCfujian2024061900278】
2024-06-19 16:17:37.789[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2024-06-19 16:17:38.475[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2024-06-19 16:17:44.805[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:42] [INFO] - 【NCP】 sending to url https://cdp-bk.mjngc.com/sales-api/api/web/portal/getcontactwayqrcode with param={"code":"101110","qrcodeType":"RecruitCode"},header={"Content-type":["application/json;charset\u003dUTF-8"],"signature":["124C16077683BBC20D6BFE2387A39AA3"],"timestamp":["1718785064795"],"access_key_id":["SCRMNPC_QRCODE"]}
2024-06-19 16:17:45.396[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPV2Client:53] [INFO] - 【NCP】 response for url /api/web/portal/getcontactwayqrcode is {"status":200,"headers":{"Date":["Wed, 19 Jun 2024 08:17:45 GMT"],"Content-Type":["application/json"],"Content-Length":["142"],"Connection":["keep-alive"],"X-Frame-Options":["sameorigin"],"X-Xss-Protection":["1; mode\u003dblock"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy-Report-Only":["Content-Security-Policy: frame-ancestors \u0027self\u0027"],"Set-Cookie":["cookiesession1\u003d678A3E4BE9980D6E81E436F80E5B4803;Expires\u003dThu, 19 Jun 2025 08:17:45 GMT;Path\u003d/;HttpOnly"]},"body":{"code":"0","msg":"SUCCESS","qrCodeInfo":{"qrCode":"https://wework.qpic.cn/wwpic/1802_1Lq0x-K_T4yg6ON_1653365627/0"}}}
2024-06-19 16:17:45.398[a2ba69da98a64a37a62f2b30bc1ccdc6][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/info/getPclassById】 with exectime=【7653】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PclassInfoResponse(address=福建省泉州市丰泽区东湖街278号, city=泉州市, classesCode=EOCfujian2024061900278, classesType=OUTSIDE_PCLASS, content=null, courseName=全线产品主题（牛奶）, endTime=Thu Jun 20 20:00:00 CST 2024, expertIntroduce=null, expertName=null, hotline=, id=923221780, isSignIn=false, isSignUp=false, isDeleted=false, isEnabled=true, ownerId=101110, place=null, province=福建省, remark=null, startTime=Thu Jun 20 08:30:00 CST 2024, topic=全线产品主题（牛奶）, pclassId=EOCfujian2024061900278, latitude=24.9096480, longitude=118.6181070, ncQrcode=https://wework.qpic.cn/wwpic/1802_1Lq0x-K_T4yg6ON_1653365627/0)}】
2024-06-19 16:17:55.327[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/inside/pclass/signIn】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":InsidePClassSignInRequest(mobile=18316552259, code=SOCguangzhou2023062900706, latitude=null, longitude=null)】
2024-06-19 16:17:55.436[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn:66] [INFO] - 院内课程签到，param=UserSignInRequest(activityId=923071388, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=null, pnecId=null, signInType=INSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=null, pnaId=null, ncCode=null, tsrId=null)
2024-06-19 16:17:55.537[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator:34] [INFO] - 课程已签到，param=PclassInfoBase(id=923071388, startTime=Fri Jul 14 06:44:00 CST 2023, endTime=Fri Jul 14 23:59:00 CST 2023, isEnabled=true, limitCount=null, applyCount=15, status=Active, isDeleted=false, ClassCode=SOCguangzhou2023062900706, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=23.1691640, targetLongitude=113.4670010, isOnline=null)
2024-06-19 16:17:55.538[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn:75] [INFO] - user=[UserSignInRequest(activityId=923071388, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=null, pnecId=null, signInType=INSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=null, pnaId=null, ncCode=null, tsrId=null)] has already signed
2024-06-19 16:17:55.539[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.external.controller.InsidePclassController:87] [ERROR] - Internal error execute /external/inside/pclass/signIn with errorCode=22, message=[您已经签过到了]
2024-06-19 16:17:55.540[6fc13974fb3b441db3e9379838b11393][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/inside/pclass/signIn】 with exectime=【213】,user=【-1】,ip=【127.0.0.1】,result=【InsidePClassInternalResponse(status=false, message=您已经签过到了)】
2024-06-19 16:21:31.964[14844359e30c42d989c8fd7af9df6dbc][http-nio-9072-exec-4] [loyalty.activity.service.common.controller.BaseExceptionController:35] [WARN] - Parameter error occurred while execute /loyalty-activity-service/pclass/signIn/userSignIn with msg=签到类型不能为空
2024-06-19 16:23:38.039[b069a770c97d491b8dfd9761aec107a2][http-nio-9072-exec-6] [loyalty.activity.service.common.controller.BaseExceptionController:35] [WARN] - Parameter error occurred while execute /loyalty-activity-service/pclass/signIn/userSignIn with msg=用户名
2024-06-19 16:24:15.309[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signIn/userSignIn】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserSignInRequest(activityId=923069986, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=1, pnecId=null, signInType=OUTSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=jimmy, pnaId=null, ncCode=null, tsrId=null)】
2024-06-19 16:24:15.313[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [loyalty.activity.service.policy.pclass.signIn.OutsidePclassSignIn:65] [INFO] - 院外课程签到，param=UserSignInRequest(activityId=923069986, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=1, pnecId=null, signInType=OUTSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=jimmy, pnaId=null, ncCode=null, tsrId=null)
2024-06-19 16:24:15.758[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator:34] [INFO] - 课程已签到，param=PclassInfoBase(id=923069986, startTime=Sat Jul 15 09:00:00 CST 2023, endTime=Sat Jul 15 16:00:00 CST 2023, isEnabled=true, limitCount=null, applyCount=58, status=Active, isDeleted=false, ClassCode=NOCbeijing2023062700036, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=40.0600940, targetLongitude=116.4157660, isOnline=null)
2024-06-19 16:24:15.759[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [loyalty.activity.service.policy.pclass.signIn.OutsidePclassSignIn:76] [INFO] - user=[UserSignInRequest(activityId=923069986, babyBirthday=null, cellphone=18316552259, city=null, latitude=null, longitude=null, openId=null, attendance=1, pnecId=null, signInType=OUTSIDE_PCLASS, unionId=null, babyStatus=null, remark=null, userName=jimmy, pnaId=null, ncCode=null, tsrId=null)] has already signed
2024-06-19 16:24:23.536[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [com.cstools.data.internal.client.NCPV2Client:42] [INFO] - 【NCP】 sending to url https://cdp-bk.mjngc.com/sales-api/api/web/portal/getcontactwayqrcode with param={"code":"5022792","qrcodeType":"RecruitCode"},header={"Content-type":["application/json;charset\u003dUTF-8"],"signature":["16A84AD4239EDB9633414BAE5A0577FF"],"timestamp":["1718785463535"],"access_key_id":["SCRMNPC_QRCODE"]}
2024-06-19 16:24:35.481[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [com.cstools.data.internal.client.NCPV2Client:53] [INFO] - 【NCP】 response for url /api/web/portal/getcontactwayqrcode is {"status":200,"headers":{"Date":["Wed, 19 Jun 2024 08:24:35 GMT"],"Content-Type":["application/json"],"Content-Length":["144"],"Connection":["keep-alive"],"X-Frame-Options":["sameorigin"],"X-Xss-Protection":["1; mode\u003dblock"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy-Report-Only":["Content-Security-Policy: frame-ancestors \u0027self\u0027"],"Set-Cookie":["cookiesession1\u003d678A3E4B45E3ADB24AD01E718B800017;Expires\u003dThu, 19 Jun 2025 08:24:35 GMT;Path\u003d/;HttpOnly"]},"body":{"code":"0","msg":"SUCCESS","qrCodeInfo":{"qrCode":"https://wework.qpic.cn/wwpic/730152_6GCUiEabTf-ZW3K_1685751114/0"}}}
2024-06-19 16:24:35.484[dcd60e59329e41e3a0f7becdd9f5edd1][http-nio-9072-exec-7] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/signIn/userSignIn】 with exectime=【20174】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={UserSignInResponse(id=1401752, qrCode=https://wework.qpic.cn/wwpic/730152_6GCUiEabTf-ZW3K_1685751114/0, isAdd=false, isSigned=true)}】
2024-06-19 16:38:06.025[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2024-06-19 16:38:06.052[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:350] [INFO] - HikariPool-1 - Shutdown initiated...
2024-06-19 16:38:06.062[][SpringContextShutdownHook] [com.zaxxer.hikari.HikariDataSource:352] [INFO] - HikariPool-1 - Shutdown completed.
