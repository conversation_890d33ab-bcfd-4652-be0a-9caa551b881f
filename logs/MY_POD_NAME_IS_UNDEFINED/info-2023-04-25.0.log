2023-04-25 09:56:01.978[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-25 09:56:02.037[background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:02.462[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:02.462[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:02.462[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387762460
2023-04-25 09:56:03.152[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-25 09:56:03.153[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:03.158[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:03.158[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:03.158[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387763158
2023-04-25 09:56:03.192[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-25 09:56:03.303[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-25 09:56:03.313[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-25 09:56:03.381[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-25 09:56:03.432[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-25 09:56:03.441[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-25 09:56:03.570[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-04-25 09:56:03.570[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:03.574[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:03.575[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:03.575[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387763574
2023-04-25 09:56:03.720[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-25 09:56:04.738[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-25 09:56:04.743[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-25 09:56:04.799[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-25 09:56:05.017[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.019[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.019[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.019[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.019[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.020[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.020[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.020[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.020[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.021[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.021[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.021[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.021[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.022[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.022[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.022[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.023[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.025[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.025[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.025[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.025[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.026[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.026[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.026[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.026[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.027[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.027[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:05.028[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-25 09:56:05.136[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=96ddcef8-1d02-3829-95f8-7df6b2b2cf59
2023-04-25 09:56:05.156[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-25 09:56:05.190[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:05.191[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:05.191[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:05.216[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-25 09:56:05.216[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:05.216[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:05.217[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:05.217[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:05.217[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:05.217[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:05.217[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:05.218[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:05.218[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:05.261[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-25 09:56:05.751[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-25 09:56:05.754[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-25 09:56:06.060[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-25 09:56:06.073[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-25 09:56:06.074[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-25 09:56:06.074[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-25 09:56:06.230[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-25 09:56:06.230[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2634 ms
2023-04-25 09:56:06.534[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-25 09:56:06.839[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-25 09:56:06.849[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-25 09:56:06.850[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-25 09:56:06.850[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-25 09:56:06.851[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-25 09:56:06.851[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-25 09:56:06.851[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-25 09:56:06.852[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-25 09:56:06.852[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-25 09:56:09.603[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-25 09:56:10.422[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-25 09:56:10.423[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-25 09:56:10.424[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-25 09:56:11.019[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-25 09:56:11.382[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-25 09:56:11.383[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-25 09:56:11.410[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bdfae42, org.springframework.security.web.context.SecurityContextPersistenceFilter@f81bd2c, org.springframework.security.web.header.HeaderWriterFilter@3a8ae302, org.springframework.security.web.authentication.logout.LogoutFilter@6f2f4b9e, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@5037f491), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@148c571c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e36a4c1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4e396d1c, org.springframework.security.web.session.SessionManagementFilter@213fc7e7, org.springframework.security.web.access.ExceptionTranslationFilter@1484cb57]
2023-04-25 09:56:11.424[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@482c8db4, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c88db1, org.springframework.security.web.header.HeaderWriterFilter@7d412ab5, org.springframework.security.web.authentication.logout.LogoutFilter@672710d5, loyalty.activity.service.common.security.UserTokenFilter@348c00d9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@241861bc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5078e308, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41f274d, org.springframework.security.web.session.SessionManagementFilter@74ee07e, org.springframework.security.web.access.ExceptionTranslationFilter@406cf37f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21298c3a]
2023-04-25 09:56:11.612[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-25 09:56:11.757[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-25 09:56:12.177[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-25 09:56:12.956[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-25 09:56:12.969[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-25 09:56:13.373[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-25 09:56:13.375[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-25 09:56:13.426[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-25 09:56:14.018[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2023-04-25 09:56:14.023[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2023-04-25 09:56:14.299[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.485 seconds (JVM running for 14.153)
2023-04-25 09:56:14.308[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:56:14.309[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:56:14.310[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-uat.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:56:14.310[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:56:14.311[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:56:14.311[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:56:27.334[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-25 09:56:27.334[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-25 09:56:27.334[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-25 09:56:27.335[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-25 09:56:27.641[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-25 09:56:27.649[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-25 09:56:27.649[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-25 09:56:27.650[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-25 09:56:27.697[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-25 09:56:27.698[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-25 09:56:28.538[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-25 09:56:28.538[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-25 09:56:31.583[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-25 09:56:34.598[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-25 09:56:34.599[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-25 09:56:34.599[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-25 09:56:34.600[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-25 09:56:34.600[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-25 09:56:34.601[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-25 09:56:34.602[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-25 09:56:34.603[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-25 09:56:34.603[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-25 09:56:34.604[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-25 09:56:34.605[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-25 09:56:34.607[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-25 09:56:50.511[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-25 09:56:50.570[background-preinit] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-default-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:50.996[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:50.997[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:50.997[background-preinit] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387810995
2023-04-25 09:56:51.714[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-25 09:56:51.715[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:51.720[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:51.720[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:51.720[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387811720
2023-04-25 09:56:51.749[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-25 09:56:51.865[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-25 09:56:51.865[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-25 09:56:51.926[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-25 09:56:51.975[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-25 09:56:51.983[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-25 09:56:52.111[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-04-25 09:56:52.111[main] [org.apache.kafka.common.config.AbstractConfig:354] [INFO] - ProducerConfig values: 
	acks = 0
	batch.size = 16384
	bootstrap.servers = [*************:9092, ************:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = WINDOWS-QGI1E3E-nacos-logback-relaxed
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	internal.auto.downgrade.txn.commit = false
	key.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer
	linger.ms = 1000
	max.block.ms = 0
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.class = class org.apache.kafka.clients.producer.internals.DefaultPartitioner
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2023-04-25 09:56:52.115[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:117] [INFO] - Kafka version: 2.6.0
2023-04-25 09:56:52.116[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:118] [INFO] - Kafka commitId: 62abe01bee039651
2023-04-25 09:56:52.116[main] [org.apache.kafka.common.utils.AppInfoParser$AppInfo:119] [INFO] - Kafka startTimeMs: 1682387812115
2023-04-25 09:56:52.260[kafka-producer-network-thread | WINDOWS-QGI1E3E-nacos-logback-relaxed] [org.apache.kafka.clients.Metadata:279] [INFO] - [Producer clientId=WINDOWS-QGI1E3E-nacos-logback-relaxed] Cluster ID: ABygKS5hRGeHq3k_rZasTQ
2023-04-25 09:56:53.303[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-25 09:56:53.307[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-25 09:56:53.351[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-25 09:56:53.561[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.562[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.562[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.563[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.563[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.563[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.563[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.563[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.564[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.566[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.566[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.566[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.566[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.566[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.567[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.567[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.567[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.567[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.567[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.568[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.568[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.568[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.568[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.568[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.569[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.569[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-25 09:56:53.569[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-25 09:56:53.671[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=96ddcef8-1d02-3829-95f8-7df6b2b2cf59
2023-04-25 09:56:53.691[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-25 09:56:53.724[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:53.725[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:53.725[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-25 09:56:53.746[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-25 09:56:53.746[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:53.746[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:53.747[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:53.747[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:53.747[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:53.747[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:53.747[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:53.748[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-25 09:56:53.748[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-25 09:56:53.790[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-25 09:56:54.231[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-25 09:56:54.234[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-25 09:56:54.490[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-25 09:56:54.503[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-25 09:56:54.504[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-25 09:56:54.504[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-25 09:56:54.657[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-25 09:56:54.658[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2527 ms
2023-04-25 09:56:54.942[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-25 09:56:55.270[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-25 09:56:55.280[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-25 09:56:55.281[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-25 09:56:55.281[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-25 09:56:55.282[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-25 09:56:55.282[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-25 09:56:55.282[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-25 09:56:55.283[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-25 09:56:55.283[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-25 09:56:58.038[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-25 09:56:58.890[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-25 09:56:58.892[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-25 09:56:58.893[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-25 09:56:59.428[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-25 09:56:59.804[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-25 09:56:59.805[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-25 09:56:59.805[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-25 09:56:59.805[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-25 09:56:59.805[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-25 09:56:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-25 09:56:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-25 09:56:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-25 09:56:59.832[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3bf31f3e, org.springframework.security.web.context.SecurityContextPersistenceFilter@1455d0f5, org.springframework.security.web.header.HeaderWriterFilter@1a524c6b, org.springframework.security.web.authentication.logout.LogoutFilter@6e87556f, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@40fd25c7), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65208c91, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@28878a52, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f8b1902, org.springframework.security.web.session.SessionManagementFilter@2f5a23c1, org.springframework.security.web.access.ExceptionTranslationFilter@4c1f4817]
2023-04-25 09:56:59.850[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ad67d82, org.springframework.security.web.context.SecurityContextPersistenceFilter@452e0a94, org.springframework.security.web.header.HeaderWriterFilter@295e9aef, org.springframework.security.web.authentication.logout.LogoutFilter@27a717a7, loyalty.activity.service.common.security.UserTokenFilter@301608d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a12048d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77c66ac2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3c3a8246, org.springframework.security.web.session.SessionManagementFilter@3f1b4a14, org.springframework.security.web.access.ExceptionTranslationFilter@fbc1de9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4086e003]
2023-04-25 09:57:00.036[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-25 09:57:00.178[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-25 09:57:00.586[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-25 09:57:01.381[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-25 09:57:01.395[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-25 09:57:01.841[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-25 09:57:01.842[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-25 09:57:01.890[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-25 09:57:02.442[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-25 09:57:02.450[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-25 09:57:02.794[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.52 seconds (JVM running for 14.462)
2023-04-25 09:57:02.801[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:57:02.803[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:57:02.804[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-uat.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:57:02.804[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:57:02.805[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-25 09:57:02.805[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.240_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-25 09:57:17.070[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-25 09:57:17.071[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-25 09:57:17.071[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-25 09:57:17.072[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-25 09:57:17.361[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-25 09:57:17.370[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-25 09:57:17.370[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-25 09:57:17.370[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-25 09:57:17.420[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-25 09:57:17.421[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-25 09:57:20.447[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-25 09:57:20.448[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-25 09:57:22.564[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-25 09:57:22.565[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(0) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> []
2023-04-25 09:57:22.567[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-25 09:57:25.602[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-25 09:57:25.603[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-25 09:57:25.604[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-25 09:57:25.604[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-25 09:57:25.604[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-25 09:57:25.605[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-25 09:57:25.605[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-25 09:57:25.605[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-25 09:57:25.606[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-25 09:57:25.606[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-25 09:57:25.606[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-25 09:57:25.607[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
