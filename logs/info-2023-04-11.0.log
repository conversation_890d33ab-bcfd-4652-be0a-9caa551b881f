2023-04-11 12:08:44.050[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 12:08:44.921[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 12:08:45.247[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 12:08:45.363[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 12:08:45.409[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 12:08:45.456[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 12:08:45.470[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 12:08:45.488[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 12:08:46.746[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 12:08:46.749[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 12:08:46.790[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-11 12:08:47.007[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.007[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.008[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.009[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.010[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.011[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.011[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.011[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.011[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:08:47.011[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 12:08:47.136[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 12:08:47.158[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 12:08:47.215[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:08:47.215[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:08:47.215[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:08:47.236[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 12:08:47.237[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:08:47.237[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:08:47.237[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:08:47.237[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:08:47.237[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:08:47.238[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:08:47.238[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:08:47.238[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:08:47.238[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:08:47.286[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 12:08:47.657[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 12:08:47.660[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 12:08:47.671[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 12:08:47.680[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 12:08:47.681[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 12:08:47.681[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 12:08:47.681[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 12:08:47.681[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 12:08:47.682[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 12:08:47.682[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 12:08:47.683[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 12:08:48.099[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:08:48.106[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$ce590e46] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:08:48.119[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:08:48.445[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 12:08:48.456[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 12:08:48.457[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 12:08:48.457[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 12:08:48.587[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 12:08:48.587[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3073 ms
2023-04-11 12:08:48.800[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 12:08:49.255[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 12:08:49.259[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 12:08:49.260[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 12:08:49.260[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 12:08:49.262[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 12:08:49.262[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 12:08:49.262[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 12:08:51.652[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 12:08:52.466[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 12:08:52.467[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 12:08:52.467[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 12:08:53.027[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 12:08:53.365[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 12:08:53.366[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 12:08:53.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@15d2c2ca, org.springframework.security.web.context.SecurityContextPersistenceFilter@67cc5a38, org.springframework.security.web.header.HeaderWriterFilter@78ae34f7, org.springframework.security.web.authentication.logout.LogoutFilter@5c6fae3c, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@253a8bdb), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@181a6784, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40af3dd8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@346d8002, org.springframework.security.web.session.SessionManagementFilter@2aec09a3, org.springframework.security.web.access.ExceptionTranslationFilter@4b2ca159]
2023-04-11 12:08:53.411[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@42014a, org.springframework.security.web.context.SecurityContextPersistenceFilter@227ae4e4, org.springframework.security.web.header.HeaderWriterFilter@1b2213b2, org.springframework.security.web.authentication.logout.LogoutFilter@93cc5eb, loyalty.activity.service.common.security.UserTokenFilter@5e78dd7f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7cc966a9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77fd5501, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@cf315f1, org.springframework.security.web.session.SessionManagementFilter@bf0025d, org.springframework.security.web.access.ExceptionTranslationFilter@4ff233f9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1639ec7a]
2023-04-11 12:08:53.611[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 12:08:53.791[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 12:08:54.238[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 12:08:55.086[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-11 12:08:55.092[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-11 12:08:55.095[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 12:08:55.118[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 12:08:55.540[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 12:08:55.542[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 12:08:55.587[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 12:08:56.151[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-11 12:08:56.151[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2023-04-11 12:08:56.252[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.19 seconds (JVM running for 14.126)
2023-04-11 12:08:56.260[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:08:56.261[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:08:56.262[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:08:56.262[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:08:56.262[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:08:56.262[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:08:57.108[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 12:08:57.109[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 12:08:57.110[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-04-11 12:09:40.293[http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/sync】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=a0j900000004UIjAAMguangzhou2023040700001, topic=如何选择奶粉, activityType=大型CS, consumerActivityType=大型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=小米, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 12:10:08.002[http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/cby/sync】 with user=【-1】, exectime=【27726】, params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=a0j900000004UIjAAMguangzhou2023040700001, topic=如何选择奶粉, activityType=大型CS, consumerActivityType=大型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=小米, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 12:10:08.169[http-nio-9072-exec-6] [loyalty.activity.service.common.controller.BaseExceptionController:50] [WARN] - Foreign constaint error occurred while execute /loyalty-activity-service/external/cby/sync with msg=Data truncation: Data too long for column 'id' at row 1
2023-04-11 12:10:41.230[http-nio-9072-exec-7] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/sync】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=a0j900000004UIjAAMguangzhou2023040700001, topic=如何选择奶粉, activityType=大型CS, consumerActivityType=大型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=小米, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 12:13:13.677[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 12:13:13.677[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 12:13:13.678[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 12:13:13.678[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 12:13:13.833[http-nio-9072-exec-7] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/cby/sync】 with user=【-1】, exectime=【152603】, params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=a0j900000004UIjAAMguangzhou2023040700001, topic=如何选择奶粉, activityType=大型CS, consumerActivityType=大型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=小米, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 12:13:13.936[http-nio-9072-exec-7] [loyalty.activity.service.common.controller.BaseExceptionController:50] [WARN] - Foreign constaint error occurred while execute /loyalty-activity-service/external/cby/sync with msg=Data truncation: Data too long for column 'id' at row 1
2023-04-11 12:13:13.977[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 12:13:13.987[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 12:13:13.988[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 12:13:13.988[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 12:13:14.080[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 12:13:14.080[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 12:13:17.115[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 12:13:17.116[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 12:13:20.144[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 12:13:23.175[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 12:13:23.175[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 12:13:23.176[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 12:13:23.177[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 12:13:23.177[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 12:13:23.179[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 12:13:23.179[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 12:13:23.180[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 12:13:23.180[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 12:13:23.180[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 12:13:23.181[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 12:13:23.182[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 12:13:23.228[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 12:13:23.235[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 12:13:23.251[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 12:13:23.252[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 12:13:23.252[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 12:13:50.396[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 12:13:51.329[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 12:13:51.644[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 12:13:51.771[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 12:13:51.822[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 12:13:51.875[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 12:13:51.911[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 12:13:51.937[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 12:13:53.182[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 12:13:53.186[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 12:13:53.230[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2023-04-11 12:13:53.450[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.452[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.453[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.454[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.455[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 12:13:53.456[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 12:13:53.570[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 12:13:53.592[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 12:13:53.631[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:13:53.631[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:13:53.631[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 12:13:53.650[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 12:13:53.650[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:13:53.650[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:13:53.650[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:13:53.650[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:13:53.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:13:53.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:13:53.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:13:53.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 12:13:53.652[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 12:13:53.701[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 12:13:54.089[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 12:13:54.092[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 12:13:54.105[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 12:13:54.114[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 12:13:54.114[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 12:13:54.114[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 12:13:54.115[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 12:13:54.115[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 12:13:54.115[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 12:13:54.116[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 12:13:54.116[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 12:13:54.547[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:13:54.554[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$3ac91f85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:13:54.570[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 12:13:54.893[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 12:13:54.904[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 12:13:54.905[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 12:13:54.905[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 12:13:55.030[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 12:13:55.031[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3069 ms
2023-04-11 12:13:55.242[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 12:13:55.726[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 12:13:55.729[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 12:13:55.731[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 12:13:55.731[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 12:13:55.731[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 12:13:55.732[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 12:13:55.732[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 12:13:58.112[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 12:13:58.920[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 12:13:58.921[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 12:13:58.921[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 12:13:59.469[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 12:13:59.806[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 12:13:59.833[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@322f6283, org.springframework.security.web.context.SecurityContextPersistenceFilter@27df4dcb, org.springframework.security.web.header.HeaderWriterFilter@160b7ccd, org.springframework.security.web.authentication.logout.LogoutFilter@6645c1f, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@4104b6a6), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5d38758a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@231d5119, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@168f447d, org.springframework.security.web.session.SessionManagementFilter@5ccc07ae, org.springframework.security.web.access.ExceptionTranslationFilter@72090a80]
2023-04-11 12:13:59.847[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7f50b08a, org.springframework.security.web.context.SecurityContextPersistenceFilter@33012e6f, org.springframework.security.web.header.HeaderWriterFilter@681ba763, org.springframework.security.web.authentication.logout.LogoutFilter@33c590, loyalty.activity.service.common.security.UserTokenFilter@56a69c47, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7593716d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4086e003, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@333cedbd, org.springframework.security.web.session.SessionManagementFilter@19cc697c, org.springframework.security.web.access.ExceptionTranslationFilter@197bf5ef, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@71acf065]
2023-04-11 12:14:00.033[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 12:14:00.181[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 12:14:00.604[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 12:14:01.395[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:01.401[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:01.403[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 12:14:01.425[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 12:14:01.833[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 12:14:01.834[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 12:14:01.896[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 12:14:02.452[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:02.453[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:02.573[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.136 seconds (JVM running for 14.045)
2023-04-11 12:14:02.583[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:14:02.584[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:14:02.585[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:14:02.585[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:14:02.585[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 12:14:02.585[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 12:14:09.688[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 12:14:09.688[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 12:14:09.690[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2023-04-11 12:14:09.768[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/sync】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=a0j900000004UIjAAMguangzhou2023040700001, topic=如何选择奶粉, activityType=大型CS, consumerActivityType=大型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=小米, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 12:14:15.504[http-nio-9072-exec-1] [loyalty.activity.service.external.service.CBYService:139] [INFO] - Insert 1 line pclass data ： [{"activity":{"id":264456,"channel":"CS","topic":"大型CS","startTime":"Apr 10, 2023 12:00:00 PM","endTime":"Apr 20, 2023 8:00:00 AM","ownerId":"123456","isEnabled":true,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"activityAddress":{"activityId":264456,"province":"广东","city":"广州","address":"测试地址","place":"南方医院","longitude":20.2356,"latitude":12.236,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"pclassInfo":{"id":"a0j900000004UIjAAMguangzhou2023040700001","pclassId":"a0j900000004UIjAAMguangzhou2023040700001","activityId":264456,"classesCode":"a0j900000004UIjAAMguangzhou2023040700001","ownerName":"小米","ownerMobile":"13800138000","ownerEmployeeNum":"123456","director":"小米","directorMobile":"13800138000","directorExmployeeNum":"123456","isEnabled":true,"expertName":"大神123","hotline":"888888","pclassType":"大型活动","pclassType2":"大型CS","courseName":"店内课程","ncCode":"123;56","status":"Active","activityType":"大型CS","isDeleted":false,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"activityOwnerRel":{"id":2264456,"activityId":264456,"userId":"123456","isOwner":true,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"operateType":"INSERT","isCorrectData":true}]
2023-04-11 12:14:15.506[http-nio-9072-exec-1] [loyalty.activity.service.external.service.CBYService:139] [INFO] - Update 0 line pclass data ： []
2023-04-11 12:14:15.599[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/cby/sync】 with exectime=【5845】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={ActivitySyncResponse(errorDataList=[], failSize=0, successActivityCodes=[a0j900000004UIjAAMguangzhou2023040700001])}】
2023-04-11 12:14:21.476[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 12:14:21.476[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 12:14:21.476[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 12:14:21.477[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 12:14:21.770[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 12:14:21.782[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 12:14:21.783[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 12:14:21.785[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 12:14:21.831[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 12:14:21.831[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 12:14:24.062[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 12:14:24.062[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 12:14:24.091[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:24.093[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 12:14:24.093[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 12:14:27.121[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 12:14:27.122[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 12:14:27.124[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 12:14:27.125[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 12:14:27.126[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 12:14:27.128[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 12:14:27.128[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 12:14:27.129[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 12:14:27.129[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 12:14:27.130[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 12:14:27.132[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 12:14:27.133[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 12:14:27.184[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 12:14:27.191[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 12:14:27.191[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 12:14:27.193[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 12:14:27.193[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:09:44.161[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:09:45.080[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:09:45.406[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:09:45.523[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:09:45.569[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:09:45.615[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:09:45.627[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:09:45.642[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:09:46.910[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:09:46.913[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:09:46.955[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-11 16:09:47.179[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.179[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.179[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.179[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.179[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.180[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.181[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.182[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.183[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.184[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:09:47.184[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:09:47.293[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:09:47.315[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:09:47.365[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:09:47.365[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:09:47.365[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:09:47.387[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:09:47.388[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:09:47.389[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:09:47.434[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:09:47.813[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:09:47.816[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:09:47.828[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:09:47.837[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:09:47.837[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:09:47.838[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:09:47.838[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:09:47.838[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:09:47.838[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:09:47.839[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:09:47.839[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:09:48.270[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:09:48.278[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1ef087d2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:09:48.291[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:09:48.611[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:09:48.622[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:09:48.623[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:09:48.623[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:09:48.766[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:09:48.766[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3100 ms
2023-04-11 16:09:49.012[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:09:49.539[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:09:49.543[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:09:49.545[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:09:49.546[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:09:49.546[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:09:49.546[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:09:49.546[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:09:52.019[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:09:52.867[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:09:52.869[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:09:52.869[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:09:53.428[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:09:53.789[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:09:53.790[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:09:53.821[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@786e2c5e, org.springframework.security.web.context.SecurityContextPersistenceFilter@62a89e92, org.springframework.security.web.header.HeaderWriterFilter@8497c23, org.springframework.security.web.authentication.logout.LogoutFilter@7d14efc, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@6bafe3b0), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1fcef7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e293c35, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75452b01, org.springframework.security.web.session.SessionManagementFilter@670e6fa3, org.springframework.security.web.access.ExceptionTranslationFilter@2100d881]
2023-04-11 16:09:53.838[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@592e315e, org.springframework.security.web.context.SecurityContextPersistenceFilter@4434eb51, org.springframework.security.web.header.HeaderWriterFilter@6314e967, org.springframework.security.web.authentication.logout.LogoutFilter@7a6ade2c, loyalty.activity.service.common.security.UserTokenFilter@f7a8a7b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d0b4b69, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5910c67e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1dfd39fe, org.springframework.security.web.session.SessionManagementFilter@48611ad4, org.springframework.security.web.access.ExceptionTranslationFilter@53115ee0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6a40e660]
2023-04-11 16:09:54.036[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:09:54.189[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:09:54.580[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:09:55.352[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:09:55.358[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:09:55.361[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:09:55.382[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:09:55.514[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:09:55.515[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:09:55.517[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2023-04-11 16:09:55.833[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:09:55.835[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:09:55.881[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:09:56.624[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.465 seconds (JVM running for 14.406)
2023-04-11 16:09:56.634[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:09:56.636[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:09:56.636[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:09:56.636[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:09:56.637[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:09:56.637[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:10:06.467[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:10:06.468[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:10:36.038[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:10:36.038[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:10:36.038[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:10:36.039[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:10:36.329[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:10:36.340[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:10:36.340[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:10:36.340[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:10:36.386[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:10:36.387[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:10:39.407[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:10:39.408[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:10:42.448[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:10:45.475[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:10:45.476[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:10:45.476[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:10:45.477[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:10:45.477[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:10:45.477[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:10:45.477[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:10:45.477[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:10:45.479[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:10:45.479[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:10:45.479[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:10:45.479[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:10:45.502[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:10:45.504[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:10:45.505[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:10:45.505[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:10:45.506[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:11:07.224[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:11:08.106[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:11:08.428[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:11:08.555[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:11:08.607[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:11:08.654[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:11:08.664[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:11:08.686[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:11:10.012[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:11:10.015[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:11:10.060[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2023-04-11 16:11:10.305[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.305[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.305[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.305[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.305[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.306[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.307[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.308[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.309[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.309[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.309[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.309[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:11:10.309[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:11:10.421[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:11:10.443[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:11:10.493[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:11:10.493[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:11:10.494[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:11:10.515[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:11:10.515[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:11:10.515[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:11:10.515[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:11:10.516[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:11:10.565[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:11:10.973[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:11:10.976[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:11:10.987[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:11:10.996[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:11:10.997[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:11:10.997[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:11:10.997[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:11:10.997[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:11:10.997[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:11:10.999[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:11:10.999[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:11:11.429[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:11:11.435[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$93bc45d7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:11:11.450[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:11:11.776[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:11:11.786[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:11:11.787[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:11:11.787[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:11:11.919[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:11:11.920[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3219 ms
2023-04-11 16:11:12.143[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:11:12.627[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:11:12.632[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:11:12.635[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:11:12.635[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:11:12.636[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:11:12.636[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:11:12.636[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:11:15.063[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:11:15.878[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:11:15.878[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:11:15.878[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:11:16.411[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:11:16.776[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:11:16.814[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5086e144, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d2b49b2, org.springframework.security.web.header.HeaderWriterFilter@42000298, org.springframework.security.web.authentication.logout.LogoutFilter@24c4370a, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@633328d3), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55c901b2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6bc45472, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@557fe039, org.springframework.security.web.session.SessionManagementFilter@2a12048d, org.springframework.security.web.access.ExceptionTranslationFilter@6ecf239d]
2023-04-11 16:11:16.831[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@771deee6, org.springframework.security.web.context.SecurityContextPersistenceFilter@40ef1198, org.springframework.security.web.header.HeaderWriterFilter@7d2ccd7f, org.springframework.security.web.authentication.logout.LogoutFilter@f41db7f, loyalty.activity.service.common.security.UserTokenFilter@345effd7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72161dbb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69ca71b3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b9750f0, org.springframework.security.web.session.SessionManagementFilter@7ae49d52, org.springframework.security.web.access.ExceptionTranslationFilter@3470c771, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6fca831e]
2023-04-11 16:11:17.055[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:11:17.238[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:11:17.623[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:11:18.443[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:11:18.448[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:11:18.450[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:11:18.471[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:11:18.553[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:11:18.553[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:11:18.555[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2023-04-11 16:11:18.988[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:11:18.989[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:11:19.034[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:11:19.909[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.674 seconds (JVM running for 14.652)
2023-04-11 16:11:19.919[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:11:19.920[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:11:19.921[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:11:19.921[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:11:19.921[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:11:19.921[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:11:29.554[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:11:29.555[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:13:55.647[http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/sync】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":ActivitySyncRequest(activityList=[ActivitySyncRequest.ActivityInfo(activityCode=WOCzhongqing2023040900018, topic=如何选择奶粉, activityType=高端妈妈班, consumerActivityType=中型活动, storeCode=123;56, eventDate=null, eventStartDate=2023-04-10 12:00, eventEndDate=2023-04-20 8:00, activityStatus=Active, ownerId=123456, ownerName=谢辉, province=广东, city=广州, address=测试地址, place=南方医院, longitude=20.2356, latitude=12.236, limitCount=null, isCorrectData=true, courseName=店内课程, hotline=888888, expertName=大神123, applicantName=小米, applicantMobile=13800138000)])】
2023-04-11 16:13:59.799[http-nio-9072-exec-2] [loyalty.activity.service.external.service.CBYService:139] [INFO] - Insert 0 line pclass data ： []
2023-04-11 16:14:00.872[http-nio-9072-exec-2] [loyalty.activity.service.external.service.CBYService:139] [INFO] - Update 1 line pclass data ： [{"activity":{"id":264461,"channel":"OUTSIDE_PCLASS","topic":"高端妈妈班","startTime":"Apr 10, 2023 12:00:00 PM","endTime":"Apr 20, 2023 8:00:00 AM","ownerId":"123456","isEnabled":true,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"activityAddress":{"activityId":264461,"province":"广东","city":"广州","address":"测试地址","place":"南方医院","longitude":20.2356,"latitude":12.236,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"pclassInfo":{"id":"WOCzhongqing2023040900018","pclassId":"WOCzhongqing2023040900018","activityId":264461,"classesCode":"WOCzhongqing2023040900018","ownerName":"谢辉","ownerMobile":"13800138000","ownerEmployeeNum":"123456","director":"小米","directorMobile":"13800138000","directorExmployeeNum":"123456","isEnabled":true,"expertName":"大神123","hotline":"888888","pclassType":"中型活动","pclassProperty":"MJT","pclassType2":"高端妈妈班","courseName":"店内课程","ncCode":"123;56","status":"Active","activityType":"高端妈妈班","actionType":"UPDATE","isDeleted":false,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"activityOwnerRel":{"activityId":264461,"userId":"123456","isOwner":true,"createdByUser":1,"updatedByUser":1,"creator":"loyalty-activity-service","updater":"loyalty-activity-service"},"operateType":"UPDATE","isCorrectData":true}]
2023-04-11 16:14:00.965[http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/cby/sync】 with exectime=【5341】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={ActivitySyncResponse(errorDataList=[], failSize=0, successActivityCodes=[WOCzhongqing2023040900018])}】
2023-04-11 16:18:39.721[http-nio-9072-exec-10] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassListByCity】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":GetPclassListByCityRequest(city=广州市, cellphone=13622751449, pnecId=null, listType=SIGN_UP)】
2023-04-11 16:18:39.950[http-nio-9072-exec-10] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/pclass/info/getPclassListByCity】 with user=【-1】, exectime=【229】, params=【"arg0":GetPclassListByCityRequest(city=广州市, cellphone=13622751449, pnecId=null, listType=SIGN_UP)】
2023-04-11 16:18:39.954[http-nio-9072-exec-10] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/pclass/info/getPclassListByCity 
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'tmp_count' at line 31
### The error may exist in loyalty/activity/service/pclass/db/mapper/ActivityQueryMapper.java (best guess)
### The error may involve loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper.getListByCity-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (  SELECT t2.id,        t2.channel,        t2.topic,        t2.content,        t2.remark,        t2.start_time,        t2.end_time,        t2.owner_id,        t2.is_enabled,        t3.province,        t3.city,        t3.address,        t3.place,        t3.longitude,        t3.latitude,        t1.classes_code,        t1.expert_name,        t1.expert_introduce,        t1.course_name,        t1.hotline,        t1.is_deleted,        t1.pclass_id FROM pclass_info t1        INNER JOIN activity t2 ON t2.id = t1.activity_id        INNER JOIN activity_address t3 ON t3.activity_id = t2.id  WHERE  t3.city LIKE CONCAT('%', ?, '%')    AND t2.is_enabled = TRUE    AND t1.is_deleted = FALSE    AND (t1.pclass_property='MJT' or t1.pclass_type in (                  ?       ,           ?       ,           ?       )              AND date(t2.start_time) > date(?)  ) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'tmp_count' at line 31
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'tmp_count' at line 31
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy135.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy175.getListByCity(Unknown Source)
	at loyalty.activity.service.pclass.service.PclassInfoService.getPclassListByCity(PclassInfoService.java:74)
	at loyalty.activity.service.pclass.controller.PclassInfoController.getPclassListByCity(PclassInfoController.java:48)
	at loyalty.activity.service.pclass.controller.PclassInfoController$$FastClassBySpringCGLIB$$8af1cce.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at loyalty.activity.service.pclass.controller.PclassInfoController$$EnhancerBySpringCGLIB$$e761dd96.getPclassListByCity(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'tmp_count' at line 31
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy255.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:166)
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:157)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:100)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy253.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 123 common frames omitted
2023-04-11 16:22:06.779[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:22:06.779[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:22:06.779[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:22:06.779[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:22:07.086[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:22:07.095[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:22:07.097[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:22:07.097[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:22:07.143[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:22:07.143[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:22:10.177[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:22:10.178[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:22:13.215[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:22:16.253[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:22:16.254[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:22:16.255[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:22:16.255[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:22:16.255[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:22:16.257[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:22:16.257[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:22:16.257[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:22:16.258[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:22:16.258[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:22:16.258[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:22:16.259[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:22:16.340[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:22:16.347[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:22:16.347[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:22:16.348[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:22:16.348[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:24:33.758[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:24:34.697[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:24:35.048[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:24:35.162[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:24:35.208[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:24:35.257[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:24:35.272[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:24:35.293[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:24:37.277[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:24:37.282[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:24:37.357[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2023-04-11 16:24:37.760[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.777[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.778[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.778[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.778[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.779[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.779[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.779[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.780[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.781[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.781[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.781[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.782[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.782[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.782[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.782[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.783[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.783[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.783[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.783[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.784[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.785[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.785[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.786[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.786[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.786[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.786[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.787[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.787[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.787[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:24:37.788[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:24:38.051[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:24:38.089[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:24:38.180[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:24:38.180[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:24:38.180[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:24:38.204[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:24:38.205[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:24:38.206[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:24:38.249[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:24:39.203[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:24:39.208[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:24:39.234[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:24:39.246[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:24:39.247[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:24:39.247[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:24:39.247[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:24:39.248[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:24:39.248[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:24:39.249[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:24:39.250[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:24:40.111[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:24:40.122[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$3ac91f85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:24:40.143[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:24:40.681[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:24:40.696[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:24:40.696[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:24:40.697[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:24:40.866[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:24:40.866[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 5538 ms
2023-04-11 16:24:41.162[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:24:41.808[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:24:41.814[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:24:41.817[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:24:41.817[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:24:41.818[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:24:41.818[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:24:41.818[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:24:45.075[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:24:46.016[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:24:46.017[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:24:46.017[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:24:46.587[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:24:46.923[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:24:46.924[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:24:46.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2541b4d3, org.springframework.security.web.context.SecurityContextPersistenceFilter@1786e49f, org.springframework.security.web.header.HeaderWriterFilter@3d41881, org.springframework.security.web.authentication.logout.LogoutFilter@6ca9f1d0, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@2c841ac4), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@42a698bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7b50e70a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6c5e6da, org.springframework.security.web.session.SessionManagementFilter@111ae918, org.springframework.security.web.access.ExceptionTranslationFilter@1104cc93]
2023-04-11 16:24:46.967[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@70b437bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@7eb370c5, org.springframework.security.web.header.HeaderWriterFilter@283074e8, org.springframework.security.web.authentication.logout.LogoutFilter@6cc9f35d, loyalty.activity.service.common.security.UserTokenFilter@f1b0658, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@26c24e5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@29509774, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4870d2e1, org.springframework.security.web.session.SessionManagementFilter@7d0bbc49, org.springframework.security.web.access.ExceptionTranslationFilter@4467088e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7aabbe81]
2023-04-11 16:24:47.159[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:24:47.332[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:24:47.767[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:24:48.563[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:24:48.570[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:24:48.572[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:24:48.594[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:24:49.010[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:24:49.011[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:24:49.058[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:24:49.622[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:24:49.623[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:24:49.768[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 17.024 seconds (JVM running for 17.973)
2023-04-11 16:24:49.777[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:24:49.778[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:24:49.778[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:24:49.778[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:24:49.778[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:24:49.779[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:25:09.072[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:25:09.072[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:25:09.074[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-04-11 16:25:09.233[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassListByCity】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":GetPclassListByCityRequest(city=广州市, cellphone=13622751449, pnecId=null, listType=SIGN_UP)】
2023-04-11 16:25:10.444[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/info/getPclassListByCity】 with exectime=【1225】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PageResponse(currentPageNumber=1, count=1, results=[PclassInfoResponse(address=测试地址, city=广州, classesCode=WOCzhongqing2023040900058, content=null, courseName=高端妈妈班, endTime=Thu Apr 20 08:00:00 CST 2023, expertIntroduce=null, expertName=大神123, hotline=888888, id=264452, isSignIn=false, isSignUp=false, isDeleted=false, isEnabled=true, ownerId=80406, place=南方医院, province=广东, remark=null, startTime=Wed Apr 12 12:00:00 CST 2023, topic=高端妈妈班, pclassId=WOCzhongqing2023040900058, latitude=23.0940820, longitude=113.2567440)])}】
2023-04-11 16:25:19.017[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:25:19.017[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:25:19.018[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:25:19.018[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:25:19.318[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:25:19.327[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:25:19.327[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:25:19.327[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:25:19.373[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:25:19.374[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:25:19.375[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:25:19.376[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:25:19.787[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:25:22.812[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:25:22.812[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:25:22.812[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:25:22.812[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:25:22.812[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:25:22.813[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:25:22.813[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:25:22.813[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:25:22.814[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:25:22.814[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:25:22.814[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:25:22.814[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:25:22.839[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:25:22.846[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:25:22.847[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:25:22.848[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:25:22.848[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:33:28.017[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:33:28.944[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:33:29.276[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:33:29.395[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:33:29.444[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:33:29.491[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:33:29.503[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:33:29.521[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:33:30.757[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:33:30.762[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:33:30.806[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2023-04-11 16:33:31.050[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.050[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.050[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.050[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.050[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.051[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.052[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.053[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.054[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:33:31.054[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:33:31.163[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:33:31.197[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:33:31.236[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:33:31.236[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:33:31.236[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:33:31.258[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:33:31.258[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:33:31.258[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:33:31.259[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:33:31.260[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:33:31.304[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:33:31.693[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:33:31.695[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:33:31.708[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:33:31.716[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:33:31.718[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:33:31.718[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:33:31.718[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:33:31.718[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:33:31.719[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:33:31.719[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:33:31.720[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:33:32.151[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:33:32.158[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$93bc45d7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:33:32.173[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:33:32.490[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:33:32.500[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:33:32.501[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:33:32.501[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:33:32.624[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:33:32.625[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3074 ms
2023-04-11 16:33:32.842[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:33:33.299[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:33:33.302[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:33:33.304[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:33:33.304[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:33:33.305[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:33:33.305[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:33:33.305[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:33:35.758[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:33:36.620[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:33:36.621[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:33:36.621[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:33:37.157[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:33:37.502[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:33:37.502[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:33:37.502[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:33:37.502[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:33:37.502[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:33:37.503[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:33:37.503[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:33:37.503[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:33:37.530[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7f1581bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@709c41da, org.springframework.security.web.header.HeaderWriterFilter@66859669, org.springframework.security.web.authentication.logout.LogoutFilter@7ec63b9a, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@5180f430), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2541b4d3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@29509774, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@30407f28, org.springframework.security.web.session.SessionManagementFilter@2df6d461, org.springframework.security.web.access.ExceptionTranslationFilter@64aab3fd]
2023-04-11 16:33:37.546[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@161974d1, org.springframework.security.web.context.SecurityContextPersistenceFilter@10cb1b89, org.springframework.security.web.header.HeaderWriterFilter@19305a7c, org.springframework.security.web.authentication.logout.LogoutFilter@4ab1ff80, loyalty.activity.service.common.security.UserTokenFilter@15d18b61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@19b7852b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@24fcfdb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2fba15c5, org.springframework.security.web.session.SessionManagementFilter@7e0fc5d0, org.springframework.security.web.access.ExceptionTranslationFilter@563bf9d2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7bb6a1f4]
2023-04-11 16:33:37.735[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:33:37.902[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:33:38.321[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:33:39.114[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:33:39.119[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:33:39.122[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:33:39.144[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:33:39.568[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:33:39.569[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:33:39.615[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:33:40.172[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:33:40.174[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:33:40.337[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.315 seconds (JVM running for 14.273)
2023-04-11 16:33:40.346[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:33:40.347[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:33:40.348[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:33:40.348[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:33:40.348[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:33:40.348[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:34:50.377[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:34:50.377[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:34:50.381[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 4 ms
2023-04-11 16:34:50.449[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/getData】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":WOCzhongqing2023041000016】
2023-04-11 16:34:51.222[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/cby/getData】 with exectime=【787】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PNECClassInfoDataDto(signUpNum=0, expectedAttendance=0, signInNum=0, getGiftNum=0)}】
2023-04-11 16:35:08.641[http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/info/getPclassById】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":WOCzhongqing2023041000016】
2023-04-11 16:35:08.696[http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/info/getPclassById】 with exectime=【55】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PclassInfoResponse(address=测试地址, city=广州, classesCode=WOCzhongqing2023041000016, content=null, courseName=精品妈妈班, endTime=Thu Apr 20 08:00:00 CST 2023, expertIntroduce=null, expertName=大神123, hotline=888888, id=264453, isSignIn=false, isSignUp=false, isDeleted=false, isEnabled=true, ownerId=80406, place=南方医院, province=广东, remark=null, startTime=Mon Apr 10 12:00:00 CST 2023, topic=精品妈妈班(月子中心), pclassId=WOCzhongqing2023041000016, latitude=23.0940820, longitude=113.2567440)}】
2023-04-11 16:35:43.940[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:35:43.940[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:35:43.941[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:35:43.943[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:35:44.227[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:35:44.238[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:35:44.238[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:35:44.238[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:35:44.288[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:35:44.289[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:35:45.921[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:35:45.922[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:35:48.922[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:35:51.948[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:35:51.949[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:35:51.949[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:35:51.950[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:35:51.950[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:35:51.951[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:35:51.951[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:35:51.951[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:35:51.951[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:35:51.952[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:35:51.954[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:35:51.955[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:35:52.014[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:35:52.020[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:35:52.020[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:35:52.021[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:35:52.021[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:48:00.903[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:48:01.787[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:48:02.107[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:48:02.227[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:48:02.279[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:48:02.326[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:48:02.341[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:48:02.373[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:48:03.597[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:48:03.600[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:48:03.641[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-11 16:48:03.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.864[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.865[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.866[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:48:03.867[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:48:03.982[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:48:04.006[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:48:04.056[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:48:04.057[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:48:04.057[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:48:04.078[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:48:04.079[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:48:04.079[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:48:04.079[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:48:04.079[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:48:04.125[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:48:04.512[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:48:04.514[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:48:04.527[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:48:04.535[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:48:04.536[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:48:04.536[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:48:04.536[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:48:04.536[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:48:04.536[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:48:04.537[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:48:04.538[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:48:04.972[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:48:04.978[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$ab2134d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:48:04.992[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:48:05.347[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:48:05.358[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:48:05.359[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:48:05.359[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:48:05.499[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:48:05.501[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3109 ms
2023-04-11 16:48:05.782[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:48:06.366[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:48:06.371[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:48:06.374[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:48:06.374[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:48:06.374[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:48:06.374[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:48:06.374[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:48:08.973[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:48:09.885[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:48:09.886[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:48:09.887[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:48:10.411[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:48:10.751[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:48:10.751[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:48:10.751[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:48:10.751[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:48:10.752[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:48:10.752[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:48:10.752[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:48:10.752[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:48:10.779[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2df6d461, org.springframework.security.web.context.SecurityContextPersistenceFilter@266b7d1, org.springframework.security.web.header.HeaderWriterFilter@72cf43fa, org.springframework.security.web.authentication.logout.LogoutFilter@3b802baa, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@3435b6d8), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@111ae918, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7e047d85, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1791670e, org.springframework.security.web.session.SessionManagementFilter@4ae93e38, org.springframework.security.web.access.ExceptionTranslationFilter@781befe7]
2023-04-11 16:48:10.799[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@72e806, org.springframework.security.web.context.SecurityContextPersistenceFilter@2541b4d3, org.springframework.security.web.header.HeaderWriterFilter@4a467f08, org.springframework.security.web.authentication.logout.LogoutFilter@6d3dc0c5, loyalty.activity.service.common.security.UserTokenFilter@270a113f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c5e6da, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7f027735, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3dc976da, org.springframework.security.web.session.SessionManagementFilter@3741a170, org.springframework.security.web.access.ExceptionTranslationFilter@6ca9f1d0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@76a90ecb]
2023-04-11 16:48:11.009[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:48:11.175[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:48:11.612[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:48:12.423[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:48:12.429[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:48:12.431[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:48:12.452[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:48:12.871[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:48:12.872[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:48:12.918[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:48:13.489[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:48:13.491[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 16:48:13.694[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.793 seconds (JVM running for 14.753)
2023-04-11 16:48:13.704[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:48:13.705[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:48:13.705[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:48:13.705[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:48:13.705[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:48:13.705[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:48:16.614[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:48:16.615[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:48:16.617[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-04-11 16:48:33.111[http-nio-9072-exec-6] [org.apache.juli.logging.DirectJDKLog:175] [ERROR] - Servlet.service() for servlet [dispatcherServlet] in context with path [/loyalty-activity-service] threw exception
org.springframework.security.authentication.BadCredentialsException: Bad credentials
	at loyalty.activity.service.common.security.CommonAppCallAuthenticationProvider.authenticate(CommonAppCallAuthenticationProvider.java:62)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at loyalty.activity.service.common.security.QywxCodeTokenFilter.doFilterInternal(QywxCodeTokenFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2023-04-11 16:48:47.413[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:48:47.413[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:48:47.413[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:48:47.414[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:48:47.700[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:48:47.710[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:48:47.710[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:48:47.710[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:48:47.755[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:48:47.756[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:48:48.264[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:48:48.265[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:48:51.293[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:48:54.310[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:48:54.311[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:48:54.311[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:48:54.311[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:48:54.311[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:48:54.312[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:48:54.312[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:48:54.312[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:48:54.313[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:48:54.313[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:48:54.313[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:48:54.313[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:48:54.346[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:48:54.354[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:48:54.355[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:48:54.357[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:48:54.358[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 16:57:18.801[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 16:57:19.705[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 16:57:20.059[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 16:57:20.174[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 16:57:20.220[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 16:57:20.268[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 16:57:20.280[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 16:57:20.296[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 16:57:21.584[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 16:57:21.587[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 16:57:21.629[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2023-04-11 16:57:21.859[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.859[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.859[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.859[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.860[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.861[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.862[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.863[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 16:57:21.864[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 16:57:21.985[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 16:57:22.009[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 16:57:22.060[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:57:22.060[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:57:22.061[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 16:57:22.082[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 16:57:22.129[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 16:57:22.530[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 16:57:22.533[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 16:57:22.545[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 16:57:22.555[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 16:57:22.556[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 16:57:22.556[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 16:57:22.556[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 16:57:22.556[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 16:57:22.556[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 16:57:22.557[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 16:57:22.558[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 16:57:23.015[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:57:23.023[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$647a0915] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:57:23.042[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 16:57:23.392[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 16:57:23.404[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 16:57:23.404[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 16:57:23.405[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 16:57:23.534[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 16:57:23.534[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3213 ms
2023-04-11 16:57:23.764[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 16:57:24.264[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 16:57:24.269[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 16:57:24.270[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 16:57:24.270[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 16:57:24.271[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 16:57:24.271[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 16:57:24.271[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 16:57:26.824[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 16:57:27.654[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 16:57:27.654[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 16:57:27.655[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 16:57:28.226[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 16:57:28.575[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 16:57:28.576[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 16:57:28.576[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 16:57:28.576[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 16:57:28.576[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 16:57:28.577[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 16:57:28.577[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 16:57:28.577[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 16:57:28.605[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2be89ba5, org.springframework.security.web.context.SecurityContextPersistenceFilter@495cac83, org.springframework.security.web.header.HeaderWriterFilter@f7a8a7b, org.springframework.security.web.authentication.logout.LogoutFilter@3cdafb82, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@3f808ad9), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@297db6ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3a02172a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@408573b1, org.springframework.security.web.session.SessionManagementFilter@23938caa, org.springframework.security.web.access.ExceptionTranslationFilter@4620e669]
2023-04-11 16:57:28.621[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@21dd405a, org.springframework.security.web.context.SecurityContextPersistenceFilter@17d3bf09, org.springframework.security.web.header.HeaderWriterFilter@2c4142f8, org.springframework.security.web.authentication.logout.LogoutFilter@1e250539, loyalty.activity.service.common.security.UserTokenFilter@31d11998, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a49995f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2cb95c6c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6ee0ef34, org.springframework.security.web.session.SessionManagementFilter@7ef8cfbb, org.springframework.security.web.access.ExceptionTranslationFilter@47db5b8f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2197990b]
2023-04-11 16:57:28.834[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 16:57:29.000[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:57:29.503[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 16:57:30.299[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:57:30.305[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:57:30.308[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 16:57:30.329[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 16:57:30.560[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 16:57:30.561[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 16:57:30.562[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-04-11 16:57:30.757[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 16:57:30.758[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 16:57:30.804[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 16:57:31.363[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:57:31.364[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:57:31.534[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.71 seconds (JVM running for 14.671)
2023-04-11 16:57:31.542[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:57:31.543[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:57:31.544[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:57:31.544[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:57:31.544[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 16:57:31.544[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 16:57:42.861[http-nio-9072-exec-10] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/cby/getDetail】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":SOCzhangsha2023040900075】
2023-04-11 16:58:01.136[http-nio-9072-exec-10] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/cby/getDetail】 with exectime=【18290】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={PNECClassInfoDetailDto(activityId=264451, status=执行中, classTime=04月11日 12:00 - 04月11日 23:00, classCode=SOCzhangsha2023040900075, city=广州, topic=高端妈妈班, pclassType2=高端妈妈班, place=南方医院, qrcodeUrl=https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQEh8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyVmUxMHg1eXljbWwxcTJ3c2hBY2UAAgSCEzVkAwQAjScA, startFlag=true)}】
2023-04-11 16:58:04.355[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 16:58:04.355[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 16:58:04.356[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 16:58:04.356[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 16:58:04.637[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 16:58:04.646[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 16:58:04.647[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 16:58:04.647[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 16:58:04.693[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 16:58:04.694[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 16:58:06.637[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 16:58:06.638[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 16:58:07.148[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:58:07.151[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2023-04-11 16:58:07.154[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 16:58:10.190[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 16:58:10.190[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 16:58:10.191[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 16:58:10.191[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 16:58:10.191[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 16:58:10.192[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 16:58:10.192[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 16:58:10.193[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 16:58:10.193[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 16:58:10.193[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 16:58:10.193[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 16:58:10.194[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 16:58:10.221[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 16:58:10.230[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 16:58:10.230[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 16:58:10.231[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 16:58:10.232[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 18:10:38.104[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 18:10:39.140[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 18:10:39.471[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 18:10:39.597[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 18:10:39.648[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 18:10:39.695[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 18:10:39.727[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 18:10:39.760[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 18:10:41.067[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 18:10:41.071[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 18:10:41.117[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.346[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.347[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.348[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.349[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.349[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.349[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.350[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.350[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.350[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.350[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.350[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:10:41.351[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 18:10:41.512[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 18:10:41.536[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 18:10:41.576[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:10:41.576[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:10:41.576[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:10:41.601[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 18:10:41.601[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:10:41.601[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:10:41.601[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:10:41.602[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:10:41.652[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 18:10:42.085[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 18:10:42.088[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 18:10:42.100[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 18:10:42.110[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 18:10:42.111[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 18:10:42.111[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 18:10:42.111[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 18:10:42.111[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 18:10:42.112[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 18:10:42.113[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 18:10:42.113[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 18:10:42.549[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:10:42.555[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1e63ae40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:10:42.569[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:10:42.944[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 18:10:42.956[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 18:10:42.957[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 18:10:42.957[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 18:10:43.096[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 18:10:43.096[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3315 ms
2023-04-11 18:10:43.331[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 18:10:44.054[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 18:10:44.058[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 18:10:44.061[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 18:10:44.062[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 18:10:44.062[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 18:10:44.063[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 18:10:44.063[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 18:10:46.592[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 18:10:47.498[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 18:10:47.499[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 18:10:47.499[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 18:10:48.103[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 18:10:48.459[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 18:10:48.460[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 18:10:48.487[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5854e526, org.springframework.security.web.context.SecurityContextPersistenceFilter@2b0df4ba, org.springframework.security.web.header.HeaderWriterFilter@29511cf9, org.springframework.security.web.authentication.logout.LogoutFilter@236a6a2a, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@6965c32), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4be171d1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59881424, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@289fff3c, org.springframework.security.web.session.SessionManagementFilter@168ffb0, org.springframework.security.web.access.ExceptionTranslationFilter@717b4de2]
2023-04-11 18:10:48.503[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@57421e4e, org.springframework.security.web.context.SecurityContextPersistenceFilter@1bc80978, org.springframework.security.web.header.HeaderWriterFilter@718198db, org.springframework.security.web.authentication.logout.LogoutFilter@2c78771b, loyalty.activity.service.common.security.UserTokenFilter@5a75bad4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c8746a0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@613d42ab, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@77227b1f, org.springframework.security.web.session.SessionManagementFilter@40bac624, org.springframework.security.web.access.ExceptionTranslationFilter@4e293c35, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3b25ce5e]
2023-04-11 18:10:48.681[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 18:10:48.834[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:10:49.293[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 18:10:50.145[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:10:50.151[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:10:50.154[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 18:10:50.175[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 18:10:50.610[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 18:10:50.611[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 18:10:50.657[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 18:10:51.416[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 14.349 seconds (JVM running for 15.281)
2023-04-11 18:10:51.425[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:10:51.426[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:10:51.426[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:10:51.427[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:10:51.427[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:10:51.427[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:11:01.254[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:01.259[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:20.754[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 18:11:20.756[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 18:11:20.756[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 18:11:20.756[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 18:11:21.049[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:11:21.059[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 18:11:21.059[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 18:11:21.059[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 18:11:21.105[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 18:11:21.106[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 18:11:24.128[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 18:11:24.130[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 18:11:27.164[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 18:11:30.189[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 18:11:30.190[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 18:11:30.190[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 18:11:30.191[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 18:11:30.191[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 18:11:30.192[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 18:11:30.192[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 18:11:30.193[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 18:11:30.193[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 18:11:30.193[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 18:11:30.193[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 18:11:30.194[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 18:11:30.227[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 18:11:30.230[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 18:11:30.230[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 18:11:30.230[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 18:11:30.230[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 18:11:34.553[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 18:11:35.429[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 18:11:35.746[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 18:11:35.865[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 18:11:35.911[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 18:11:35.959[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 18:11:35.971[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 18:11:35.988[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 18:11:37.216[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 18:11:37.219[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 18:11:37.261[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2023-04-11 18:11:37.483[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.483[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.483[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.483[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.483[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.484[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.485[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.486[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.487[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.487[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:11:37.487[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 18:11:37.624[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 18:11:37.651[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 18:11:37.690[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:11:37.690[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:11:37.690[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:11:37.719[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 18:11:37.719[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:11:37.719[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:11:37.719[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:11:37.720[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:11:37.779[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 18:11:38.160[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 18:11:38.163[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 18:11:38.175[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 18:11:38.184[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 18:11:38.185[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 18:11:38.185[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 18:11:38.185[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 18:11:38.185[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 18:11:38.186[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 18:11:38.186[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 18:11:38.187[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 18:11:38.623[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:11:38.632[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$dadb9c55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:11:38.647[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:11:38.997[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 18:11:39.006[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 18:11:39.007[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 18:11:39.007[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 18:11:39.136[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 18:11:39.136[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3122 ms
2023-04-11 18:11:39.349[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 18:11:39.810[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 18:11:39.814[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 18:11:39.817[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 18:11:39.817[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 18:11:39.817[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 18:11:39.817[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 18:11:39.818[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 18:11:42.214[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 18:11:43.056[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 18:11:43.057[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 18:11:43.057[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 18:11:43.614[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 18:11:43.951[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 18:11:43.977[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5b6789cf, org.springframework.security.web.context.SecurityContextPersistenceFilter@3b802baa, org.springframework.security.web.header.HeaderWriterFilter@58192546, org.springframework.security.web.authentication.logout.LogoutFilter@17179a7e, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@63c07eaf), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1791670e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@45f20384, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@70882cb7, org.springframework.security.web.session.SessionManagementFilter@1fb4377d, org.springframework.security.web.access.ExceptionTranslationFilter@6b6b4177]
2023-04-11 18:11:43.993[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@25bd8eab, org.springframework.security.web.context.SecurityContextPersistenceFilter@30407f28, org.springframework.security.web.header.HeaderWriterFilter@21c7415e, org.springframework.security.web.authentication.logout.LogoutFilter@563bf9d2, loyalty.activity.service.common.security.UserTokenFilter@15974f4e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2dac4d04, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@367a245e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@38911759, org.springframework.security.web.session.SessionManagementFilter@7f1581bb, org.springframework.security.web.access.ExceptionTranslationFilter@4f1a4d61, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2d094add]
2023-04-11 18:11:44.188[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 18:11:44.343[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:11:44.792[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 18:11:45.596[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:45.602[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:45.604[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 18:11:45.624[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 18:11:46.042[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 18:11:46.043[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 18:11:46.093[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 18:11:46.679[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:46.680[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:11:46.850[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.292 seconds (JVM running for 14.242)
2023-04-11 18:11:46.859[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:11:46.860[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:11:46.860[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:11:46.860[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:11:46.860[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:11:46.860[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:11:47.985[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 18:11:47.985[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 18:11:47.987[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2023-04-11 18:12:03.578[http-nio-9072-exec-6] [loyalty.activity.service.common.controller.BaseExceptionController:21] [ERROR] - Error occurred while execute /loyalty-activity-service/pclass/signUp/userApply 
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.Date` from String "Sun Apr 11 08:00:00 CST 2021": not a valid representation (error: Failed to parse Date value 'Sun Apr 11 08:00:00 CST 2021': Cannot parse date "Sun Apr 11 08:00:00 CST 2021": not compatible with any of standard forms ("yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSS", "EEE, dd MMM yyyy HH:mm:ss zzz", "yyyy-MM-dd")); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "Sun Apr 11 08:00:00 CST 2021": not a valid representation (error: Failed to parse Date value 'Sun Apr 11 08:00:00 CST 2021': Cannot parse date "Sun Apr 11 08:00:00 CST 2021": not compatible with any of standard forms ("yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSS", "EEE, dd MMM yyyy HH:mm:ss zzz", "yyyy-MM-dd"))
 at [Source: (PushbackInputStream); line: 4, column: 18] (through reference chain: loyalty.activity.service.pclass.domain.request.UserApplyRequest["babyBirthday"])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:285)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:243)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:205)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:158)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:131)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:170)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:137)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "Sun Apr 11 08:00:00 CST 2021": not a valid representation (error: Failed to parse Date value 'Sun Apr 11 08:00:00 CST 2021': Cannot parse date "Sun Apr 11 08:00:00 CST 2021": not compatible with any of standard forms ("yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSS", "EEE, dd MMM yyyy HH:mm:ss zzz", "yyyy-MM-dd"))
 at [Source: (PushbackInputStream); line: 4, column: 18] (through reference chain: loyalty.activity.service.pclass.domain.request.UserApplyRequest["babyBirthday"])
	at com.fasterxml.jackson.databind.exc.InvalidFormatException.from(InvalidFormatException.java:67)
	at com.fasterxml.jackson.databind.DeserializationContext.weirdStringException(DeserializationContext.java:1702)
	at com.fasterxml.jackson.databind.DeserializationContext.handleWeirdStringValue(DeserializationContext.java:947)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._parseDate(StdDeserializer.java:556)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._parseDate(StdDeserializer.java:497)
	at com.fasterxml.jackson.databind.deser.std.DateDeserializers$DateBasedDeserializer._parseDate(DateDeserializers.java:200)
	at com.fasterxml.jackson.databind.deser.std.DateDeserializers$DateDeserializer.deserialize(DateDeserializers.java:290)
	at com.fasterxml.jackson.databind.deser.std.DateDeserializers$DateDeserializer.deserialize(DateDeserializers.java:273)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:371)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:164)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4526)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3521)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:274)
	... 93 common frames omitted
2023-04-11 18:12:23.113[http-nio-9072-exec-8] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signUp/userApply】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserApplyRequest(activityId=264452, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:14:20.882[http-nio-9072-exec-8] [loyalty.activity.service.validator.pclass.PclassLimitValidator:24] [ERROR] - 课程签到或报名超出人数限制，param=PclassInfoBase(id=264452, startTime=Wed Apr 12 12:00:00 CST 2023, endTime=Thu Apr 20 08:00:00 CST 2023, isEnabled=true, limitCount=0, applyCount=0, status=Active, isDeleted=false, ClassCode=WOCzhongqing2023040900058, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=null, targetLongitude=null, isOnline=null)
2023-04-11 18:14:20.924[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 18:14:20.924[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 18:14:20.924[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 18:14:20.924[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 18:14:20.973[com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@loyalty-activity-service', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 18:14:20.973[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:14:20.975[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2023-04-11 18:14:20.977[http-nio-9072-exec-8] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/pclass/signUp/userApply】 with user=【-1】, exectime=【117880】, params=【"arg0":UserApplyRequest(activityId=264452, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:14:21.161[http-nio-9072-exec-8] [loyalty.activity.service.common.controller.BaseExceptionController:28] [WARN] - Internal error occurred while execute /loyalty-activity-service/pclass/signUp/userApply with errorCode=17, msg=
2023-04-11 18:14:21.234[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:14:21.244[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 18:14:21.244[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 18:14:21.244[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 18:14:21.288[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 18:14:21.289[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 18:14:24.325[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 18:14:24.329[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 18:14:27.345[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 18:14:30.379[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 18:14:30.380[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 18:14:30.380[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 18:14:30.382[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 18:14:30.382[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 18:14:30.383[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 18:14:30.384[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 18:14:30.384[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 18:14:30.385[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 18:14:30.385[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 18:14:30.386[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 18:14:30.386[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 18:14:30.427[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 18:14:30.436[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 18:14:30.436[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 18:14:30.437[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 18:14:30.437[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
2023-04-11 18:14:35.951[background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-04-11 18:14:36.835[main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-04-11 18:14:37.162[main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-04-11 18:14:37.280[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-04-11 18:14:37.327[main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-04-11 18:14:37.375[main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-04-11 18:14:37.387[main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-04-11 18:14:37.405[main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: local
2023-04-11 18:14:38.712[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-04-11 18:14:38.716[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-04-11 18:14:38.762[main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.982[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'cmpDbMapper' and 'loyalty.activity.service.pnec.db.cmpmapper.CmpDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'crmPgDbMapper' and 'loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.983[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.984[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.985[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.986[main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-04-11 18:14:38.986[main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-04-11 18:14:39.113[main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=c1fd0673-8ea0-32ed-b34f-b564b1221e8f
2023-04-11 18:14:39.136[main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-04-11 18:14:39.174[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-local.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:14:39.174[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:14:39.174[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-04-11 18:14:39.198[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-04-11 18:14:39.198[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:14:39.198[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-04-11 18:14:39.199[main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-04-11 18:14:39.246[main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-04-11 18:14:39.629[main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-04-11 18:14:39.631[main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-04-11 18:14:39.645[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-04-11 18:14:39.654[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-04-11 18:14:39.655[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-04-11 18:14:39.655[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-04-11 18:14:39.655[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-04-11 18:14:39.656[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-04-11 18:14:39.656[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-04-11 18:14:39.657[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-04-11 18:14:39.657[main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-04-11 18:14:40.089[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:14:40.096[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$354a9a0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:14:40.109[main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-04-11 18:14:40.431[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-04-11 18:14:40.442[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-04-11 18:14:40.443[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-04-11 18:14:40.443[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-04-11 18:14:40.567[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-04-11 18:14:40.568[main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3138 ms
2023-04-11 18:14:40.784[main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-04-11 18:14:41.246[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1,activity} inited
2023-04-11 18:14:41.250[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-2,cmp} inited
2023-04-11 18:14:41.251[main] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-3,crmpg} inited
2023-04-11 18:14:41.251[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [activity] success
2023-04-11 18:14:41.251[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [cmp] success
2023-04-11 18:14:41.251[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:148] [INFO] - dynamic-datasource - add a datasource named [crmpg] success
2023-04-11 18:14:41.251[main] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:228] [INFO] - dynamic-datasource initial loaded [3] datasource,primary datasource named [activity]
2023-04-11 18:14:43.691[main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-04-11 18:14:44.507[main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-04-11 18:14:44.508[main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-04-11 18:14:44.509[main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-04-11 18:14:45.051[main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-04-11 18:14:45.393[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-04-11 18:14:45.394[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-04-11 18:14:45.421[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@404a7c61, org.springframework.security.web.context.SecurityContextPersistenceFilter@62e8ef9f, org.springframework.security.web.header.HeaderWriterFilter@182ce25e, org.springframework.security.web.authentication.logout.LogoutFilter@776eb512, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@3503e68e), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@49d658bf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e03d75e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@428e875f, org.springframework.security.web.session.SessionManagementFilter@19d75d3c, org.springframework.security.web.access.ExceptionTranslationFilter@1cda3a6d]
2023-04-11 18:14:45.436[main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8be84bf, org.springframework.security.web.context.SecurityContextPersistenceFilter@24c8bc1e, org.springframework.security.web.header.HeaderWriterFilter@6149adee, org.springframework.security.web.authentication.logout.LogoutFilter@5df17e60, loyalty.activity.service.common.security.UserTokenFilter@1d5e0621, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16488ed7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3ac2772d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@aee3d6e, org.springframework.security.web.session.SessionManagementFilter@36361951, org.springframework.security.web.access.ExceptionTranslationFilter@33903444, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@530e500c]
2023-04-11 18:14:45.630[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-04-11 18:14:45.787[main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:14:46.197[main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-04-11 18:14:46.988[main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2023-04-11 18:14:46.993[main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2023-04-11 18:14:46.995[main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2023-04-11 18:14:47.016[main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2023-04-11 18:14:47.421[main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2023-04-11 18:14:47.422[main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] loyalty-activity-service registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2023-04-11 18:14:47.468[main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2023-04-11 18:14:48.053[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2023-04-11 18:14:48.055[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"*************#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*************","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2023-04-11 18:14:48.254[main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 13.288 seconds (JVM running for 14.236)
2023-04-11 18:14:48.263[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-local.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:14:48.264[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-local.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:14:48.264[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:14:48.265[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:14:48.265[main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2023-04-11 18:14:48.265[main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2023-04-11 18:14:52.922[http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-04-11 18:14:52.922[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2023-04-11 18:14:52.924[http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2023-04-11 18:14:53.107[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signUp/userApply】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserApplyRequest(activityId=264452, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:15:08.579[http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPClient:146] [INFO] - 【NCP】 sending to url https://qywork.otype.com/portal/getcontactwayqrcode with param {"code":"80406","qrcodeType":"RecruitCode","signature":"Dq98Kl03SMKt69vnEQVuiP2j72lLPC4ZIYNGPo/pIueGamJcUWzbCDmQCaVo547SZpC8L3Wp/HJ8FaouxCtAHx8KrjlNzel0YE/q8ofWnUlDRtAJoSkaROfUEniYR6KpQ4KOWIesII2W+2MJBvPTzYY+NFejKBDKONmja4/ggh0\u003d","timestamp":"1681208108559"}, header={"headers":{"Content-type":["application/json;charset\u003dUTF-8"],"signature":["B7A09024E1907505B415ADDC45A5B5EB"],"timestamp":["1681208108"],"access_key_id":["loyalty"]},"body":{"code":"80406","qrcodeType":"RecruitCode","signature":"Dq98Kl03SMKt69vnEQVuiP2j72lLPC4ZIYNGPo/pIueGamJcUWzbCDmQCaVo547SZpC8L3Wp/HJ8FaouxCtAHx8KrjlNzel0YE/q8ofWnUlDRtAJoSkaROfUEniYR6KpQ4KOWIesII2W+2MJBvPTzYY+NFejKBDKONmja4/ggh0\u003d","timestamp":"1681208108559"}}
2023-04-11 18:15:09.022[http-nio-9072-exec-1] [com.cstools.data.internal.client.NCPClient:155] [INFO] - 【NCP】 response for url https://qywork.otype.com/portal/getcontactwayqrcode is {"status":200,"headers":{"Server":["Tengine"],"Date":["Tue, 11 Apr 2023 10:15:09 GMT"],"Content-Type":["application/json; charset\u003dutf-8"],"Transfer-Encoding":["chunked"],"Connection":["keep-alive"],"Vary":["Accept-Encoding"],"X-Powered-By":["ASP.NET"],"X-Upstream-Addr":["*********:8110"]},"body":{"body":{"qrCode":"https://wework.qpic.cn/wwpic/855566_UlW1kVm8TxuOYzM_1662141743/0"},"errCode":"200","errMsg":"请求成功"}}
2023-04-11 18:15:13.349[http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/signUp/userApply】 with exectime=【20259】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={UserSignUpResponse(id=null, qrCode=https://wework.qpic.cn/wwpic/855566_UlW1kVm8TxuOYzM_1662141743/0)}】
2023-04-11 18:16:34.332[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signUp/userApply】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserApplyRequest(activityId=231287, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:16:34.478[http-nio-9072-exec-3] [loyalty.activity.service.validator.pclass.pclassSignUpValidators.PclassSignUpEffectivenessValidator:22] [ERROR] - 课程已失效（课程已结束），param=PclassInfoBase(id=231287, startTime=Tue Feb 07 09:00:00 CST 2023, endTime=Tue Feb 07 11:00:00 CST 2023, isEnabled=true, limitCount=111, applyCount=0, status=活动执行中, isDeleted=false, ClassCode=PCEOC2-20230287321, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=null, targetLongitude=null, isOnline=null)
2023-04-11 18:16:34.572[http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/pclass/signUp/userApply】 with user=【-1】, exectime=【240】, params=【"arg0":UserApplyRequest(activityId=231287, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:16:34.719[http-nio-9072-exec-3] [loyalty.activity.service.common.controller.BaseExceptionController:28] [WARN] - Internal error occurred while execute /loyalty-activity-service/pclass/signUp/userApply with errorCode=13, msg=
2023-04-11 18:19:06.512[http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signUp/userApply】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserApplyRequest(activityId=231287, attendance=2, babyBirthday=Tue Oct 12 08:00:00 CST 2021, babyStatus=1, cellphone=13622751449, inputCellphone=13622751449, latitude=23.09406494140625, longitude=113.2574378797743, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, pnecId=, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, userName=测试, remark=)】
2023-04-11 18:19:19.562[http-nio-9072-exec-6] [com.cstools.data.internal.client.NCPClient:146] [INFO] - 【NCP】 sending to url https://qywork.otype.com/portal/getcontactwayqrcode with param {"code":"843870","qrcodeType":"RecruitCode","signature":"iLWNVbeYPWnhWXQuBycgdg5JgETJh4co7cQCKFFTddz7iceff8KzX9onM14RcC+lTO8QBeUb9OLzh1VI4nXBff4WiEbOkIwrwdEC1BKhe9ZJYyALyYRXLffcUoL5RCg2n8YVeS4bnBLkGoHxiZzcMCXE59S6er07QYzEKT3nDKo\u003d","timestamp":"1681208359558"}, header={"headers":{"Content-type":["application/json;charset\u003dUTF-8"],"signature":["431DB425A2F5F41428159E0E1229C984"],"timestamp":["1681208359"],"access_key_id":["loyalty"]},"body":{"code":"843870","qrcodeType":"RecruitCode","signature":"iLWNVbeYPWnhWXQuBycgdg5JgETJh4co7cQCKFFTddz7iceff8KzX9onM14RcC+lTO8QBeUb9OLzh1VI4nXBff4WiEbOkIwrwdEC1BKhe9ZJYyALyYRXLffcUoL5RCg2n8YVeS4bnBLkGoHxiZzcMCXE59S6er07QYzEKT3nDKo\u003d","timestamp":"1681208359558"}}
2023-04-11 18:19:19.579[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-04-11 18:19:19.580[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-04-11 18:19:19.580[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-04-11 18:19:19.581[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-04-11 18:19:19.808[http-nio-9072-exec-6] [com.cstools.data.internal.client.NCPClient:155] [INFO] - 【NCP】 response for url https://qywork.otype.com/portal/getcontactwayqrcode is {"status":200,"headers":{"Server":["Tengine"],"Date":["Tue, 11 Apr 2023 10:19:19 GMT"],"Content-Type":["application/json; charset\u003dutf-8"],"Transfer-Encoding":["chunked"],"Connection":["keep-alive"],"Vary":["Accept-Encoding"],"X-Powered-By":["ASP.NET"],"X-Upstream-Addr":["*********:8110"]},"body":{"body":{"qrCode":"https://wework.qpic.cn/wwpic/572738_W1FU-xkOQA2Fep2_1652376927/0"},"errCode":"200","errMsg":"请求成功"}}
2023-04-11 18:19:20.082[http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/signUp/userApply】 with exectime=【13569】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={UserSignUpResponse(id=null, qrCode=https://wework.qpic.cn/wwpic/572738_W1FU-xkOQA2Fep2_1652376927/0)}】
2023-04-11 18:19:20.231[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-04-11 18:19:20.246[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-04-11 18:19:20.247[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-04-11 18:19:20.247[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] loyalty-activity-service deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-04-11 18:19:20.294[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-04-11 18:19:20.295[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-04-11 18:19:23.324[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-04-11 18:19:23.324[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-04-11 18:19:26.346[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-04-11 18:19:29.377[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-04-11 18:19:29.377[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-04-11 18:19:29.378[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-04-11 18:19:29.378[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-04-11 18:19:29.379[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-04-11 18:19:29.379[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-04-11 18:19:29.380[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-04-11 18:19:29.380[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-04-11 18:19:29.380[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-04-11 18:19:29.381[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-04-11 18:19:29.381[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-04-11 18:19:29.382[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2023-04-11 18:19:29.410[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:205] [INFO] - dynamic-datasource start closing ....
2023-04-11 18:19:29.417[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-1} closed
2023-04-11 18:19:29.418[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-2} closed
2023-04-11 18:19:29.418[SpringContextShutdownHook] [com.alibaba.druid.pool.DruidDataSource:1823] [INFO] - {dataSource-3} closed
2023-04-11 18:19:29.419[SpringContextShutdownHook] [com.baomidou.dynamic.datasource.DynamicRoutingDataSource:209] [INFO] - dynamic-datasource all closed success,bye
