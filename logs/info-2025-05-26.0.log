2025-05-26 17:12:12.427[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-05-26 17:12:13.433[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-05-26 17:12:13.787[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-05-26 17:12:13.841[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-05-26 17:12:13.854[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-05-26 17:12:13.869[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-05-26 17:12:13.889[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-05-26 17:12:13.895[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-05-26 17:12:13.916[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-05-26 17:12:15.531[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-26 17:12:15.535[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-26 17:12:15.601[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 48 ms. Found 0 Redis repository interfaces.
2025-05-26 17:12:15.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PClassExtMapper' and 'loyalty.activity.service.external.db.mapper.PClassExtMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.976[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.977[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.977[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.977[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.977[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.977[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.978[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-05-26 17:12:15.979[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-05-26 17:12:16.106[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=22ec375e-a1f5-3333-8606-fe85feadb89e
2025-05-26 17:12:16.163[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-05-26 17:12:16.222[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-05-26 17:12:16.223[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-05-26 17:12:16.223[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-05-26 17:12:16.223[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-05-26 17:12:16.255[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-05-26 17:12:16.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-26 17:12:16.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-26 17:12:16.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-26 17:12:16.258[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-05-26 17:12:16.326[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-05-26 17:12:16.792[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.798[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.803[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.807[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.857[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.861[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.864[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-26 17:12:16.966[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-05-26 17:12:16.969[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-05-26 17:12:17.273[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-05-26 17:12:17.284[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-05-26 17:12:17.284[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-05-26 17:12:17.285[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-26 17:12:17.468[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-05-26 17:12:17.468[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3530 ms
2025-05-26 17:12:17.691[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-05-26 17:12:18.123[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-05-26 17:12:18.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-05-26 17:12:18.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-05-26 17:12:18.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-05-26 17:12:18.136[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-05-26 17:12:18.136[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-05-26 17:12:18.136[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-05-26 17:12:18.138[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-05-26 17:12:18.139[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-05-26 17:12:21.381[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-05-26 17:12:22.237[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-05-26 17:12:22.238[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-05-26 17:12:22.239[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-05-26 17:12:22.806[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-05-26 17:12:23.251[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-05-26 17:12:23.279[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6da76bb1, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a99d328, org.springframework.security.web.header.HeaderWriterFilter@672710d5, org.springframework.security.web.authentication.logout.LogoutFilter@65448932, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@7597ae32), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@38950138, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@354a1239, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2ceb722e, org.springframework.security.web.session.SessionManagementFilter@2197990b, org.springframework.security.web.access.ExceptionTranslationFilter@74ee07e]
2025-05-26 17:12:23.296[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5b1ebbe, org.springframework.security.web.context.SecurityContextPersistenceFilter@68ecc60e, org.springframework.security.web.header.HeaderWriterFilter@5ec75178, org.springframework.security.web.authentication.logout.LogoutFilter@7ed0e5db, loyalty.activity.service.common.security.UserTokenFilter@3b802baa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@24bc52ce, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40949bb0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@109941fa, org.springframework.security.web.session.SessionManagementFilter@38410ce5, org.springframework.security.web.access.ExceptionTranslationFilter@5ab2c5d8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6e6c723b]
2025-05-26 17:12:23.492[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-05-26 17:12:30.207[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-05-26 17:12:30.228[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-05-26 17:12:30.913[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-05-26 17:12:31.640[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-05-26 17:12:31.913[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"**********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"**********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-05-26 17:12:31.925[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"**********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"**********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-05-26 17:12:31.929[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-05-26 17:12:31.950[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-05-26 17:12:32.367[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-05-26 17:12:32.369[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-05-26 17:12:32.380[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-05-26 17:12:32.396[][com.alibaba.nacos.naming.push.receiver] [com.alibaba.nacos.client.naming.core.PushReceiver:86] [INFO] - received push data: {"type":"dom","data":"{\"name\":\"DEFAULT_GROUP@@loyalty-activity-service\",\"clusters\":\"DEFAULT\",\"cacheMillis\":10000,\"hosts\":[{\"instanceId\":\"**********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service\",\"ip\":\"**********\",\"port\":9072,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@loyalty-activity-service\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000},{\"instanceId\":\"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service\",\"ip\":\"*********\",\"port\":9072,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"DEFAULT_GROUP@@loyalty-activity-service\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}],\"lastRefTime\":1748250752345,\"checksum\":\"\",\"allIPs\":false,\"reachProtectionThreshold\":false,\"valid\":true}","lastRefTime":7854390582893327} from /********
2025-05-26 17:12:32.399[][com.alibaba.nacos.naming.push.receiver] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-05-26 17:12:32.400[][com.alibaba.nacos.naming.push.receiver] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"**********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"**********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-05-26 17:12:33.368[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 21.979 seconds (JVM running for 22.937)
2025-05-26 17:12:33.382[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-05-26 17:12:33.383[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-05-26 17:12:33.384[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-05-26 17:12:33.384[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-05-26 17:12:33.384[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-05-26 17:12:33.385[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-05-26 17:12:33.385[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-05-26 17:12:33.386[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-192.168.0.205_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-05-26 17:13:14.861[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-26 17:13:14.861[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-05-26 17:13:14.865[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 4 ms
2025-05-26 17:13:15.319[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signIn/userSignIn】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)】
2025-05-26 17:13:15.358[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-05-26 17:13:28.092[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-05-26 17:13:28.210[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:79] [INFO] - CS课程签到，param=UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)
2025-05-26 17:13:28.325[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator:34] [INFO] - 课程已签到，param=PclassInfoBase(id=264434, startTime=Mon May 26 10:00:00 CST 2025, endTime=Sat May 31 23:00:00 CST 2025, isEnabled=true, limitCount=null, applyCount=1, status=null, isDeleted=false, ClassCode=测试课程, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=12.2360000, targetLongitude=20.2356000, province=广东, city=广州, isOnline=null)
2025-05-26 17:13:28.328[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:90] [INFO] - user=[UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)] has already signed
2025-05-26 17:13:31.943[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [com.alibaba.druid.pool.DruidDataSource:928] [INFO] - {dataSource-1} inited
2025-05-26 17:13:32.173[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.pnec.service.PNECClassService:349] [INFO] - query cs_nc_sign from ncp with result=[]
2025-05-26 17:13:32.181[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:106] [INFO] - 已签到处理
2025-05-26 17:13:32.286[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:108] [INFO] - byActivityIdCellphone:ActivityUserSignin(super=BaseEntity(creator=loyalty-activity-app-service, updater=loyalty-activity-app-service, createTime=Mon Mar 31 11:40:29 CST 2025, updateTime=Mon Mar 31 11:40:29 CST 2025), id=333385910, activityId=264434, cellphone=15102065793, unionid=oDSWq1cUmwjzd0KDyO2RthN4CF1w, openid=oFDxX4zj_GMR_4EagNsr8jytCwOg, inviterType=TRS, inviterId=123456, roleType=test, triggerTime=Mon Mar 31 11:40:29 CST 2025, currentLongitude=null, currentLatitude=null, BabyStatus=null, attendance=null, babyBirthday=null, remark=null, userName=null, ext0={}, isMdPerson=false)
2025-05-26 17:13:32.296[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:123] [INFO] - 重置randomNc：null
2025-05-26 17:13:32.358[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [com.cstools.data.internal.client.CRMClient:380] [INFO] - 【CRM】 sending to url https://cdp-test.mjngc.com/crm/MemberHandle/QureyIsAdd with param {"cellphone":"15102065793","unionid":"oDSWq1cUmwjzd0KDyO2RthN4CF1w","signature":"W4dj+1t2Paepb+z1OrvnLhfSHjqZ8nbVqGAWeBWJ+jL+0+nN7yI4Z8+Jdp7k0QFxvNv8Ihud2ZT1t1N+bwn6cJD6bdLrOcHFuRCYnS3yKxDqZlLQmh7E8wMmkaajQUJfPJTSU9U+qsEJ879dQkyb16AcgGjsyRkBLdzYa33a/Hs\u003d","timestamp":"1748250812349"}
2025-05-26 17:13:32.740[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [com.cstools.data.internal.client.CRMClient:389] [INFO] - 【CRM】 response for url /MemberHandle/QureyIsAdd is {"status":200,"headers":{"Date":["Mon, 26 May 2025 09:13:32 GMT"],"Content-Type":["application/json;charset\u003dUTF-8"],"Connection":["keep-alive"],"traceId":["e42e4afdd3de41098fc3ef65c2fab227"],"x-git-bt":["2025-05-26 16:42:18"],"x-git-cid":["4567bd158cc41f1c7e13899ddbf514dffd03f8a7"],"x-git-b":["crm_mjn_uat"],"Set-Cookie":["cookiesession1\u003d678A3E1EFC9526C6047568B3755B0799;Expires\u003dTue, 26 May 2026 09:13:32 GMT;Path\u003d/;HttpOnly"],"content-length":["80"]},"body":{"body":{"isadd":false},"errCode":"200","errMsg":"请求成功"}}
2025-05-26 17:13:32.740[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:134] [INFO] - IsAddNcResponse is valve:IsAddNcResponse(isAdd=false, qrCode=null)
2025-05-26 17:13:32.742[59a183a651404146bb61850173c22bba][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/signIn/userSignIn】 with exectime=【17438】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={UserSignInResponse(id=333385910, activityCode=测试课程, qrCode=null, isAdd=false, isSigned=true)}】
2025-05-26 17:15:04.501[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/pclass/signIn/userSignIn】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)】
2025-05-26 17:15:04.607[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:79] [INFO] - CS课程签到，param=UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)
2025-05-26 17:15:04.711[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator:34] [INFO] - 课程已签到，param=PclassInfoBase(id=264434, startTime=Mon May 26 10:00:00 CST 2025, endTime=Sat May 31 23:00:00 CST 2025, isEnabled=true, limitCount=null, applyCount=1, status=null, isDeleted=false, ClassCode=12345678, hasStartedClass=null, latitude=null, longitude=null, targetLatitude=12.2360000, targetLongitude=20.2356000, province=广东, city=广州, isOnline=null)
2025-05-26 17:15:04.712[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:90] [INFO] - user=[UserSignInRequest(activityId=264434, babyBirthday=null, cellphone=15102065793, city=广州市, latitude=null, longitude=null, openId=oFDxX4zj_GMR_4EagNsr8jytCwOg, attendance=null, pnecId=822482, signInType=CS, unionId=oDSWq1cUmwjzd0KDyO2RthN4CF1w, babyStatus=11706, remark=, userName=ken, pnaId=, ncCode=822482, tsrId=, isYousheng=false, personId=123456, personType=test)] has already signed
2025-05-26 17:15:06.960[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.pnec.service.PNECClassService:349] [INFO] - query cs_nc_sign from ncp with result=[]
2025-05-26 17:15:06.961[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:106] [INFO] - 已签到处理
2025-05-26 17:15:07.064[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:108] [INFO] - byActivityIdCellphone:ActivityUserSignin(super=BaseEntity(creator=loyalty-activity-app-service, updater=loyalty-activity-app-service, createTime=Mon Mar 31 11:40:29 CST 2025, updateTime=Mon Mar 31 11:40:29 CST 2025), id=333385910, activityId=264434, cellphone=15102065793, unionid=oDSWq1cUmwjzd0KDyO2RthN4CF1w, openid=oFDxX4zj_GMR_4EagNsr8jytCwOg, inviterType=TRS, inviterId=123456, roleType=test, triggerTime=Mon Mar 31 11:40:29 CST 2025, currentLongitude=null, currentLatitude=null, BabyStatus=null, attendance=null, babyBirthday=null, remark=null, userName=null, ext0={}, isMdPerson=false)
2025-05-26 17:15:07.065[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:123] [INFO] - 重置randomNc：null
2025-05-26 17:15:07.120[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [com.cstools.data.internal.client.CRMClient:380] [INFO] - 【CRM】 sending to url https://cdp-test.mjngc.com/crm/MemberHandle/QureyIsAdd with param {"cellphone":"15102065793","unionid":"oDSWq1cUmwjzd0KDyO2RthN4CF1w","signature":"I+6UmaFNf6xwxUgonjWJpD2Cyjp78YMAqz4wX9MmyGm/aFvoQV87og1xmzZJxoAxTYkDh8w2JHbtSksq7qzGX1np8ffXqUr9N2hfVcp8d/MegHVZnIs8CZCr9ODMwRU2agTnptGqvzgcmO9JgNEgomrs2OC0hETecFtLYYrFO8I\u003d","timestamp":"1748250907118"}
2025-05-26 17:15:18.431[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [com.cstools.data.internal.client.CRMClient:389] [INFO] - 【CRM】 response for url /MemberHandle/QureyIsAdd is {"status":200,"headers":{"Date":["Mon, 26 May 2025 09:15:18 GMT"],"Content-Type":["application/json;charset\u003dUTF-8"],"Connection":["keep-alive"],"traceId":["c92a39a45415402aab1a7689941ddc31"],"x-git-bt":["2025-05-26 16:42:18"],"x-git-cid":["4567bd158cc41f1c7e13899ddbf514dffd03f8a7"],"x-git-b":["crm_mjn_uat"],"Set-Cookie":["cookiesession1\u003d678A3E1ED5F8E6A0717CA3F6D29269D6;Expires\u003dTue, 26 May 2026 09:15:18 GMT;Path\u003d/;HttpOnly"],"content-length":["80"]},"body":{"body":{"isadd":false},"errCode":"200","errMsg":"请求成功"}}
2025-05-26 17:15:18.432[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.policy.pclass.signIn.CsSignIn:134] [INFO] - IsAddNcResponse is valve:IsAddNcResponse(isAdd=false, qrCode=null)
2025-05-26 17:15:18.432[9797f3cc10984d50833ecb2ec017a7c3][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/pclass/signIn/userSignIn】 with exectime=【13931】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={UserSignInResponse(id=333385910, activityCode=12345678, qrCode=null, isAdd=false, isSigned=true)}】
2025-05-26 17:15:32.316[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 17:15:32.316[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-05-26 17:15:32.317[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-05-26 17:15:32.317[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-05-26 17:15:32.577[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-05-26 17:15:32.590[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-05-26 17:15:32.591[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-05-26 17:15:32.591[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-05-26 17:15:32.600[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-05-26 17:15:32.601[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
