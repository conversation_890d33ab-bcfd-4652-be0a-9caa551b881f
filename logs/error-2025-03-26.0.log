2025-03-26 09:49:43.160[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-03-26 09:49:44.165[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-03-26 09:49:45.176[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-03-26 09:49:45.176[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] - no available server
2025-03-26 09:49:45.180[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] - [fixed-192.168.0.205_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-03-26 09:53:07.953[783e5e5a35d64674bf8d193ac1e88b03][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【127794】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 09:57:43.191[b351986d7937413b885fa366349082b3][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【91117】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:12:25.811[af28b86b47044ed5b8b5dda23090505a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【79155】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:14:49.448[564b2ce77f254dc2afa11db71accc717][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【33930】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:18:11.341[b935f65dc73d4b9d9d65d432553ce4bd][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【61560】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:24:37.021[da1dc2b1a03f402c80a7edbc3b5b7e7a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【266198】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:26:35.296[a5518a68c43b4013b62754687b5a6bee][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【80057】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:31:50.813[167ea0d33b334b8e93aea9ec25cd5616][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【45484】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:41:18.488[e213a5bc98ae4cc0906d3754d71f4061][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【19991】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:43:30.315[2e332e14ddb240f2af877588fa06b893][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【21356】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:44:38.644[074b424bb1e54d8c997e1cdfb25948a7][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【18013】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-03-26 10:45:01.366[e3f0f59c2538492fb7a1197247129d26][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【8104】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
