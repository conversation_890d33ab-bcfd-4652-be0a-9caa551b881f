2023-03-03 14:33:32.395[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-03-03 14:33:32.395[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-03-03 14:33:32.398[Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-03-03 14:33:32.402[Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2023-03-03 14:33:32.787[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-03-03 14:33:32.818[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2023-03-03 14:33:32.818[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2023-03-03 14:33:32.819[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2023-03-03 14:33:32.871[SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2023-03-03 14:33:32.875[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2023-03-03 14:33:35.906[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2023-03-03 14:33:35.907[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2023-03-03 14:33:37.734[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2023-03-03 14:33:37.737[com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(0) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> []
2023-03-03 14:33:37.739[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2023-03-03 14:33:40.775[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2023-03-03 14:33:40.776[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2023-03-03 14:33:40.778[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2023-03-03 14:33:40.779[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2023-03-03 14:33:40.779[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2023-03-03 14:33:40.781[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2023-03-03 14:33:40.782[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2023-03-03 14:33:40.783[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2023-03-03 14:33:40.784[SpringContextShutdownHook] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2023-03-03 14:33:40.784[SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2023-03-03 14:33:40.786[SpringContextShutdownHook] [org.springframework.beans.factory.support.DisposableBeanAdapter:349] [WARN] - Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2023-03-03 14:33:40.789[SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
