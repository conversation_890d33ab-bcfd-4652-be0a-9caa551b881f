2025-04-02 11:47:35.991[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 11:47:37.212[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 11:47:37.641[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 11:47:38.681[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:39.697[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:40.713[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:40.714[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-04-02 11:47:40.718[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-*************_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:47:40.719[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-04-02 11:47:40.720[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-04-02 11:47:40.722[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 11:47:41.724[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:42.738[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:43.754[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:43.754[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-04-02 11:47:43.754[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-*************_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:144)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:47:43.755[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-04-02 11:47:43.755[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-04-02 11:47:43.755[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:47:44.768[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:45.785[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:46.788[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:46.789[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-04-02 11:47:46.789[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-*************_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:149)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:47:46.790[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-04-02 11:47:46.790[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-04-02 11:47:46.792[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 11:47:47.820[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:48.822[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:49.837[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://*************:8848， err : connect timed out
2025-04-02 11:47:49.838[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-04-02 11:47:49.839[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-*************_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:149)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:47:49.839[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-04-02 11:47:49.840[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-*************_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-04-02 11:47:49.840[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:47:49.842[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 11:47:49.883[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 11:47:51.526[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 11:47:51.530[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 11:47:51.591[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-04-02 11:47:51.914[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.915[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.916[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.917[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.918[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.919[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.919[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.919[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:47:51.919[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 11:47:52.045[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 11:47:52.074[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 11:47:52.114[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:47:52.114[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:47:52.114[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:47:52.114[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:47:52.139[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 11:47:52.140[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:47:52.140[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:47:52.140[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.140[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:47:52.141[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:47:52.220[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 11:47:52.682[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.687[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.693[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.699[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.741[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.744[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.747[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:47:52.913[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 11:47:52.917[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 11:47:53.296[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 11:47:53.310[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 11:47:53.311[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 11:47:53.312[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 11:47:53.534[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 11:47:53.534[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3634 ms
2025-04-02 11:47:53.816[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 11:47:54.307[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 11:47:54.327[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 11:47:54.328[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 11:47:54.328[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 11:47:54.329[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 11:47:54.329[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 11:47:54.329[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 11:47:54.331[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 11:47:54.333[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 11:47:58.154[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 11:47:59.209[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 11:47:59.210[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 11:47:59.211[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 11:47:59.994[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 11:48:00.410[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 11:48:00.411[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 11:48:00.411[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 11:48:00.411[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 11:48:00.411[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 11:48:00.411[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 11:48:00.412[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 11:48:00.412[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 11:48:00.446[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d1c15cb, org.springframework.security.web.context.SecurityContextPersistenceFilter@17524c36, org.springframework.security.web.header.HeaderWriterFilter@1f15d346, org.springframework.security.web.authentication.logout.LogoutFilter@5e78dd7f, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@2e0d6270), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a1fce44, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1f7653ae, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7597ae32, org.springframework.security.web.session.SessionManagementFilter@2aa85cc4, org.springframework.security.web.access.ExceptionTranslationFilter@7315264e]
2025-04-02 11:48:00.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1147a8de, org.springframework.security.web.context.SecurityContextPersistenceFilter@110b6c78, org.springframework.security.web.header.HeaderWriterFilter@63069995, org.springframework.security.web.authentication.logout.LogoutFilter@3de4f936, loyalty.activity.service.common.security.UserTokenFilter@29471e3e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@e62d757, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@597f2d3f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@73fa97a9, org.springframework.security.web.session.SessionManagementFilter@3003d288, org.springframework.security.web.access.ExceptionTranslationFilter@794cd751, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@59881424]
2025-04-02 11:48:00.707[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 11:48:07.511[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 11:48:07.528[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:48:08.089[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 11:48:08.882[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 11:48:12.029[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:15.034[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:18.041[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:18.042[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:552] [ERROR] - request: /nacos/v1/ns/instance/list failed, servers: [*************:8848], code: 500, msg: connect timed out
2025-04-02 11:48:18.042[][main] [com.alibaba.nacos.client.naming.core.HostReactor:343] [ERROR] - [NA] failed to update serviceName: DEFAULT_GROUP@@loyalty-activity-service
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:18.045[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 11:48:18.061[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 11:48:18.748[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 11:48:18.750[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 11:48:19.568[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketException: Software caused connection abort: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:19.568[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketException: Software caused connection abort: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:426)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:383)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:22.574[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:22.574[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:426)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:383)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:25.590[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:25.590[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:426)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:383)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:25.591[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:552] [ERROR] - request: /nacos/v1/ns/instance failed, servers: [*************:8848], code: 500, msg: connect timed out
2025-04-02 11:48:25.591[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:552] [ERROR] - request: /nacos/v1/ns/instance/list failed, servers: [*************:8848], code: 500, msg: connect timed out
2025-04-02 11:48:25.593[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask:484] [WARN] - [NA] failed to update serviceName: DEFAULT_GROUP@@loyalty-activity-service
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:25.593[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:79] [ERROR] - nacos registry, loyalty-activity-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='*************:8848', endpoint='', namespace='', watchDelay=30000, logName='', service='loyalty-activity-service', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*********', networkInterface='', port=9072, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null}},
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:426)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:383)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-04-02 11:48:25.595[][main] [org.springframework.context.support.AbstractApplicationContext:596] [WARN] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
2025-04-02 11:48:25.624[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-04-02 11:48:26.802[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1334)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1309)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:106)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.sendBeat(NamingProxy.java:433)
	at com.alibaba.nacos.client.naming.beat.BeatReactor$BeatTask.run(BeatReactor.java:167)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:28.650[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:149] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-04-02 11:48:28.651[][main] [com.alibaba.nacos.client.naming.core.HostReactor:409] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-04-02 11:48:29.815[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1334)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1309)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:106)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.sendBeat(NamingProxy.java:433)
	at com.alibaba.nacos.client.naming.beat.BeatReactor$BeatTask.run(BeatReactor.java:167)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:30.616[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:31.678[][main] [com.alibaba.nacos.client.naming.core.PushReceiver:121] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-04-02 11:48:32.817[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream0(HttpURLConnection.java:1334)
	at sun.net.www.protocol.http.HttpURLConnection.getOutputStream(HttpURLConnection.java:1309)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:106)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.sendBeat(NamingProxy.java:433)
	at com.alibaba.nacos.client.naming.beat.BeatReactor$BeatTask.run(BeatReactor.java:167)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:32.818[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:552] [ERROR] - request: /nacos/v1/ns/instance/beat failed, servers: [*************:8848], code: 500, msg: connect timed out
2025-04-02 11:48:32.819[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.beat.BeatReactor$BeatTask:198] [ERROR] - [CLIENT-BEAT] failed to send beat: {"port":9072,"ip":"*********","weight":1.0,"serviceName":"DEFAULT_GROUP@@loyalty-activity-service","cluster":"DEFAULT","metadata":{"preserved.register.source":"SPRING_CLOUD"},"scheduled":false,"period":5000,"stopped":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
2025-04-02 11:48:33.623[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:34.684[][main] [com.alibaba.nacos.client.naming.core.PushReceiver:125] [INFO] - com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-04-02 11:48:34.684[][main] [com.alibaba.nacos.client.naming.backups.FailoverReactor:132] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-04-02 11:48:34.684[][main] [com.alibaba.nacos.client.naming.backups.FailoverReactor:134] [INFO] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-04-02 11:48:34.684[][main] [com.alibaba.nacos.client.naming.core.HostReactor:414] [INFO] - com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-04-02 11:48:34.685[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:719] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-04-02 11:48:34.685[][main] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:72] [WARN] - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-04-02 11:48:34.685[][main] [com.alibaba.nacos.client.naming.net.NamingHttpClientManager:79] [WARN] - [NamingHttpClientManager] Destruction of the end
2025-04-02 11:48:34.686[][main] [com.alibaba.nacos.client.identify.CredentialWatcher:105] [INFO] - [null] CredentialWatcher is stopped
2025-04-02 11:48:34.686[][main] [com.alibaba.nacos.client.identify.CredentialService:98] [INFO] - [null] CredentialService is freed
2025-04-02 11:48:34.686[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:723] [INFO] - com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-04-02 11:48:34.686[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-04-02 11:48:35.102[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Pausing ProtocolHandler ["http-nio-9072"]
2025-04-02 11:48:35.102[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Stopping service [Tomcat]
2025-04-02 11:48:35.105[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Stopping ProtocolHandler ["http-nio-9072"]
2025-04-02 11:48:35.108[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Destroying ProtocolHandler ["http-nio-9072"]
2025-04-02 11:48:35.124[][main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener:136] [INFO] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-04-02 11:48:35.147[][main] [org.springframework.boot.SpringApplication:856] [ERROR] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is java.lang.reflect.UndeclaredThrowableException
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:181)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
Caused by: java.lang.reflect.UndeclaredThrowableException: null
	at org.springframework.util.ReflectionUtils.rethrowRuntimeException(ReflectionUtils.java:147)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:83)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:232)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:133)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:98)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:86)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:47)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:426)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:383)
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:46)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	... 15 common frames omitted
Caused by: com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:246)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:212)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:74)
	... 28 common frames omitted
2025-04-02 11:48:36.625[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:36.626[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.net.NamingProxy:552] [ERROR] - request: /nacos/v1/ns/instance/list failed, servers: [*************:8848], code: 500, msg: connect timed out
2025-04-02 11:48:36.627[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask:484] [WARN] - [NA] failed to update serviceName: DEFAULT_GROUP@@loyalty-activity-service
com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([*************:8848]) tried: java.net.SocketTimeoutException: connect timed out
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:556)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:460)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 11:48:40.876[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 11:48:40.876[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 11:48:40.877[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 11:48:40.877[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 11:48:46.293[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 11:48:47.418[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 11:48:47.900[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 11:48:47.950[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 11:48:47.966[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:48:47.983[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 11:48:48.005[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:48:48.006[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 11:48:48.027[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 11:48:49.611[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 11:48:49.614[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 11:48:49.678[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 48 ms. Found 0 Redis repository interfaces.
2025-04-02 11:48:50.005[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.005[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.005[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.006[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.006[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.006[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.006[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.006[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.007[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.008[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.009[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.010[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:48:50.011[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 11:48:50.144[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 11:48:50.174[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 11:48:50.227[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:48:50.227[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:48:50.228[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:48:50.228[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:48:50.255[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 11:48:50.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:48:50.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:48:50.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.256[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:48:50.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.257[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:48:50.258[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:48:50.311[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 11:48:50.684[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.688[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.694[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.698[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.733[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.735[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.737[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:48:50.830[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 11:48:50.833[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 11:48:51.147[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 11:48:51.157[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 11:48:51.158[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 11:48:51.158[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 11:48:51.371[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 11:48:51.371[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3315 ms
2025-04-02 11:48:51.595[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 11:48:51.930[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 11:48:51.939[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 11:48:51.940[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 11:48:51.940[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 11:48:51.940[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 11:48:51.940[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 11:48:51.941[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 11:48:51.941[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 11:48:51.942[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 11:48:55.686[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 11:48:56.691[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 11:48:56.691[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 11:48:56.692[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 11:48:57.402[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 11:48:57.785[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 11:48:57.786[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 11:48:57.815[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ee0ef34, org.springframework.security.web.context.SecurityContextPersistenceFilter@9a932a4, org.springframework.security.web.header.HeaderWriterFilter@7e383712, org.springframework.security.web.authentication.logout.LogoutFilter@4104b6a6, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@349469db), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f5378cd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3c88db1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@ab5d5c8, org.springframework.security.web.session.SessionManagementFilter@2258228f, org.springframework.security.web.access.ExceptionTranslationFilter@7d90d4c6]
2025-04-02 11:48:57.831[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41fdb59c, org.springframework.security.web.context.SecurityContextPersistenceFilter@17287458, org.springframework.security.web.header.HeaderWriterFilter@78fcc4ef, org.springframework.security.web.authentication.logout.LogoutFilter@3e02988, loyalty.activity.service.common.security.UserTokenFilter@5f386009, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7296675e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6cd31e5f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5112858c, org.springframework.security.web.session.SessionManagementFilter@2a12389e, org.springframework.security.web.access.ExceptionTranslationFilter@354a1239, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1201c2f6]
2025-04-02 11:48:58.050[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 11:49:04.877[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 11:49:04.891[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:49:05.371[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 11:49:06.200[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 11:49:06.373[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 11:49:06.381[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 11:49:06.384[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 11:49:06.401[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 11:49:06.976[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 11:49:06.977[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 11:49:06.991[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 11:49:08.132[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 22.953 seconds (JVM running for 23.814)
2025-04-02 11:49:08.143[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:49:08.145[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:49:08.146[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:49:08.146[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:49:08.147[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:49:08.147[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:49:08.148[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:49:08.148[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:49:10.133[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 11:49:10.133[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 11:49:10.135[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2025-04-02 11:49:10.477[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:49:10.520[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oWVvN4mBb6ie_wKqOial_RyX2Y9R","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:49:12.938[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 11:49:13.780[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 11:49:17.775[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 11:49:17.777[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 11:49:17.891[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":548,"openid":"oWVvN4mBb6ie_wKqOial_RyX2Y9R","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:49:20.271[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=],template={
  "touser": "oWVvN4mBb6ie_wKqOial_RyX2Y9R",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 11:49:20.272[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:49:20.453[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:49:20 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:49:20.454[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oWVvN4mBb6ie_wKqOial_RyX2Y9R"}】
2025-04-02 11:49:20.817[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":40003,"errmsg":"invalid openid rid: 67ecb3c0-70823b11-76158c59"}】
2025-04-02 11:49:25.141[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【14686】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oWVvN4mBb6ie_wKqOial_RyX2Y9R, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:49:25.344[06e8f3b0cbc142019822dee9dafac7b9][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=invalid openid rid: 67ecb3c0-70823b11-76158c59
2025-04-02 11:50:53.499[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 11:50:53.499[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 11:50:53.499[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 11:50:53.500[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 11:50:53.884[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:50:53.896[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-04-02 11:50:53.897[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-04-02 11:50:53.897[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-04-02 11:50:53.910[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-04-02 11:50:53.911[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-04-02 11:51:01.416[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 11:51:02.641[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 11:51:03.175[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 11:51:03.227[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 11:51:03.242[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:51:03.258[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 11:51:03.281[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:51:03.282[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 11:51:03.303[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 11:51:04.808[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 11:51:04.810[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 11:51:04.866[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-04-02 11:51:05.178[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.178[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.178[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.178[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.178[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.179[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.180[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.181[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.182[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.182[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.182[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:51:05.182[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 11:51:05.302[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 11:51:05.330[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 11:51:05.368[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:51:05.369[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:51:05.369[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:51:05.369[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:51:05.392[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 11:51:05.392[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:51:05.392[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:51:05.392[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:51:05.393[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:51:05.439[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 11:51:05.802[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.805[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.810[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.813[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.849[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.851[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.853[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:51:05.932[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 11:51:05.935[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 11:51:06.220[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 11:51:06.231[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 11:51:06.231[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 11:51:06.232[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 11:51:06.435[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 11:51:06.435[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3106 ms
2025-04-02 11:51:06.661[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 11:51:07.000[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 11:51:07.009[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 11:51:07.009[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 11:51:07.009[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 11:51:07.010[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 11:51:07.010[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 11:51:07.010[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 11:51:07.011[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 11:51:07.011[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 11:51:10.549[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 11:51:11.595[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 11:51:11.596[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 11:51:11.597[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 11:51:12.252[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 11:51:12.710[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 11:51:12.710[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 11:51:12.710[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 11:51:12.711[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 11:51:12.711[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 11:51:12.711[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 11:51:12.711[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 11:51:12.711[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 11:51:12.745[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@43f4621b, org.springframework.security.web.context.SecurityContextPersistenceFilter@45dbbb97, org.springframework.security.web.header.HeaderWriterFilter@34db5935, org.springframework.security.web.authentication.logout.LogoutFilter@698e2cba, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@2d05d022), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@36c763cd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@12418d3f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5b076d23, org.springframework.security.web.session.SessionManagementFilter@7d90d4c6, org.springframework.security.web.access.ExceptionTranslationFilter@c358f32]
2025-04-02 11:51:12.763[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19dc9dda, org.springframework.security.web.context.SecurityContextPersistenceFilter@63b0aeb1, org.springframework.security.web.header.HeaderWriterFilter@2197990b, org.springframework.security.web.authentication.logout.LogoutFilter@31af3f22, loyalty.activity.service.common.security.UserTokenFilter@72e9c0a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@75b5d09, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1a99d328, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@494c452b, org.springframework.security.web.session.SessionManagementFilter@17eb5661, org.springframework.security.web.access.ExceptionTranslationFilter@140e003e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@650929dc]
2025-04-02 11:51:12.968[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 11:51:19.747[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 11:51:19.760[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:51:20.263[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 11:51:21.116[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 11:51:21.288[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:51:21.294[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:51:21.297[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 11:51:21.309[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 11:51:21.856[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 11:51:21.858[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 11:51:21.872[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 11:51:23.011[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 22.735 seconds (JVM running for 23.585)
2025-04-02 11:51:23.028[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:51:23.030[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:51:23.031[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:51:23.031[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:51:23.032[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:51:23.032[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:51:23.034[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:51:23.034[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:51:29.401[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 11:51:29.401[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 11:51:29.409[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 7 ms
2025-04-02 11:51:29.823[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:51:29.887[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:51:31.874[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 11:51:32.341[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:51:32.343[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:51:32.607[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 11:51:37.276[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":549,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:51:38.963[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 11:51:38.964[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:51:39.168[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:51:39 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:51:39.168[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:51:39.551[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 11:51:48.836[b108d39373e44f83abd09ff4f2313fd8][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【19033】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 11:51:52.056[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:51:52.057[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:51:57.904[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":550,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:51:57.962[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 11:51:57.962[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:51:58.081[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:51:58 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:51:58.081[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:51:58.447[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 11:51:58.672[3a9826dc66304e7ea4c9d1d1e9d74e1c][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【6615】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 11:52:03.698[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:52:03.699[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:52:04.093[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":551,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:52:04.150[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 11:52:04.150[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:52:04.276[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:52:04 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:52:04.278[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:52:04.612[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 11:52:04.837[21e77b764bca46e781312744bf9e436d][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【1138】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 11:52:42.091[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消)】
2025-04-02 11:52:42.092[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:52:42.487[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":552,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:52:42.543[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 11:52:42.544[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:52:42.683[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:52:42 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:52:42.684[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:52:43.065[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 11:52:43.344[7ab73e3dbffa43eab09cd6be9e238d87][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【1253】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 11:52:49.626[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 11:52:49.626[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 11:52:49.626[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 11:52:49.627[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 11:52:49.985[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:52:49.997[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-04-02 11:52:49.998[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-04-02 11:52:49.998[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-04-02 11:52:50.011[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-04-02 11:52:50.012[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-04-02 11:54:07.944[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 11:54:09.124[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 11:54:09.532[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 11:54:09.583[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 11:54:09.599[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:54:09.613[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 11:54:09.636[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 11:54:09.637[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 11:54:09.664[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 11:54:11.107[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 11:54:11.110[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 11:54:11.167[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.494[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.495[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.496[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.497[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 11:54:11.498[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 11:54:11.603[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 11:54:11.625[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 11:54:11.668[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:54:11.668[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:54:11.668[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:54:11.668[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 11:54:11.691[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 11:54:11.691[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:54:11.691[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 11:54:11.692[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 11:54:11.742[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 11:54:12.097[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.101[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.104[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.108[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.146[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.148[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.150[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 11:54:12.231[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 11:54:12.234[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 11:54:12.509[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 11:54:12.521[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 11:54:12.522[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 11:54:12.522[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 11:54:12.719[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 11:54:12.719[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3039 ms
2025-04-02 11:54:12.937[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 11:54:13.312[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 11:54:13.322[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 11:54:13.322[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 11:54:13.323[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 11:54:13.323[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 11:54:13.323[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 11:54:13.324[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 11:54:13.325[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 11:54:13.325[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 11:54:16.932[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 11:54:17.939[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 11:54:17.940[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 11:54:17.940[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 11:54:18.709[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 11:54:19.146[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 11:54:19.147[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 11:54:19.147[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 11:54:19.147[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 11:54:19.147[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 11:54:19.147[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 11:54:19.148[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 11:54:19.148[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 11:54:19.178[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d1703f8, org.springframework.security.web.context.SecurityContextPersistenceFilter@17224bad, org.springframework.security.web.header.HeaderWriterFilter@343b3399, org.springframework.security.web.authentication.logout.LogoutFilter@39352f6b, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@1639ec7a), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6cf47d05, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63b0aeb1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d492d46, org.springframework.security.web.session.SessionManagementFilter@5078e308, org.springframework.security.web.access.ExceptionTranslationFilter@42210d27]
2025-04-02 11:54:19.193[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18be7f57, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d8e9f22, org.springframework.security.web.header.HeaderWriterFilter@76614fd1, org.springframework.security.web.authentication.logout.LogoutFilter@119cd026, loyalty.activity.service.common.security.UserTokenFilter@64aab3fd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47166740, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3b75b7b4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2565b097, org.springframework.security.web.session.SessionManagementFilter@1a2b23f2, org.springframework.security.web.access.ExceptionTranslationFilter@318d69ea, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e047d85]
2025-04-02 11:54:19.408[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 11:54:26.094[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 11:54:26.106[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 11:54:26.593[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 11:54:27.424[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 11:54:27.599[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:54:27.605[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:54:27.608[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 11:54:27.622[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 11:54:28.184[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 11:54:28.185[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 11:54:28.199[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 11:54:29.233[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 22.383 seconds (JVM running for 23.287)
2025-04-02 11:54:29.248[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:54:29.250[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:54:29.251[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:54:29.251[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:54:29.252[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:54:29.252[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:54:29.254[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 11:54:29.254[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 11:54:31.239[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 11:54:31.239[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 11:54:31.241[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2025-04-02 11:54:31.570[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:31.607[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:54:31.691[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 11:54:32.538[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 11:54:32.942[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":553,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:54:33.067[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 11:54:33.067[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:54:33.245[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:54:33 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:54:33.246[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:54:33.659[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 11:54:34.025[90658dfc737e4d79984561bb1d47807a][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【2471】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 11:54:35.249[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:35.251[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:54:35.625[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":554,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:54:35.682[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 11:54:35.683[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:54:35.749[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:54:35 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:54:35.750[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:54:35.910[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ecb4fb-4b9dbe60-734ea585"}】
2025-04-02 11:54:36.125[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【876】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:36.261[495ce3aa707240a2b22d2aff9c3934b4][http-nio-9072-exec-2] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ecb4fb-4b9dbe60-734ea585
2025-04-02 11:54:38.660[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:54:38.666[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-04-02 11:54:45.386[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:45.387[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:54:45.764[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":555,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:54:45.819[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 11:54:45.820[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:54:45.939[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:54:45 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:54:45.941[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:54:46.278[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ecb506-05a74b45-36283f55"}】
2025-04-02 11:54:46.501[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【1115】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:46.624[5029f7c00b274a39a2b303167a91dc58][http-nio-9072-exec-3] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ecb506-05a74b45-36283f55
2025-04-02 11:54:49.929[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:49.930[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:54:50.306[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":556,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:54:50.376[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 11:54:50.377[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:54:50.442[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:54:50 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:54:50.443[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:54:50.629[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ecb50a-35c875e6-58d1ff5b"}】
2025-04-02 11:54:50.840[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【911】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:50.950[6631aa288eaf4fd58114989a90036739][http-nio-9072-exec-4] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ecb50a-35c875e6-58d1ff5b
2025-04-02 11:54:56.186[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:56.187[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 11:54:56.557[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.pclass.service.WxMessageService:103] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":557,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 11:54:56.611[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.pclass.service.WxMessageService:122] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 11:54:56.611[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 11:54:56.732[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 03:54:56 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI"}
2025-04-02 11:54:56.732[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_qkliDT8Lajng6z3z_5O4ylB9rcQ_TUeaTiWdeoYmu4sGDwCT9rIdMYEVunXFkToDU1BYc7d-dMhU8EAaymc0nBHngkBxjrkr0U-11vUW8e-rcDRAsjasBRyKKY8YVCcAHAEBI】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 11:54:56.993[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ecb510-24618225-4abdda82"}】
2025-04-02 11:54:57.203[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【1017】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消)】
2025-04-02 11:54:57.315[1d899038795d4758b4d1162fd364e203][http-nio-9072-exec-5] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ecb510-24618225-4abdda82
2025-04-02 12:01:14.539[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 12:01:14.540[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 12:01:14.540[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 12:01:14.541[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 15:31:47.412[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 15:31:48.592[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 15:31:49.048[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 15:31:49.100[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 15:31:49.116[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 15:31:49.131[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 15:31:49.154[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 15:31:49.154[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 15:31:49.181[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 15:31:50.703[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 15:31:50.706[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 15:31:50.770[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
2025-04-02 15:31:51.086[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.086[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.086[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.086[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.087[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.088[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.089[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.090[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.091[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.091[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.091[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 15:31:51.091[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 15:31:51.221[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 15:31:51.253[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 15:31:51.302[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 15:31:51.302[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 15:31:51.302[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 15:31:51.302[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 15:31:51.342[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 15:31:51.343[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.344[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.345[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.345[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 15:31:51.345[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 15:31:51.414[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 15:31:51.814[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.819[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.823[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.827[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.869[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.872[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.873[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 15:31:51.962[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 15:31:51.964[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 15:31:52.276[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 15:31:52.290[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 15:31:52.291[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 15:31:52.291[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 15:31:52.516[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 15:31:52.516[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3318 ms
2025-04-02 15:31:52.754[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 15:31:53.123[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 15:31:53.134[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 15:31:53.134[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 15:31:53.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 15:31:53.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 15:31:53.135[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 15:31:53.136[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 15:31:53.137[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 15:31:53.137[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 15:31:56.768[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 15:31:57.803[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 15:31:57.804[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 15:31:57.804[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 15:31:58.498[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 15:31:58.919[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 15:31:58.919[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 15:31:58.919[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 15:31:58.919[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 15:31:58.920[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 15:31:58.920[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 15:31:58.920[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 15:31:58.920[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 15:31:58.950[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4e0a5606, org.springframework.security.web.context.SecurityContextPersistenceFilter@3edb09b9, org.springframework.security.web.header.HeaderWriterFilter@54aacbc7, org.springframework.security.web.authentication.logout.LogoutFilter@63236968, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@40a360e6), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d8230cd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d8de155, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@361a1e86, org.springframework.security.web.session.SessionManagementFilter@27115d45, org.springframework.security.web.access.ExceptionTranslationFilter@643947d1]
2025-04-02 15:31:58.966[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@38293d23, org.springframework.security.web.context.SecurityContextPersistenceFilter@2da7ef3e, org.springframework.security.web.header.HeaderWriterFilter@79864d6, org.springframework.security.web.authentication.logout.LogoutFilter@1d82e396, loyalty.activity.service.common.security.UserTokenFilter@1ee37c7a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@67cc5a38, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@23b62cc3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4973dacf, org.springframework.security.web.session.SessionManagementFilter@472d7f34, org.springframework.security.web.access.ExceptionTranslationFilter@2aec09a3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7fcc8d9f]
2025-04-02 15:31:59.213[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 15:32:06.060[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 15:32:06.073[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 15:32:06.651[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 15:32:07.463[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 15:32:07.648[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:32:07.657[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:32:07.661[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 15:32:07.680[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 15:32:08.285[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 15:32:08.286[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 15:32:08.300[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 15:32:09.394[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 23.224 seconds (JVM running for 24.211)
2025-04-02 15:32:09.407[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 15:32:09.409[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 15:32:09.410[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 15:32:09.410[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 15:32:09.411[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 15:32:09.411[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 15:32:09.412[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 15:32:09.412[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 15:32:18.709[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:32:18.710[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:32:25.492[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 15:32:25.493[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 15:32:25.495[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2025-04-02 15:32:25.689[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消, redirectUrl=null)】
2025-04-02 15:32:25.746[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 15:32:26.033[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 15:32:26.785[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 15:32:27.233[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":558,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 15:32:27.386[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 15:32:27.387[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 15:32:27.596[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 07:32:27 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW"}
2025-04-02 15:32:27.597[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 15:32:27.969[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ece80b-5ed971c3-01eb7f18"}】
2025-04-02 15:32:28.285[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【2614】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消, redirectUrl=null)】
2025-04-02 15:32:28.481[749e3aca7a954c9890c9b2c2411c6a7e][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ece80b-5ed971c3-01eb7f18
2025-04-02 15:33:14.801[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:33:14.802[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":2,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 15:33:19.833[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":559,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecid}\u0026signInType\u003d${signInType}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"date3\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 15:33:57.518[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 15:33:57.519[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:33:57.517[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS&h5_url=QS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=${pnecid}&signInType=OUTSIDE_PCLASS",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing2": {
      "value": "N产品主题"
    },
    "date3": {
      "value": "2025-03-25 02:29:00"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 15:34:31.467[][HikariPool-1 housekeeper] [com.zaxxer.hikari.pool.HikariPool$HouseKeeper:787] [WARN] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s176ms489µs100ns).
2025-04-02 15:34:31.467[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 15:34:31.469[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:34:31.479[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@loyalty-activity-service', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 15:34:31.660[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 07:34:31 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW"}
2025-04-02 15:34:31.662[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW】 with param 【{"data":{"thing2":{"value":"N产品主题"},"date3":{"value":"2025-03-25 02:29:00"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d${pnecid}\u0026signInType\u003dOUTSIDE_PCLASS\u0026h5_url\u003dQS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70","templateId":"iM2r8AJgm-5T8Vbp1cqTp177iw12Ug2TsoztfJIdyiM","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 15:34:31.996[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ece887-6d491f3b-44250c6b"}】
2025-04-02 15:34:36.546[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【81745】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=2, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:34:36.670[56c612f6f06049e6942d8976bd93424f][http-nio-9072-exec-2] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ece887-6d491f3b-44250c6b
2025-04-02 15:34:41.496[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 15:34:41.496[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:34:41.498[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:47:48.822[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:47:48.823[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":1,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 15:48:00.615[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":560,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\u0026pnec_id\u003d${pnecId}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"thing1\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityStore}\"\r\n    },\r\n    \"thing5\": {\r\n      \"value\": \"${activityAddress}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"您已经成功报名妈妈课程，请当天准时参加哟\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 15:48:04.843[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=&h5_url=QS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&pnec_id=",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "thing1": {
      "value": "N产品主题"
    },
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": " "
    },
    "thing5": {
      "value": "广州市"
    },
    "thing4": {
      "value": "您已经成功报名妈妈课程，请当天准时参加哟"
    }
  }
}
2025-04-02 15:48:04.844[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 15:48:05.022[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 07:48:04 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW"}
2025-04-02 15:48:05.028[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":" "},"thing5":{"value":"广州市"},"thing4":{"value":"您已经成功报名妈妈课程，请当天准时参加哟"},"thing1":{"value":"N产品主题"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026pnec_id\u003d\u0026h5_url\u003dQS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70","templateId":"J7L4FPeEbiaujkCJlSpPK7V5dqzMAtlfgBvgKn_m-wI","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 15:48:05.477[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 15:48:21.031[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 15:48:21.031[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:48:21.032[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:48:21.266[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【32444】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=1, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:48:21.388[8e97002d664046efaa1269b12713aa91][http-nio-9072-exec-4] [loyalty.activity.service.common.controller.BaseExceptionController:66] [WARN] - Foreign constaint error occurred while execute /loyalty-activity-service/external/jmg/sendSubscribeMsg with msg=Data truncation: Data too long for column 'send_data' at row 1
2025-04-02 15:48:31.062[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 15:48:31.064[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:48:31.083[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 15:59:44.290[645ba55998974b42876ae154b553f94f][http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:59:44.291[645ba55998974b42876ae154b553f94f][http-nio-9072-exec-6] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":3,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 15:59:46.146[645ba55998974b42876ae154b553f94f][http-nio-9072-exec-6] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【1856】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 15:59:46.326[645ba55998974b42876ae154b553f94f][http-nio-9072-exec-6] [loyalty.activity.service.common.controller.BaseExceptionController:23] [ERROR] - Error occurred while execute /loyalty-activity-service/external/jmg/sendSubscribeMsg 
java.lang.NullPointerException: null
	at loyalty.activity.service.external.service.JmgActivityService.sendSubscribeMsg(JmgActivityService.java:111)
	at loyalty.activity.service.external.service.JmgActivityService$$FastClassBySpringCGLIB$$f7143a0e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at loyalty.activity.service.external.service.JmgActivityService$$EnhancerBySpringCGLIB$$d487008a.sendSubscribeMsg(<generated>)
	at loyalty.activity.service.external.controller.JmgActivityController.sendSubscribeMsg(JmgActivityController.java:44)
	at loyalty.activity.service.external.controller.JmgActivityController$$FastClassBySpringCGLIB$$5ec8a110.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at loyalty.activity.service.external.controller.JmgActivityController$$EnhancerBySpringCGLIB$$425c8439.sendSubscribeMsg(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at loyalty.activity.service.common.security.UserTokenFilter.doFilterInternal(UserTokenFilter.java:32)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-04-02 16:00:42.795[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 16:00:42.795[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 16:00:42.795[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 16:00:42.795[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 16:00:43.157[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 16:00:43.171[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-04-02 16:00:43.171[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-04-02 16:00:43.171[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-04-02 16:00:43.183[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-04-02 16:00:43.184[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-04-02 16:00:51.889[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 16:00:53.018[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 16:00:53.479[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 16:00:53.531[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 16:00:53.545[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 16:00:53.561[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 16:00:53.584[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 16:00:53.585[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 16:00:53.608[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 16:00:55.474[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 16:00:55.477[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 16:00:55.551[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 57 ms. Found 0 Redis repository interfaces.
2025-04-02 16:00:55.948[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.948[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.948[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.948[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:00:55.953[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 16:00:56.080[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 16:00:56.109[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 16:00:56.154[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:00:56.155[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:00:56.155[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:00:56.155[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:00:56.188[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 16:00:56.189[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:00:56.189[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:00:56.189[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.190[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.190[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:00:56.190[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.190[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.190[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.191[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:00:56.191[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:00:56.249[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 16:00:56.650[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.654[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.659[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.663[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.708[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.710[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.713[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:00:56.820[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 16:00:56.824[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 16:00:57.124[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 16:00:57.135[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 16:00:57.135[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 16:00:57.136[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 16:00:57.342[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 16:00:57.342[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3709 ms
2025-04-02 16:00:57.606[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 16:00:58.033[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 16:00:58.043[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 16:00:58.043[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 16:00:58.044[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 16:00:58.044[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 16:00:58.044[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 16:00:58.044[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 16:00:58.045[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 16:00:58.046[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 16:01:01.855[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 16:01:02.908[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 16:01:02.909[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 16:01:02.909[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 16:01:03.609[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 16:01:04.041[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 16:01:04.041[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 16:01:04.042[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 16:01:04.075[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@65448932, org.springframework.security.web.context.SecurityContextPersistenceFilter@17eb5661, org.springframework.security.web.header.HeaderWriterFilter@241861bc, org.springframework.security.web.authentication.logout.LogoutFilter@3b75b7b4, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@cf315f1), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@578cd644, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@41a18b54, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@731a5a39, org.springframework.security.web.session.SessionManagementFilter@140449d9, org.springframework.security.web.access.ExceptionTranslationFilter@45382749]
2025-04-02 16:01:04.091[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fb4377d, org.springframework.security.web.context.SecurityContextPersistenceFilter@5ab2c5d8, org.springframework.security.web.header.HeaderWriterFilter@7b13ae73, org.springframework.security.web.authentication.logout.LogoutFilter@24bc52ce, loyalty.activity.service.common.security.UserTokenFilter@6289376f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5ec75178, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@38410ce5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@442e7215, org.springframework.security.web.session.SessionManagementFilter@2dbfb843, org.springframework.security.web.access.ExceptionTranslationFilter@d012e18, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2565b097]
2025-04-02 16:01:04.287[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 16:01:11.211[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 16:01:11.225[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 16:01:11.796[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 16:01:12.637[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 16:01:12.811[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:01:12.817[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:01:12.820[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 16:01:12.833[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 16:01:13.495[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 16:01:13.497[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 16:01:13.512[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 16:01:14.630[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 24.032 seconds (JVM running for 25.011)
2025-04-02 16:01:14.642[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:01:14.644[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:01:14.644[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:01:14.645[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:01:14.645[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:01:14.645[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:01:14.646[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:01:14.646[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:01:23.873[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:01:23.874[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:04:17.152[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 16:04:17.152[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 16:04:17.155[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 2 ms
2025-04-02 16:04:17.511[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 16:04:17.563[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":3,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 16:04:19.483[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 16:04:20.216[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 16:04:28.358[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":561,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"time2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 16:04:36.474[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&h5_url=QS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "time2": {
      "value": "N产品主题"
    },
    "thing3": {
      "value": "N产品主题"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 16:04:36.475[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 16:04:36.696[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 08:04:36 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW"}
2025-04-02 16:04:36.698[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_G1jQlv6VqpHgT6pInDkWHrL2cBrFoZ8s_6MtLd-QiXCnEYNvfLIktDbTfuU_aVRCd6O92OHj8-aXEOUPIUP6Rvdbxk3lvsQvMUh0_r-XGCoPNug-IBhjbtw7SoQFLPgADAMSW】 with param 【{"data":{"time2":{"value":"N产品主题"},"thing3":{"value":"N产品主题"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026h5_url\u003dQS93dV3HDiaNLfrcql6T6vRIgXQqEkeLXcHK_1RWlEyeuMkpNOHdBNEyZikbVh56bhpej82fZHQtnYSsbF_ZX0vTqtchpXxN_1LRSH-by70","templateId":"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 16:04:37.048[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":43101,"errmsg":"user refuse to accept the msg rid: 67ecef94-68025ff8-70149ace"}】
2025-04-02 16:07:50.048[][HikariPool-1 housekeeper] [com.zaxxer.hikari.pool.HikariPool$HouseKeeper:787] [WARN] - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m29s724ms326µs900ns).
2025-04-02 16:07:50.072[][com.alibaba.nacos.naming.beat.sender] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@loyalty-activity-service', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 16:07:50.074[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:07:50.076[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:07:52.215[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【214720】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=http://www.baidu.com?classcode=abc&openid=**********&cellphone=13800138000)】
2025-04-02 16:07:52.414[aafdb979958940ad8b92cc6e63419f7c][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=user refuse to accept the msg rid: 67ecef94-68025ff8-70149ace
2025-04-02 16:08:00.103[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:08:00.105[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:25:43.789[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:234] [INFO] - removed ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:25:43.792[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:26:23.878[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:26:23.884[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-04-02 16:26:45.658[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 16:26:45.659[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 16:26:45.659[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 16:26:45.660[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 16:26:46.063[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 16:26:46.075[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-04-02 16:26:46.076[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-04-02 16:26:46.076[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-04-02 16:26:46.090[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-04-02 16:26:46.091[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-04-02 16:47:03.318[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-04-02 16:47:04.458[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-04-02 16:47:04.888[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-04-02 16:47:04.937[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-04-02 16:47:04.953[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-04-02 16:47:04.968[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2025-04-02 16:47:04.990[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service-attacher.yaml] & group[DEFAULT_GROUP]
2025-04-02 16:47:04.991[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2025-04-02 16:47:05.019[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: sit,attacher
2025-04-02 16:47:06.541[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-04-02 16:47:06.545[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-02 16:47:06.607[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-04-02 16:47:06.949[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logExternalApiCallMapper' and 'loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.950[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSignRuleMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.951[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.952[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.953[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.954[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.954[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.954[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.954[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.954[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.955[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2025-04-02 16:47:06.956[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2025-04-02 16:47:07.074[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=9de8f698-96ad-3bcd-aa16-4d91d6ce540b
2025-04-02 16:47:07.099[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2025-04-02 16:47:07.134[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-attacher.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:47:07.135[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-sit.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:47:07.135[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:47:07.135[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-04-02 16:47:07.157[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-04-02 16:47:07.158[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:47:07.158[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [application-attacher.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.159[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #4) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.160[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-04-02 16:47:07.160[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-04-02 16:47:07.211[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-04-02 16:47:07.617[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.620[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.624[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.WebClientAutoConfig' of type [com.icyanstone.colud.config.WebClientAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.628[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'webClientHeaderInterceptor' of type [com.icyanstone.colud.webClient.WebClientHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.663[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.665[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.667[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-02 16:47:07.749[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-04-02 16:47:07.752[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-04-02 16:47:08.022[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2025-04-02 16:47:08.032[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2025-04-02 16:47:08.032[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2025-04-02 16:47:08.033[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-04-02 16:47:08.210[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2025-04-02 16:47:08.210[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 3171 ms
2025-04-02 16:47:08.448[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2025-04-02 16:47:08.807[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-04-02 16:47:08.817[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-04-02 16:47:08.817[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-04-02 16:47:08.817[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-04-02 16:47:08.818[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-04-02 16:47:08.818[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-04-02 16:47:08.818[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-04-02 16:47:08.819[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-04-02 16:47:08.819[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-04-02 16:47:12.343[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2025-04-02 16:47:13.363[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2025-04-02 16:47:13.365[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2025-04-02 16:47:13.366[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2025-04-02 16:47:14.079[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2025-04-02 16:47:14.461[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2025-04-02 16:47:14.462[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2025-04-02 16:47:14.489[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55c901b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@305230ea, org.springframework.security.web.header.HeaderWriterFilter@6aefb242, org.springframework.security.web.authentication.logout.LogoutFilter@59ffb0af, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@1b6441c9), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2420e962, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6ca6cc1c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7197b96c, org.springframework.security.web.session.SessionManagementFilter@550ce3a9, org.springframework.security.web.access.ExceptionTranslationFilter@1b7c1c6f]
2025-04-02 16:47:14.504[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3bde85b0, org.springframework.security.web.context.SecurityContextPersistenceFilter@25e9f5e6, org.springframework.security.web.header.HeaderWriterFilter@72090a80, org.springframework.security.web.authentication.logout.LogoutFilter@7c27ed2f, loyalty.activity.service.common.security.UserTokenFilter@22bf4d9d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@512319dc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6bc45472, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9930ff6, org.springframework.security.web.session.SessionManagementFilter@198c97d0, org.springframework.security.web.access.ExceptionTranslationFilter@16cc5e51, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1dc37d4f]
2025-04-02 16:47:14.724[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2025-04-02 16:47:21.572[][main] [com.alibaba.arthas.spring.ArthasConfiguration:70] [INFO] - Arthas agent start success.
2025-04-02 16:47:21.586[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 16:47:22.091[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-04-02 16:47:22.946[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-04-02 16:47:23.151[][main] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:47:23.161[][main] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:47:23.164[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting ProtocolHandler ["http-nio-9072"]
2025-04-02 16:47:23.181[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220] [INFO] - Tomcat started on port(s): 9072 (http) with context path '/loyalty-activity-service'
2025-04-02 16:47:23.724[][main] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 16:47:23.725[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:230] [INFO] - [REGISTER-SERVICE] public registering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-04-02 16:47:23.739[][main] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:75] [INFO] - nacos registry, DEFAULT_GROUP loyalty-activity-service *********:9072 register finished
2025-04-02 16:47:24.966[][main] [org.springframework.boot.StartupInfoLogger:61] [INFO] - Started Application in 22.799 seconds (JVM running for 23.665)
2025-04-02 16:47:24.981[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:47:24.982[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:47:24.983[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-attacher.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:47:24.983[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-attacher.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:47:24.984[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:47:24.984[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:47:24.985[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:239] [INFO] - [fixed-*************_8848-loyalty-activity-service] [subscribe] loyalty-activity-service-sit.yaml+DEFAULT_GROUP+loyalty-activity-service
2025-04-02 16:47:24.985[][main] [com.alibaba.nacos.client.config.impl.CacheData:93] [INFO] - [fixed-*************_8848-loyalty-activity-service] [add-listener] ok, tenant=loyalty-activity-service, dataId=loyalty-activity-service-sit.yaml, group=DEFAULT_GROUP, cnt=1
2025-04-02 16:47:34.207[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:228] [INFO] - new ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:47:34.213[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:48:24.732[][http-nio-9072-exec-1] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-02 16:48:24.733[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:525] [INFO] - Initializing Servlet 'dispatcherServlet'
2025-04-02 16:48:24.734[][http-nio-9072-exec-1] [org.springframework.web.servlet.FrameworkServlet:547] [INFO] - Completed initialization in 1 ms
2025-04-02 16:48:25.063[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html)】
2025-04-02 16:48:25.103[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":3,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 16:48:27.009[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:110] [INFO] - HikariPool-1 - Starting...
2025-04-02 16:48:27.770[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [com.zaxxer.hikari.HikariDataSource:123] [INFO] - HikariPool-1 - Start completed.
2025-04-02 16:48:32.478[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":562,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"time2\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 16:48:37.672[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&h5_url=XrIMkPQ09ayajqQ2G0UyRYuj5m_DIjNI8Iiu98ZZySbNJ11LQYqKbbVozIobKM7VHt-eerfYB2LG0SPFvPPfOw],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "time2": {
      "value": "N产品主题"
    },
    "thing3": {
      "value": "N产品主题"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 16:48:37.673[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 16:48:37.878[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 08:48:37 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_K9px1Cp3fTbcVwhafryckn5uea2D5UfbFr8Tr6Cu041i8xQe3Aqv52c4ALJUD5vXo6o3dj3BNZtO9N-eqnOO8X8tQgt6etIb9_hyMXZIyNVR9D7EK57FwCUNoRcLZNcAAAPDR"}
2025-04-02 16:48:37.879[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_K9px1Cp3fTbcVwhafryckn5uea2D5UfbFr8Tr6Cu041i8xQe3Aqv52c4ALJUD5vXo6o3dj3BNZtO9N-eqnOO8X8tQgt6etIb9_hyMXZIyNVR9D7EK57FwCUNoRcLZNcAAAPDR】 with param 【{"data":{"time2":{"value":"N产品主题"},"thing3":{"value":"N产品主题"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026h5_url\u003dXrIMkPQ09ayajqQ2G0UyRYuj5m_DIjNI8Iiu98ZZySbNJ11LQYqKbbVozIobKM7VHt-eerfYB2LG0SPFvPPfOw","templateId":"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 16:48:38.171[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":47003,"errmsg":"argument invalid! data.time2.value invalid rid: 67ecf9e6-77d376a1-6be0f4d2"}】
2025-04-02 16:48:55.806[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 16:48:55.807[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:48:55.809[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":false,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:48:57.774[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:119] [ERROR] - Runtime exception occurred when excecute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】, exectime=【32726】, params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html)】
2025-04-02 16:48:57.996[f437ac22e29f447f8819c5b3d5dd7a57][http-nio-9072-exec-1] [loyalty.activity.service.common.controller.BaseExceptionController:37] [WARN] - Internal error occurred while request /loyalty-activity-service/external/jmg/sendSubscribeMsg with errorCode=03, msg=argument invalid! data.time2.value invalid rid: 67ecf9e6-77d376a1-6be0f4d2
2025-04-02 16:49:05.833[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.beat.BeatReactor:81] [INFO] - [BEAT] adding beat: BeatInfo{port=9072, ip='*********', weight=1.0, serviceName='DEFAULT_GROUP@@loyalty-activity-service', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-04-02 16:49:05.833[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:241] [INFO] - modified ips(1) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:49:05.835[][com.alibaba.nacos.client.naming.updater] [com.alibaba.nacos.client.naming.core.HostReactor:267] [INFO] - current ips:(2) service: DEFAULT_GROUP@@loyalty-activity-service@@DEFAULT -> [{"instanceId":"********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*********#9072#DEFAULT#DEFAULT_GROUP@@loyalty-activity-service","ip":"*********","port":9072,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@loyalty-activity-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-04-02 16:52:12.586[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:87] [INFO] - Start to execute method=【/external/jmg/sendSubscribeMsg】 with user=【-1】,ip=【127.0.0.1】,params=【"arg0":JmgSubscribeMsgRequest(code=SOChubei2024101700010, openid=oFDxX4-zwZToKXhtfcFYJGPUhcRM, cellphone=18316552259, unionid=oDSWq1TlwtuxQk7m6tcbACunwQqI, subscribeType=3, updateContent=活动已取消, redirectUrl=https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html)】
2025-04-02 16:52:12.588[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [loyalty.activity.service.external.controller.JmgActivityController:43] [INFO] - start to request /jmg/sendSubscribeMsg with request={"code":"SOChubei2024101700010","unionid":"oDSWq1TlwtuxQk7m6tcbACunwQqI","redirectUrl":"https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html","openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","subscribeType":3,"cellphone":"18316552259","updateContent":"活动已取消"}
2025-04-02 16:52:18.464[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:104] [INFO] - get sendSignIn class channel={"address":"广东省广州市黄埔区神舟路与科翔路交叉口东北200米","courseName":"N产品主题","id":563,"openid":"oFDxX4-zwZToKXhtfcFYJGPUhcRM","place":"","startTime":"Mar 25, 2025 2:29:00 PM","templateData":"{\r\n  \"touser\": \"${openid}\",\r\n  \"template_id\": \"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY\",\r\n  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code\u003d${classesCode}\",\r\n  \"miniprogram_state\": \"${miniProgramState}\",\r\n  \"lang\": \"zh_CN\",\r\n  \"data\": {\r\n    \"time2\": {\r\n      \"value\": \"${activityStartTime}\"\r\n    },\r\n    \"thing3\": {\r\n      \"value\": \"${activityName}\"\r\n    },\r\n    \"thing4\": {\r\n      \"value\": \"${content}\"\r\n    }\r\n  }\r\n}","classesCode":"SOChubei2024101700010"}
2025-04-02 16:52:20.554[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [loyalty.activity.service.pclass.service.WxMessageService:134] [INFO] - get sendSignIn page=[packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010&h5_url=XrIMkPQ09ayajqQ2G0UyRYuj5m_DIjNI8Iiu98ZZySbNJ11LQYqKbbVozIobKM7VHt-eerfYB2LG0SPFvPPfOw],template={
  "touser": "oFDxX4-zwZToKXhtfcFYJGPUhcRM",
  "template_id": "P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY",
  "page": "packages/campaign/pages/mother-class/index/index?classes_code=SOChubei2024101700010",
  "miniprogram_state": "${miniProgramState}",
  "lang": "zh_CN",
  "data": {
    "time2": {
      "value": "2025-03-25 02:29:00"
    },
    "thing3": {
      "value": "N产品主题"
    },
    "thing4": {
      "value": "活动已取消"
    }
  }
}
2025-04-02 16:52:20.555[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:30] [INFO] - Sending to url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a with param 
2025-04-02 16:52:20.735[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [common.wechat.serviceapi.client.CommonWechatServiceClient:32] [INFO] - Response for url http://wechat-tools.icyanstone.com/springcloud-common-wechat-service/wechat-service/tokenInfo/getAccessToken?appid=wx0906e6c98c19783a is {"status":200,"headers":{"Date":["Wed, 02 Apr 2025 08:52:20 GMT"],"Content-Type":["application/json"],"Content-Length":["136"],"Connection":["keep-alive"]},"body":"90_K9px1Cp3fTbcVwhafryckn5uea2D5UfbFr8Tr6Cu041i8xQe3Aqv52c4ALJUD5vXo6o3dj3BNZtO9N-eqnOO8X8tQgt6etIb9_hyMXZIyNVR9D7EK57FwCUNoRcLZNcAAAPDR"}
2025-04-02 16:52:20.736[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:56] [INFO] - String to send request to Weixin 【https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=90_K9px1Cp3fTbcVwhafryckn5uea2D5UfbFr8Tr6Cu041i8xQe3Aqv52c4ALJUD5vXo6o3dj3BNZtO9N-eqnOO8X8tQgt6etIb9_hyMXZIyNVR9D7EK57FwCUNoRcLZNcAAAPDR】 with param 【{"data":{"time2":{"value":"2025-03-25 02:29:00"},"thing3":{"value":"N产品主题"},"thing4":{"value":"活动已取消"}},"lang":"zh_CN","miniProgramState":"trial","page":"packages/campaign/pages/mother-class/index/index?classes_code\u003dSOChubei2024101700010\u0026h5_url\u003dXrIMkPQ09ayajqQ2G0UyRYuj5m_DIjNI8Iiu98ZZySbNJ11LQYqKbbVozIobKM7VHt-eerfYB2LG0SPFvPPfOw","templateId":"P68gDhFc-anIT3aEjyLgREvnyje9N22KGbp1vpFebdY","toUser":"oFDxX4-zwZToKXhtfcFYJGPUhcRM"}】
2025-04-02 16:52:21.144[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [com.cstools.data.internal.client.WxClient:60] [INFO] - Resp for sending request to Weixin is 【{"errcode":0,"errmsg":"ok"}】
2025-04-02 16:52:26.561[5f99f8d167524247aa95cd4c5bc906b3][http-nio-9072-exec-3] [loyalty.activity.service.sharelib.utils.log.GenericApiAccessLogExecutor:100] [INFO] - End to execute method=【/external/jmg/sendSubscribeMsg】 with exectime=【13975】,user=【-1】,ip=【127.0.0.1】,result=【sender=loyalty-activity-service,code=01,message=操作成功,body={null}】
2025-04-02 17:03:39.776[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-04-02 17:03:39.776[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2025-04-02 17:03:39.777[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2025-04-02 17:03:39.778[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
2025-04-02 17:03:40.185[][SpringContextShutdownHook] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:218] [INFO] - Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-04-02 17:03:40.197[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:90] [INFO] - De-registering from Nacos Server now...
2025-04-02 17:03:40.197[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:101] [INFO] - [BEAT] removing beat: DEFAULT_GROUP@@loyalty-activity-service:*********:9072 from beat map.
2025-04-02 17:03:40.197[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.net.NamingProxy:260] [INFO] - [DEREGISTER-SERVICE] public deregistering service DEFAULT_GROUP@@loyalty-activity-service with instance: Instance{instanceId='null', ip='*********', port=9072, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-04-02 17:03:40.211[][SpringContextShutdownHook] [com.alibaba.cloud.nacos.registry.NacosServiceRegistry:110] [INFO] - De-registration finished.
2025-04-02 17:03:40.212[][SpringContextShutdownHook] [com.alibaba.nacos.client.naming.beat.BeatReactor:147] [INFO] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
