2025-07-22 11:14:33.884[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2025-07-22 11:14:35.240[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-22 11:14:35.655[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2025-07-22 11:14:36.711[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:37.722[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:38.737[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:38.737[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-07-22 11:14:38.744[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.205_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-07-22 11:14:38.745[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.205_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-07-22 11:14:38.746[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.205_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-07-22 11:14:38.748[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2025-07-22 11:14:39.753[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:40.763[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:41.777[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.205:8848， err : connect timed out
2025-07-22 11:14:41.777[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2025-07-22 11:14:41.778[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.205_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:144)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2025-07-22 11:14:41.778[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.205_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2025-07-22 11:14:41.778[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.205_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2025-07-22 11:14:41.779[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2025-07-22 11:14:41.991[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-22 11:14:41.998[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
