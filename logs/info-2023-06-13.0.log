2023-06-13 10:16:23.804[][background-preinit] [org.hibernate.validator.internal.util.Version:30] [INFO] - HV000001: Hibernate Validator 5.3.4.Final
2023-06-13 10:16:24.828[][main] [com.alibaba.nacos.client.config.impl.LocalConfigInfoProcessor:212] [INFO] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2023-06-13 10:16:25.191[][main] [com.alibaba.nacos.client.config.impl.Limiter:54] [INFO] - limitTime:5.0
2023-06-13 10:16:26.224[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:27.231[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:28.238[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:28.239[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-06-13 10:16:28.246[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-06-13 10:16:28.248[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-06-13 10:16:28.249[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-06-13 10:16:28.251[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service] & group[DEFAULT_GROUP]
2023-06-13 10:16:29.264[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:30.267[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:31.276[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:31.276[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-06-13 10:16:31.277[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:144)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-06-13 10:16:31.277[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-06-13 10:16:31.277[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-06-13 10:16:31.277[][main] [com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder:87] [WARN] - Ignore the empty nacos configuration and get it based on dataId[loyalty-activity-service.yaml] & group[DEFAULT_GROUP]
2023-06-13 10:16:32.283[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:33.294[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:34.305[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:112] [ERROR] - [NACOS SocketTimeoutException httpGet] currentServerAddr:http://192.168.0.240:8848， err : connect timed out
2023-06-13 10:16:34.306[][main] [com.alibaba.nacos.client.config.http.ServerHttpAgent:133] [ERROR] - no available server
2023-06-13 10:16:34.308[][main] [com.alibaba.nacos.client.config.impl.ClientWorker:279] [ERROR] - [fixed-192.168.0.240_8848-loyalty-activity-service] [sub-server] get server config exception, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service
java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:149)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:635)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:390)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-06-13 10:16:34.308[][main] [com.alibaba.nacos.client.config.NacosConfigService:166] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get from server error, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, msg=ErrCode:500, ErrMsg:no available server
2023-06-13 10:16:34.308[][main] [com.alibaba.nacos.client.config.NacosConfigService:170] [WARN] - [fixed-192.168.0.240_8848-loyalty-activity-service] [get-config] get snapshot ok, dataId=loyalty-activity-service-uat.yaml, group=DEFAULT_GROUP, tenant=loyalty-activity-service, config=
2023-06-13 10:16:34.310[][main] [com.alibaba.nacos.client.config.utils.JvmUtil:49] [INFO] - isMultiInstance:false
2023-06-13 10:16:34.334[][main] [org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration:109] [INFO] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP'}]
2023-06-13 10:16:34.364[][main] [org.springframework.boot.SpringApplication:664] [INFO] - The following profiles are active: uat
2023-06-13 10:16:35.663[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:250] [INFO] - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-06-13 10:16:35.666[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:128] [INFO] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2023-06-13 10:16:35.710[][main] [org.springframework.data.repository.config.RepositoryConfigurationDelegate:188] [INFO] - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'CSActivityMapper' and 'loyalty.activity.service.external.db.mapper.CSActivityMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classInfoMapper' and 'loyalty.activity.service.external.db.mapper.ClassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'classOwnerDataMapper' and 'loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignInMapper' and 'loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.971[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'associationSurveyMapper' and 'loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'imitationTestMapper' and 'loyalty.activity.service.pclass.db.mapper.ImitationTestMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassApplyQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoQueryMapper' and 'loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'PNECClassMapper' and 'loyalty.activity.service.pnec.db.mapper.PNECClassMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchInfoMapper' and 'loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.973[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'apiCallLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ApiCallLogMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPnaInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPnaInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSignupMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassScheduleJobLogMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassScheduleJobLogMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppSubscribeMsgInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserSignupFormMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityAddressMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassPnecContactWayMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassPnecContactWayMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.974[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'syncPclassCouponInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.SyncPclassCouponInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityOwnerRelMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'wxmppTemplateInfoMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activitySignRemindMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchStatisticsMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchStatisticsMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'activityUserSigninMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchRecordMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'logPclassSubscribeSendMsgMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassUserExpectCityRecordMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.975[][main] [org.mybatis.logging.Logger:44] [WARN] - Skipping MapperFactoryBean with name 'pclassResearchMapper' and 'loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassResearchMapper' mapperInterface. Bean already defined with the same name!
2023-06-13 10:16:35.976[][main] [org.mybatis.logging.Logger:44] [WARN] - No MyBatis mapper was found in '[loyalty.activity.service.sharelib.domain.db, loyalty.activity.service]' package. Please check your configuration.
2023-06-13 10:16:36.100[][main] [org.springframework.cloud.context.scope.GenericScope:288] [INFO] - BeanFactory id=5105a450-6003-3741-a655-ab6477441d90
2023-06-13 10:16:36.130[][main] [com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor:50] [INFO] - Post-processing PropertySource instances
2023-06-13 10:16:36.170[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service-uat.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-06-13 10:16:36.170[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-06-13 10:16:36.171[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource bootstrapProperties-loyalty-activity-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2023-06-13 10:16:36.193[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2023-06-13 10:16:36.195[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-06-13 10:16:36.195[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2023-06-13 10:16:36.195[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2023-06-13 10:16:36.195[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2023-06-13 10:16:36.196[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2023-06-13 10:16:36.196[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-06-13 10:16:36.196[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' (document #2) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2023-06-13 10:16:36.196[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2023-06-13 10:16:36.196[][main] [com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter:39] [INFO] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2023-06-13 10:16:36.242[][main] [com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter:31] [INFO] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2023-06-13 10:16:36.618[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'com.icyanstone.colud.config.RestTemplateAutoConfig' of type [com.icyanstone.colud.config.RestTemplateAutoConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-13 10:16:36.623[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'requestHeaderInterceptor' of type [com.icyanstone.colud.http.RestTemplateHeaderInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-13 10:16:36.669[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-13 10:16:36.672[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-13 10:16:36.675[][main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:350] [INFO] - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-06-13 10:16:36.768[][main] [com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver:31] [INFO] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2023-06-13 10:16:36.775[][main] [com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector:30] [INFO] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2023-06-13 10:16:37.071[][main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108] [INFO] - Tomcat initialized with port(s): 9072 (http)
2023-06-13 10:16:37.082[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing ProtocolHandler ["http-nio-9072"]
2023-06-13 10:16:37.083[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting service [Tomcat]
2023-06-13 10:16:37.083[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Starting Servlet engine: [Apache Tomcat/9.0.41]
2023-06-13 10:16:37.228[][main] [org.apache.juli.logging.DirectJDKLog:173] [INFO] - Initializing Spring embedded WebApplicationContext
2023-06-13 10:16:37.229[][main] [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:289] [INFO] - Root WebApplicationContext: initialization completed in 2849 ms
2023-06-13 10:16:37.459[][main] [org.springframework.boot.actuate.endpoint.EndpointId:155] [WARN] - Endpoint ID 'list-exception' contains invalid characters, please migrate to a valid format.
2023-06-13 10:16:38.059[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:34] [INFO] - String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2023-06-13 10:16:38.077[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2023-06-13 10:16:38.078[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2023-06-13 10:16:38.078[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2023-06-13 10:16:38.078[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2023-06-13 10:16:38.079[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2023-06-13 10:16:38.079[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2023-06-13 10:16:38.080[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2023-06-13 10:16:38.081[][main] [com.ulisesbocchio.jasyptspringboot.encryptor.DefaultLazyEncryptor:88] [INFO] - Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2023-06-13 10:16:42.960[][main] [loyalty.activity.service.sharelib.utils.log.APILogComponent:26] [INFO] - Enabling APILogComponent
2023-06-13 10:16:43.867[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$1:65] [INFO] - initializer namespace from System Property :null
2023-06-13 10:16:43.868[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$2:74] [INFO] - initializer namespace from System Environment :null
2023-06-13 10:16:43.869[][main] [com.alibaba.nacos.client.naming.utils.InitUtils$3:84] [INFO] - initializer namespace from System Property :null
2023-06-13 10:16:44.464[][main] [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58] [INFO] - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-06-13 10:16:44.880[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/access/**'] with []
2023-06-13 10:16:44.880[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-ui/**'] with []
2023-06-13 10:16:44.880[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/webjars/springfox-swagger-ui/**'] with []
2023-06-13 10:16:44.880[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/swagger-resources/**'] with []
2023-06-13 10:16:44.880[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/v2/**'] with []
2023-06-13 10:16:44.881[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/loggers/**'] with []
2023-06-13 10:16:44.881[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/actuator/**'] with []
2023-06-13 10:16:44.881[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/access/**'] with []
2023-06-13 10:16:44.912[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure Ant [pattern='/ncp/**'] with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f455d91, org.springframework.security.web.context.SecurityContextPersistenceFilter@5ae5a507, org.springframework.security.web.header.HeaderWriterFilter@4a29520e, org.springframework.security.web.authentication.logout.LogoutFilter@5623fd90, QywxCodeTokenFilter(authenticationManager=org.springframework.security.authentication.ProviderManager@5e8f1db2), org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1a370c7c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@14144cc9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16c3e494, org.springframework.security.web.session.SessionManagementFilter@3d3e9163, org.springframework.security.web.access.ExceptionTranslationFilter@32cfd722]
2023-06-13 10:16:44.929[][main] [org.springframework.security.web.DefaultSecurityFilterChain:51] [INFO] - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3de507af, org.springframework.security.web.context.SecurityContextPersistenceFilter@4fe87cb9, org.springframework.security.web.header.HeaderWriterFilter@59eff566, org.springframework.security.web.authentication.logout.LogoutFilter@6bafe3b0, loyalty.activity.service.common.security.UserTokenFilter@349504d5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e212f38, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4843b35f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@347770a9, org.springframework.security.web.session.SessionManagementFilter@1277a6a1, org.springframework.security.web.access.ExceptionTranslationFilter@537e52de, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@261d7ee2]
2023-06-13 10:16:45.120[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'applicationTaskExecutor'
2023-06-13 10:16:45.280[][main] [org.springframework.scheduling.concurrent.ExecutorConfigurationSupport:181] [INFO] - Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2023-06-13 10:16:45.775[][main] [org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration:65] [WARN] - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2023-06-13 10:16:46.564[][main] [org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82] [WARN] - Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2023-06-13 10:16:49.692[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-06-13 10:16:52.708[][main] [com.alibaba.nacos.client.naming.net.NamingProxy:617] [ERROR] - [NA] failed to request
java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1220)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1156)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1050)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:984)
	at com.alibaba.nacos.common.http.client.request.JdkHttpClientRequest.execute(JdkHttpClientRequest.java:112)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:482)
	at com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)
	at com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:603)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:526)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:498)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:493)
	at com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:407)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:378)
	at com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:341)
	at com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:316)
	at com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:139)
	at com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:457)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.start(NacosWatch.java:123)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:178)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:54)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:356)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:155)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:123)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:940)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:767)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:759)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:426)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:326)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at loyalty.activity.service.Application.main(Application.java:19)
2023-06-13 10:16:53.597[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:108] [WARN] - [HttpClientBeanHolder] Start destroying common HttpClient
2023-06-13 10:16:53.597[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:145] [WARN] - [NotifyCenter] Start destroying Publisher
2023-06-13 10:16:53.599[][Thread-13] [com.alibaba.nacos.common.notify.NotifyCenter:162] [WARN] - [NotifyCenter] Destruction of the end
2023-06-13 10:16:53.599[][Thread-2] [com.alibaba.nacos.common.http.HttpClientBeanHolder:114] [WARN] - [HttpClientBeanHolder] Destruction of the end
