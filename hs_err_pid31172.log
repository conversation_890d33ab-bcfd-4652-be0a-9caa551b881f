#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 131088 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=31172, tid=38524
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Fri May 30 01:02:48 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.486533 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002f5865c7390):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=38524, stack(0x000000ffcb400000,0x000000ffcb500000) (1024K)]


Current CompileTask:
C2:    486 1088       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x000000ffcb400000,0x000000ffcb500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0xc6265]
V  [jvm.dll+0x6be6fc]
V  [jvm.dll+0x618f7e]
V  [jvm.dll+0x607c0b]
V  [jvm.dll+0x607835]
V  [jvm.dll+0x82592a]
V  [jvm.dll+0x81d01c]
V  [jvm.dll+0x82884a]
V  [jvm.dll+0x60b692]
V  [jvm.dll+0x258b52]
V  [jvm.dll+0x258f0f]
V  [jvm.dll+0x2517e5]
V  [jvm.dll+0x24f03e]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002f5865b82d0, length=16, elements={
0x000002f5df746e80, 0x000002f5ffb3bd10, 0x000002f5ffb3cb30, 0x000002f5ffb95dc0,
0x000002f5ffb96820, 0x000002f5ffb66130, 0x000002f5ffb66b90, 0x000002f5ffb69f40,
0x000002f5ffb6ae00, 0x000002f5ffd47fe0, 0x000002f5ffd4c450, 0x000002f586125420,
0x000002f5864f6270, 0x000002f5863470f0, 0x000002f5865bd100, 0x000002f5865c7390
}

Java Threads: ( => current thread )
  0x000002f5df746e80 JavaThread "main"                              [_thread_blocked, id=40148, stack(0x000000ffc9d00000,0x000000ffc9e00000) (1024K)]
  0x000002f5ffb3bd10 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7016, stack(0x000000ffca500000,0x000000ffca600000) (1024K)]
  0x000002f5ffb3cb30 JavaThread "Finalizer"                  daemon [_thread_blocked, id=37448, stack(0x000000ffca600000,0x000000ffca700000) (1024K)]
  0x000002f5ffb95dc0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=11336, stack(0x000000ffca700000,0x000000ffca800000) (1024K)]
  0x000002f5ffb96820 JavaThread "Attach Listener"            daemon [_thread_blocked, id=7040, stack(0x000000ffca800000,0x000000ffca900000) (1024K)]
  0x000002f5ffb66130 JavaThread "Service Thread"             daemon [_thread_blocked, id=13448, stack(0x000000ffca900000,0x000000ffcaa00000) (1024K)]
  0x000002f5ffb66b90 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=10076, stack(0x000000ffcaa00000,0x000000ffcab00000) (1024K)]
  0x000002f5ffb69f40 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=41864, stack(0x000000ffcab00000,0x000000ffcac00000) (1024K)]
  0x000002f5ffb6ae00 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26108, stack(0x000000ffcac00000,0x000000ffcad00000) (1024K)]
  0x000002f5ffd47fe0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15396, stack(0x000000ffcad00000,0x000000ffcae00000) (1024K)]
  0x000002f5ffd4c450 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8796, stack(0x000000ffcae00000,0x000000ffcaf00000) (1024K)]
  0x000002f586125420 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=7132, stack(0x000000ffcaf00000,0x000000ffcb000000) (1024K)]
  0x000002f5864f6270 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=16432, stack(0x000000ffcb000000,0x000000ffcb100000) (1024K)]
  0x000002f5863470f0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_blocked, id=30480, stack(0x000000ffcb200000,0x000000ffcb300000) (1024K)]
  0x000002f5865bd100 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=22496, stack(0x000000ffcb300000,0x000000ffcb400000) (1024K)]
=>0x000002f5865c7390 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=38524, stack(0x000000ffcb400000,0x000000ffcb500000) (1024K)]
Total: 16

Other Threads:
  0x000002f5ff06e9b0 VMThread "VM Thread"                           [id=41432, stack(0x000000ffca400000,0x000000ffca500000) (1024K)]
  0x000002f5ffac0f70 WatcherThread "VM Periodic Task Thread"        [id=41532, stack(0x000000ffca300000,0x000000ffca400000) (1024K)]
  0x000002f5e1ac9360 WorkerThread "GC Thread#0"                     [id=34396, stack(0x000000ffc9e00000,0x000000ffc9f00000) (1024K)]
  0x000002f5e1ada150 ConcurrentGCThread "G1 Main Marker"            [id=6968, stack(0x000000ffc9f00000,0x000000ffca000000) (1024K)]
  0x000002f5e1adae00 WorkerThread "G1 Conc#0"                       [id=6948, stack(0x000000ffca000000,0x000000ffca100000) (1024K)]
  0x000002f5fef6bde0 ConcurrentGCThread "G1 Refine#0"               [id=5872, stack(0x000000ffca100000,0x000000ffca200000) (1024K)]
  0x000002f5fef6c950 ConcurrentGCThread "G1 Service"                [id=32360, stack(0x000000ffca200000,0x000000ffca300000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread1      511 1179       4       java.util.HashMap::putVal (300 bytes)
C2 CompilerThread2      511 1088       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 18134K, committed 18432K, reserved 1114112K
  class space    used 1793K, committed 1920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x00000006229715b8, 0x0000000622c00000| 36%| E|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 124|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|CS|TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000002f5f5e80000,0x000002f5f6e70000] _byte_map_base: 0x000002f5f2e62000

Marking Bits: (CMBitMap*) 0x000002f5e1ac9a60
 Bits: [0x000002f5f6e70000, 0x000002f5fed80000)

Polling page: 0x000002f5dfa00000

Metaspace:

Usage:
  Non-class:     15.96 MB used.
      Class:      1.75 MB used.
       Both:     17.71 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      16.12 MB ( 25%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      18.00 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.84 MB
       Class:  14.16 MB
        Both:  30.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 242.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 288.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 441.
num_chunk_merges: 0.
num_chunk_splits: 246.
num_chunks_enlarged: 115.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=383Kb max_used=383Kb free=119616Kb
 bounds [0x000002f5ed400000, 0x000002f5ed670000, 0x000002f5f4930000]
CodeHeap 'profiled nmethods': size=120000Kb used=1924Kb max_used=1924Kb free=118075Kb
 bounds [0x000002f5e5930000, 0x000002f5e5ba0000, 0x000002f5ece60000]
CodeHeap 'non-nmethods': size=5760Kb used=1435Kb max_used=1451Kb free=4324Kb
 bounds [0x000002f5ece60000, 0x000002f5ed0d0000, 0x000002f5ed400000]
 total_blobs=1698 nmethods=1179 adapters=424
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.478 Thread 0x000002f5ffb6ae00 nmethod 1133 0x000002f5e5af7b10 code [0x000002f5e5af7ce0, 0x000002f5e5af7fc0]
Event: 0.478 Thread 0x000002f5ffb6ae00 1134       3       jdk.internal.misc.Unsafe::convEndian (16 bytes)
Event: 0.478 Thread 0x000002f5ffb6ae00 nmethod 1134 0x000002f5e5af8110 code [0x000002f5e5af82c0, 0x000002f5e5af8430]
Event: 0.481 Thread 0x000002f5ffb6ae00 1137       3       jdk.internal.misc.Unsafe::checkSize (32 bytes)
Event: 0.481 Thread 0x000002f586125420 1138   !   4       java.util.zip.Inflater::finished (19 bytes)
Event: 0.481 Thread 0x000002f5ffb6ae00 nmethod 1137 0x000002f5e5af8510 code [0x000002f5e5af86c0, 0x000002f5e5af8938]
Event: 0.481 Thread 0x000002f5ffb6ae00 1136   !   3       jdk.internal.loader.BuiltinClassLoader::defineClass (162 bytes)
Event: 0.481 Thread 0x000002f586125420 nmethod 1138 0x000002f5ed45cc10 code [0x000002f5ed45cda0, 0x000002f5ed45cfa0]
Event: 0.482 Thread 0x000002f5ffb6ae00 nmethod 1136 0x000002f5e5af8a10 code [0x000002f5e5af8f00, 0x000002f5e5afb710]
Event: 0.482 Thread 0x000002f5ffb6ae00 1140       3       java.lang.invoke.AbstractValidatingLambdaMetafactory::isAdaptableTo (126 bytes)
Event: 0.483 Thread 0x000002f5ffb6ae00 nmethod 1140 0x000002f5e5afca10 code [0x000002f5e5afcca0, 0x000002f5e5afd648]
Event: 0.483 Thread 0x000002f5ffb6ae00 1139       3       java.nio.ByteBuffer::hasArray (20 bytes)
Event: 0.483 Thread 0x000002f5ffb6ae00 nmethod 1139 0x000002f5e5afda10 code [0x000002f5e5afdba0, 0x000002f5e5afdd48]
Event: 0.483 Thread 0x000002f5ffb6ae00 1141       3       java.lang.invoke.MemberName::<init> (139 bytes)
Event: 0.483 Thread 0x000002f5ffb6ae00 nmethod 1141 0x000002f5e5afde10 code [0x000002f5e5afe140, 0x000002f5e5aff300]
Event: 0.483 Thread 0x000002f5ffb6ae00 1142       3       java.lang.invoke.InnerClassLambdaMetafactory::getOpcodeOffset (38 bytes)
Event: 0.483 Thread 0x000002f5ffb6ae00 nmethod 1142 0x000002f5e5aff890 code [0x000002f5e5affa60, 0x000002f5e5affd30]
Event: 0.484 Thread 0x000002f5ffb6ae00 1143       3       jdk.internal.misc.Unsafe::copyMemory (33 bytes)
Event: 0.485 Thread 0x000002f5ffb6ae00 nmethod 1143 0x000002f5e5affe10 code [0x000002f5e5b000e0, 0x000002f5e5b00df0]
Event: 0.485 Thread 0x000002f5ffb6ae00 1144       3       jdk.internal.misc.Unsafe::copyMemoryChecks (21 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.006 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.010 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: trap_request=0xffffff66 fr.pc=0x000002f5ed45527c relative=0x000000000000031c
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x000002f5ed45527c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.458 Thread 0x000002f5863470f0 DEOPT PACKING pc=0x000002f5ed45527c sp=0x000000ffcb2fe1e0
Event: 0.458 Thread 0x000002f5863470f0 DEOPT UNPACKING pc=0x000002f5eceb46a2 sp=0x000000ffcb2fe120 mode 2
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002f5ed4522c0 relative=0x0000000000000280
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002f5ed4522c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.458 Thread 0x000002f5863470f0 DEOPT PACKING pc=0x000002f5ed4522c0 sp=0x000000ffcb2fe150
Event: 0.458 Thread 0x000002f5863470f0 DEOPT UNPACKING pc=0x000002f5eceb46a2 sp=0x000000ffcb2fe120 mode 2
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002f5ed4522c0 relative=0x0000000000000280
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002f5ed4522c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.458 Thread 0x000002f5863470f0 DEOPT PACKING pc=0x000002f5ed4522c0 sp=0x000000ffcb2fe150
Event: 0.458 Thread 0x000002f5863470f0 DEOPT UNPACKING pc=0x000002f5eceb46a2 sp=0x000000ffcb2fe120 mode 2
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002f5ed4522c0 relative=0x0000000000000280
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002f5ed4522c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.458 Thread 0x000002f5863470f0 DEOPT PACKING pc=0x000002f5ed4522c0 sp=0x000000ffcb2fe150
Event: 0.458 Thread 0x000002f5863470f0 DEOPT UNPACKING pc=0x000002f5eceb46a2 sp=0x000000ffcb2fe120 mode 2
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002f5ed4522c0 relative=0x0000000000000280
Event: 0.458 Thread 0x000002f5863470f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002f5ed4522c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.458 Thread 0x000002f5863470f0 DEOPT PACKING pc=0x000002f5ed4522c0 sp=0x000000ffcb2fe150
Event: 0.458 Thread 0x000002f5863470f0 DEOPT UNPACKING pc=0x000002f5eceb46a2 sp=0x000000ffcb2fe120 mode 2

Classes loaded (20 events):
Event: 0.476 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator
Event: 0.476 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator done
Event: 0.476 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator
Event: 0.476 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator done
Event: 0.476 Loading class sun/security/ssl/Finished$1
Event: 0.476 Loading class sun/security/ssl/Finished$1 done
Event: 0.476 Loading class sun/security/ssl/SSLBasicKeyDerivation
Event: 0.476 Loading class sun/security/ssl/SSLBasicKeyDerivation done
Event: 0.476 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec
Event: 0.476 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec done
Event: 0.477 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 0.477 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 0.477 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 0.477 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 0.477 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp
Event: 0.477 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp done
Event: 0.478 Loading class sun/security/ssl/CipherSuite$1
Event: 0.478 Loading class sun/security/ssl/CipherSuite$1 done
Event: 0.478 Loading class java/util/concurrent/CompletionException
Event: 0.478 Loading class java/util/concurrent/CompletionException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.262 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623750b98}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623750b98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.262 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623757758}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000623757758) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.263 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x000000062375e1e8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x000000062375e1e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.263 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623762870}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623762870) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.266 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x000000062378fa30}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000062378fa30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.267 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x000000062379ed50}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062379ed50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.270 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237d09f8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237d09f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.271 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237d72c8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237d72c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.271 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237da6e8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237da6e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.330 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006231f8a28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006231f8a28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.350 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f3b70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233f3b70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.351 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f74d8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233f74d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.355 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c25e48}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c25e48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.366 Thread 0x000002f5df746e80 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ca7a88}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ca7a88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.366 Thread 0x000002f5864f6270 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623233990}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623233990) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.371 Thread 0x000002f5864f6270 Exception <a 'java/lang/NoSuchMethodError'{0x000000062326de68}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062326de68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.372 Thread 0x000002f5863470f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1f2d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1f2d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.378 Thread 0x000002f5863470f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d71188}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d71188) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.454 Thread 0x000002f5863470f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622f88880}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000622f88880) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.478 Thread 0x000002f5863470f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622800a18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622800a18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.089 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.089 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.097 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.097 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.216 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.216 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.456 Executing VM operation: ICBufferFull
Event: 0.456 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.049 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb3cb30
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb95dc0
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb96820
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb66130
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb66b90
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb69f40
Event: 0.050 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffb6ae00
Event: 0.070 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffd47fe0
Event: 0.074 Thread 0x000002f5df746e80 Thread added: 0x000002f5ffd4c450
Event: 0.079 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.081 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.086 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.140 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.233 Thread 0x000002f5ffb6ae00 Thread added: 0x000002f586125420
Event: 0.292 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.323 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.326 Thread 0x000002f5df746e80 Thread added: 0x000002f5864f6270
Event: 0.371 Thread 0x000002f5864f6270 Thread added: 0x000002f5863470f0
Event: 0.382 Thread 0x000002f5864f6270 Thread added: 0x000002f5865bd100
Event: 0.415 Thread 0x000002f5ffb6ae00 Thread added: 0x000002f5865c7390


Dynamic libraries:
0x00007ff70eca0000 - 0x00007ff70ecaa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffb27cf0000 - 0x00007ffb27f07000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb27080000 - 0x00007ffb27144000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb24ed0000 - 0x00007ffb252a3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb255f0000 - 0x00007ffb25701000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffadc0f0000 - 0x00007ffadc108000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffadd490000 - 0x00007ffadd4ab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffb27540000 - 0x00007ffb276f1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb24de0000 - 0x00007ffb24e06000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb27780000 - 0x00007ffb277a9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb254c0000 - 0x00007ffb255e2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb25420000 - 0x00007ffb254ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb09bc0000 - 0x00007ffb09e5b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffb27820000 - 0x00007ffb278c7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb26fb0000 - 0x00007ffb26fe1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffaed160000 - 0x00007ffaed16c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffadcf80000 - 0x00007ffadd00d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffad8010000 - 0x00007ffad8dc7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffb26ee0000 - 0x00007ffb26f91000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb272b0000 - 0x00007ffb27357000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb252b0000 - 0x00007ffb252d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb27360000 - 0x00007ffb27474000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb27170000 - 0x00007ffb271e1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb24cb0000 - 0x00007ffb24cfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb17970000 - 0x00007ffb179a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb1cbc0000 - 0x00007ffb1cbca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb24c90000 - 0x00007ffb24ca3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb23e00000 - 0x00007ffb23e18000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffaea2c0000 - 0x00007ffaea2ca000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffb225e0000 - 0x00007ffb22812000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb25980000 - 0x00007ffb25d13000 	C:\WINDOWS\System32\combase.dll
0x00007ffb278d0000 - 0x00007ffb279a7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb0f2b0000 - 0x00007ffb0f2e2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb25790000 - 0x00007ffb2580b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffadc110000 - 0x00007ffadc12f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ffadc0d0000 - 0x00007ffadc0e8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffb264e0000 - 0x00007ffb26d7d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb252e0000 - 0x00007ffb2541f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb22d00000 - 0x00007ffb2361d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb25d80000 - 0x00007ffb25e8b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb277b0000 - 0x00007ffb27816000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb24d10000 - 0x00007ffb24d3b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffaea200000 - 0x00007ffaea210000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffb1f930000 - 0x00007ffb1fa5c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb24280000 - 0x00007ffb242ea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffadc0b0000 - 0x00007ffadc0c6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ffb244e0000 - 0x00007ffb244fb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb23d60000 - 0x00007ffb23d97000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb24370000 - 0x00007ffb24398000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb244d0000 - 0x00007ffb244dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb23910000 - 0x00007ffb2393d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb27150000 - 0x00007ffb27159000 	C:\WINDOWS\System32\NSI.dll
0x00007ffaec230000 - 0x00007ffaec23e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ffb25810000 - 0x00007ffb25977000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffb246b0000 - 0x00007ffb246dd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffb24670000 - 0x00007ffb246a7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa75630000 - 0x00007ffa75638000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffaec210000 - 0x00007ffaec219000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\;rogram Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 91372K (0% of 33293192K total physical memory with 921352K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 16225K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1892K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16448B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 10 days 7:03 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (899M free)
TotalPageFile size 42752M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 89M, peak: 89M
current process commit charge ("private bytes"): 631M, peak: 633M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
