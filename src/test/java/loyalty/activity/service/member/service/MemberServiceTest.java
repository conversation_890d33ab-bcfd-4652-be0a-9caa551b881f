package loyalty.activity.service.member.service;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.response.CRMMemberInfoBaseResponse;
import com.cstools.data.internal.domain.crm.response.CRMQueryMemberInfoResponse;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.member.domain.request.PclassMemberRequest;
import loyalty.activity.service.member.domain.response.PclassMemberResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class MemberServiceTest extends BaseTest {

    @InjectMocks
    private MemberService service;
    @Mock
    private CRMClient crmClient;
    private PclassMemberRequest pclassMemberRequest;
    private CRMQueryMemberInfoResponse memberInfoResponse;
    private CRMMemberInfoBaseResponse crmMemberInfoResponse;

    @Before
    public void setUp() throws Exception {
        pclassMemberRequest = new PclassMemberRequest();
        pclassMemberRequest.setCellphone(cellphone);
        pclassMemberRequest.setOpenid("oFDxX4woupj3qtiUG1gao_gq_f9s");
        pclassMemberRequest.setUnionId("oDSWq1YvfDFsXSkjFdVNZ0H-oZS0");

        crmMemberInfoResponse = new CRMMemberInfoBaseResponse();
        crmMemberInfoResponse.setCellphone(cellphone);
        crmMemberInfoResponse.setBabyStatus(1);
        crmMemberInfoResponse.setBabyBirth("2023-03-22");

        memberInfoResponse = new CRMQueryMemberInfoResponse();
        memberInfoResponse.setErrCode("200");
        memberInfoResponse.setStatus("200");
        memberInfoResponse.setCrmMemberInfoResponse(crmMemberInfoResponse);
    }

    @Test
    public void getPclassMemberBaseInfo() throws Exception {

        Mockito.when(crmClient.queryMemberInfo(Mockito.any()))
                .thenReturn(memberInfoResponse);
        PclassMemberResponse pclassMemberBaseInfo = service.getPclassMemberBaseInfo(pclassMemberRequest);
        Assert.assertEquals(pclassMemberBaseInfo.getCellphone(),cellphone);
    }
}