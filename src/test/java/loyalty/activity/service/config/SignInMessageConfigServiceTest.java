package loyalty.activity.service.config;

import loyalty.activity.service.common.dto.SignInHistory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * SignInMessageConfigService 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class SignInMessageConfigServiceTest {

    @InjectMocks
    private SignInMessageConfigService signInMessageConfigService;

    @Before
    public void setUp() {
        // 模拟院外活动配置值
        ReflectionTestUtils.setField(signInMessageConfigService, "outsideSmallFirstMessages",
            "高端妈妈班:恭喜您首次签到小型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到小型豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "outsideSmallSecondMessages",
            "高端妈妈班:欢迎您再次参加小型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加小型豪华妈妈班活动！");

        ReflectionTestUtils.setField(signInMessageConfigService, "outsideMediumFirstMessages",
            "高端妈妈班:恭喜您首次签到中型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到中型豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "outsideMediumSecondMessages",
            "高端妈妈班:欢迎您再次参加中型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加中型豪华妈妈班活动！");

        // 模拟院内活动配置值
        ReflectionTestUtils.setField(signInMessageConfigService, "insideFirstMessages",
            "高端妈妈班:恭喜您首次签到院内高端妈妈班活动！,豪华妈妈班:恭喜您首次签到院内豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "insideSecondMessages",
            "高端妈妈班:欢迎您再次参加院内高端妈妈班活动！,豪华妈妈班:欢迎您再次参加院内豪华妈妈班活动！");

        // 模拟CS活动配置值
        ReflectionTestUtils.setField(signInMessageConfigService, "csFirstMessages",
            "CS活动:恭喜您首次签到CS活动！,专家讲座:恭喜您首次签到专家讲座活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "csSecondMessages",
            "CS活动:欢迎您再次参加CS活动！,专家讲座:欢迎您再次参加专家讲座活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "csThirdMessages",
            "CS活动:感谢您的持续参与CS活动！,专家讲座:感谢您的持续参与专家讲座活动！");
    }

    @Test
    public void testGetSignInSuccessMessage_OutsideSmallActivity_FirstTime() {
        List<SignInHistory> emptyHistory = new ArrayList<>();
        String message = signInMessageConfigService.getSignInSuccessMessage("OUTSIDE_PCLASS", "小型活动", "高端妈妈班", emptyHistory);
        assertEquals("恭喜您首次签到小型高端妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_OutsideSmallActivity_SecondTime() {
        List<SignInHistory> historyWithOneRecord = createSignInHistory("OUTSIDE_PCLASS", 1);
        String message = signInMessageConfigService.getSignInSuccessMessage("OUTSIDE_PCLASS", "小型活动", "高端妈妈班", historyWithOneRecord);
        assertEquals("欢迎您再次参加小型高端妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_InsideActivity_FirstTime() {
        List<SignInHistory> emptyHistory = new ArrayList<>();
        String message = signInMessageConfigService.getSignInSuccessMessage("INSIDE_PCLASS", "中型活动", "高端妈妈班", emptyHistory);
        assertEquals("恭喜您首次签到院内高端妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_InsideActivity_SecondTime() {
        List<SignInHistory> historyWithOneRecord = createSignInHistory("INSIDE_PCLASS", 1);
        String message = signInMessageConfigService.getSignInSuccessMessage("INSIDE_PCLASS", "大型活动", "豪华妈妈班", historyWithOneRecord);
        assertEquals("欢迎您再次参加院内豪华妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_CSActivity_FirstTime() {
        List<SignInHistory> emptyHistory = new ArrayList<>();
        String message = signInMessageConfigService.getSignInSuccessMessage("CS", null, "CS活动", emptyHistory);
        assertEquals("恭喜您首次签到CS活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_CSActivity_SecondTime() {
        List<SignInHistory> historyWithOneRecord = createSignInHistory("CS", 1);
        String message = signInMessageConfigService.getSignInSuccessMessage("CS", null, "专家讲座", historyWithOneRecord);
        assertEquals("欢迎您再次参加专家讲座活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_CSActivity_ThirdTime() {
        List<SignInHistory> historyWithTwoRecords = createSignInHistory("CS", 2);
        String message = signInMessageConfigService.getSignInSuccessMessage("CS", null, "CS活动", historyWithTwoRecords);
        assertEquals("感谢您的持续参与CS活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_UnknownSignInType() {
        List<SignInHistory> emptyHistory = new ArrayList<>();
        String message = signInMessageConfigService.getSignInSuccessMessage("UNKNOWN", "小型活动", "高端妈妈班", emptyHistory);
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_UnknownActivityType() {
        List<SignInHistory> emptyHistory = new ArrayList<>();
        String message = signInMessageConfigService.getSignInSuccessMessage("OUTSIDE_PCLASS", "小型活动", "未知活动", emptyHistory);
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_NullParameters() {
        String message = signInMessageConfigService.getSignInSuccessMessage(null, null, null, null);
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetDefaultSuccessMessage() {
        String message = signInMessageConfigService.getDefaultSuccessMessage();
        assertEquals("签到成功！", message);
    }

    /**
     * 创建签到历史记录
     */
    private List<SignInHistory> createSignInHistory(String channel, int count) {
        List<SignInHistory> historyList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            SignInHistory history = new SignInHistory();
            history.setChannel(channel);
            history.setSignInTime(new Date());
            historyList.add(history);
        }
        return historyList;
    }
}
