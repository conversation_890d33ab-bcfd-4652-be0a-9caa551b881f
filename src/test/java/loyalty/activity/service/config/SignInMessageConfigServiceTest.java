package loyalty.activity.service.config;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;

/**
 * SignInMessageConfigService 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class SignInMessageConfigServiceTest {

    @InjectMocks
    private SignInMessageConfigService signInMessageConfigService;

    @Before
    public void setUp() {
        // 模拟配置值
        ReflectionTestUtils.setField(signInMessageConfigService, "smallActivityMessages", 
            "高端妈妈班:恭喜您成功签到小型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到小型豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "mediumActivityMessages", 
            "高端妈妈班:恭喜您成功签到中型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到中型豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "largeActivityMessages", 
            "高端妈妈班:恭喜您成功签到大型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到大型豪华妈妈班活动！");
        ReflectionTestUtils.setField(signInMessageConfigService, "csActivityMessages", 
            "CS活动:恭喜您成功签到CS活动！,专家讲座:恭喜您成功签到专家讲座活动！");
    }

    @Test
    public void testGetSignInSuccessMessage_SmallActivity_HighEnd() {
        String message = signInMessageConfigService.getSignInSuccessMessage("小型活动", "高端妈妈班");
        assertEquals("恭喜您成功签到小型高端妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_MediumActivity_Luxury() {
        String message = signInMessageConfigService.getSignInSuccessMessage("中型活动", "豪华妈妈班");
        assertEquals("恭喜您成功签到中型豪华妈妈班活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_CSActivity() {
        String message = signInMessageConfigService.getSignInSuccessMessage("CS", "CS活动");
        assertEquals("恭喜您成功签到CS活动！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_UnknownPclassType() {
        String message = signInMessageConfigService.getSignInSuccessMessage("未知类型", "高端妈妈班");
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_UnknownActivityType() {
        String message = signInMessageConfigService.getSignInSuccessMessage("小型活动", "未知活动");
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_NullParameters() {
        String message = signInMessageConfigService.getSignInSuccessMessage(null, null);
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetSignInSuccessMessage_EmptyParameters() {
        String message = signInMessageConfigService.getSignInSuccessMessage("", "");
        assertEquals("签到成功！", message);
    }

    @Test
    public void testGetDefaultSuccessMessage() {
        String message = signInMessageConfigService.getDefaultSuccessMessage();
        assertEquals("签到成功！", message);
    }
}
