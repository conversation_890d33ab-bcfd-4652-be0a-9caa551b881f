package loyalty.activity.service.external.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.external.db.mapper.CSActivityMapper;
import loyalty.activity.service.external.db.model.SignupDataPO;
import loyalty.activity.service.external.domain.request.CSActivityQuerySignupRequest;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.request.CSActivitySyncRequest;
import loyalty.activity.service.external.domain.response.CSActivityQuerySignupResponse;
import loyalty.activity.service.external.domain.response.ActivitySyncResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;


public class CSActivityServiceTest extends BaseTest {

    @InjectMocks
    private CSActivityService service;
    @Mock
    private ActivityMapper activityMapper;
    @Mock
    private PclassInfoMapper pclassInfoMapper;
    @Mock
    private ActivityOwnerRelMapper activityOwnerRelMapper;
    @Mock
    private CSActivityMapper csActivityMapper;
    private CSActivitySyncRequest syncRequest;
    private CSActivitySyncRequest.ActivityInfo activityInfo;
    private CSActivityQuerySignupRequest querySignupRequest;
    private SignupDataPO signupDataPO;

    @Before
    public void init() {
        syncRequest = new CSActivitySyncRequest();
        activityInfo = new CSActivitySyncRequest.ActivityInfo();
        activityInfo.setActivityCode("N221219Testcs200001");
        activityInfo.setActivityType("羊奶望闻冲饮");
        activityInfo.setStoreCode("20730554");
        activityInfo.setEventDate("2023-03-21");
        activityInfo.setOwnerId("5005788");
        activityInfo.setOwnerName("欧阳玮娜");
        syncRequest.setActivityList(Collections.singletonList(activityInfo));

        querySignupRequest = new CSActivityQuerySignupRequest();
        querySignupRequest.setActivityCode("N221219Testcs200001");

        signupDataPO = new SignupDataPO();
        signupDataPO.setActivityCode("N221219Testcs200001");
        signupDataPO.setSignupNum(1);
        signupDataPO.setActivityId("1");
    }

    @Test
    public void syncSuc() {
        ActivitySyncResponse sync = service.sync(syncRequest);
        Assert.assertNull(sync);
    }

    @Test
    public void syncFail() {
        activityInfo.setEventDate(null);
        ActivitySyncResponse sync = service.sync(syncRequest);
        Assert.assertEquals(java.util.Optional.ofNullable(sync.getFailSize()), java.util.Optional.of(1));
    }

    @Test
    public void querySignup() {
        Mockito.when(csActivityMapper.queryCSActivitySignupData("N221219Testcs200001",null,
                null,null,20,ActivityChannel.CS.name())).thenReturn(Collections.singletonList(signupDataPO));
        CSActivityQuerySignupResponse csActivityQuerySignupResponse = service.querySignup(querySignupRequest);
        Assert.assertNotNull(csActivityQuerySignupResponse.getSignupData());
    }
}