package loyalty.activity.service.external.service;

import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.external.db.app.ParamClassInfo;
import loyalty.activity.service.external.db.app.ParamQueryClassRequest;
import loyalty.activity.service.external.db.app.StoreClassInfo;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.Date;
import java.util.List;

public class NcpQueryServiceTest extends BaseTest {

    @InjectMocks
    private NcpQueryService service;
    @Mock
    private ClassInfoMapper classInfoMapper;
    private ParamQueryClassRequest paramQueryClassRequest;
    private ParamClassInfo paramClassInfo;
    private StoreClassInfo storeClassInfo;

    @Before
    public void setUp() throws Exception {
        paramQueryClassRequest = new ParamQueryClassRequest();
        paramQueryClassRequest.setClassesCode(classesCode);
        paramQueryClassRequest.setStatus("执行中");
        paramQueryClassRequest.setStartTime(new Date());
        paramQueryClassRequest.setEndTime(new Date());
        paramQueryClassRequest.setPage(1);
        paramQueryClassRequest.setLimit(10);

        paramClassInfo = new ParamClassInfo();
        paramClassInfo.setClassesCode(classesCode);

        storeClassInfo = new StoreClassInfo();
        storeClassInfo.setEventNumber(classesCode);
    }

    @Test
    public void queryClassInfoForQueryParam() {
        Mockito.when(classInfoMapper.getClassesByParam(paramQueryClassRequest))
                .thenReturn(Collections.singletonList(paramClassInfo));
        PageResponse<ParamClassInfo> paramClassInfoPageResponse = service.queryClassInfoForQueryParam(paramQueryClassRequest);
        Assert.assertEquals(classesCode,paramClassInfoPageResponse.getResults().get(0).getClassesCode());
    }

    @Test
    public void queryClassInfoForStore() {
        String storeId = "12345";
        Mockito.when(classInfoMapper.getClassesByStoreId(storeId))
                .thenReturn(Collections.singletonList(storeClassInfo));
        List<StoreClassInfo> storeClassInfos = service.queryClassInfoForStore(storeId);
        Assert.assertEquals(storeClassInfos.get(0).getEventNumber(),classesCode);
    }
}