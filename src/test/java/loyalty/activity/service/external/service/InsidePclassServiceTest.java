package loyalty.activity.service.external.service;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMRegisterMemberRequest;
import com.cstools.data.internal.domain.crm.request.CRMRetrieveMemberRequest;
import com.cstools.data.internal.domain.crm.response.CRMMemberBaseInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMRetrieveMemberResponse;
import com.cstools.data.internal.domain.crm.response.InternalCRMRestResponse;
import com.google.gson.Gson;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.error.*;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper;
import loyalty.activity.service.external.db.model.CeRegisterLog;
import loyalty.activity.service.external.domain.request.InsidePClassRegisterRequest;
import loyalty.activity.service.external.domain.request.InsidePClassSignInRequest;
import loyalty.activity.service.external.domain.request.InsidePClassUpdateMemberInfoRequest;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.policy.pclass.signIn.InsidePclassSignIn;
import loyalty.activity.service.policy.pclass.signIn.SignInPolicy;
import loyalty.activity.service.policy.pclass.signIn.UserSignIn;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


public class InsidePclassServiceTest extends BaseTest {

    @InjectMocks
    private InsidePclassService service;
    @Spy
    private SignInPolicy signInPolicy;
    @Spy
    private PclassSignInService pclassSignInService;
    @Mock
    private CRMClient crmClient;
    @Mock
    private PclassInfoMapper pclassInfoMapper;
    @Mock
    private ClassInfoMapper classInfoMapper;
    @Mock
    private ClassOwnerDataMapper classOwnerDataMapper;
    @Mock
    private ActivitySignInMapper activitySignInMapper;
    @Mock
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Mock
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Mock
    private ActivityUserSigninMapper activityUserSigninMapper;
    @Mock
    private PNECClassService pnecClassService;

    private InsidePClassRegisterRequest insidePClassRegisterRequest;
    private InternalCRMRestResponse internalCRMRestResponse;
    private InsidePClassSignInRequest insidePClassSignInRequest;
    private InsidePClassUpdateMemberInfoRequest insidePClassUpdateMemberInfoRequest;
    private CeRegisterLog ceRegisterLog;
    private InsidePclassSignIn insidePclassSignIn = new InsidePclassSignIn();
    private final Map<String, UserSignIn> SIGN_IN_STRATEGY_MAP = new HashMap<>();
    private PclassInfoApplyCount pclassInfoApplyCount;
    private CRMRetrieveMemberResponse memberResponse;
    private UserSignInResponse userSignInResponse;
    private ActivityUserSignin activityUserSignin;
    private Activity activity;

    @Before
    public void setUp() throws Exception {
        insidePClassRegisterRequest = new InsidePClassRegisterRequest();
        insidePClassRegisterRequest.setMobile(cellphone);
        insidePClassRegisterRequest.setType(1);
        insidePClassRegisterRequest.setCode(classesCode);

        internalCRMRestResponse = new InternalCRMRestResponse();
        internalCRMRestResponse.setErrCode("200");
        internalCRMRestResponse.setStatus("200");

        insidePClassSignInRequest = new InsidePClassSignInRequest();
        insidePClassSignInRequest.setCode(classesCode);
        insidePClassSignInRequest.setMobile(cellphone);

        ReflectionTestUtils.setField(insidePclassSignIn, "activitySignInMapper", activitySignInMapper);
        ReflectionTestUtils.setField(insidePclassSignIn, "pclassApplyQueryMapper", pclassApplyQueryMapper);
        ReflectionTestUtils.setField(insidePclassSignIn, "pclassInfoQueryMapper", pclassInfoQueryMapper);
        ReflectionTestUtils.setField(insidePclassSignIn, "pclassSignInService", pclassSignInService);
        ReflectionTestUtils.setField(insidePclassSignIn, "activityUserSigninMapper", activityUserSigninMapper);
        ReflectionTestUtils.setField(insidePclassSignIn, "crmClient", crmClient);
        ReflectionTestUtils.setField(insidePclassSignIn, "pnecClassService", pnecClassService);
        SIGN_IN_STRATEGY_MAP.put(ActivityChannel.INSIDE_PCLASS.name(), insidePclassSignIn);
        ReflectionTestUtils.setField(signInPolicy, "SIGN_IN_STRATEGY_MAP", SIGN_IN_STRATEGY_MAP);
        ReflectionTestUtils.setField(pclassSignInService, "signInPolicy", signInPolicy);
        ReflectionTestUtils.setField(pclassSignInService, "activityUserSigninMapper", activityUserSigninMapper);

        insidePClassUpdateMemberInfoRequest = new InsidePClassUpdateMemberInfoRequest();
        insidePClassUpdateMemberInfoRequest.setCellphone(cellphone);
        insidePClassUpdateMemberInfoRequest.setOpenid(openid);

        ceRegisterLog = new CeRegisterLog();

        pclassInfoApplyCount = new PclassInfoApplyCount();
        pclassInfoApplyCount.setId(1L);
        pclassInfoApplyCount.setIsDeleted(false);
        pclassInfoApplyCount.setIsEnabled(true);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, -1);
        pclassInfoApplyCount.setStartTime(instance.getTime());
        instance.add(Calendar.DATE, 2);
        pclassInfoApplyCount.setEndTime(instance.getTime());
        pclassInfoApplyCount.setId(1L);

        memberResponse = new CRMRetrieveMemberResponse();
        CRMMemberBaseInfoResponse memberInfo = new CRMMemberBaseInfoResponse();
        memberResponse.setMemberInfo(memberInfo);
        memberResponse.setErrCode("200");
        memberResponse.setStatus("200");

        userSignInResponse = new UserSignInResponse();
        userSignInResponse.setId(1);

        activityUserSignin = new ActivityUserSignin();
        activityUserSignin.setInviterId("1");

        activity = new Activity();
        instance.add(Calendar.DATE, -1);
        activity.setStartTime(instance.getTime());
        instance.add(Calendar.DATE, 1);
        activity.setEndTime(instance.getTime());

        when(pclassInfoMapper.getActivityIdByClassesCode(classesCode)).thenReturn(1);
        when(pclassInfoQueryMapper.getPclassInfoSignInCountById(1)).thenReturn(pclassInfoApplyCount);
        CRMRetrieveMemberRequest crmRetrieveMemberRequest = new CRMRetrieveMemberRequest();
        crmRetrieveMemberRequest.setCellPhone(cellphone);
        when(crmClient.getMemberBaseInfo(crmRetrieveMemberRequest))
                .thenReturn(memberResponse);
    }

    //@Test
    public void register() throws Exception {
        Mockito.when(crmClient.registerMember(Mockito.any(CRMRegisterMemberRequest.class)))
                .thenReturn(internalCRMRestResponse);
        service.register(insidePClassRegisterRequest, ceRegisterLog);
        Assert.assertEquals(ceRegisterLog.getCrmResponse(), new Gson().toJson(internalCRMRestResponse));
    }

    //@Test(expected = CRMRegisterFailException.class)
    public void registerCRMRegisterFailException() throws Exception {
        internalCRMRestResponse.setStatus("104");
        Mockito.when(crmClient.registerMember(Mockito.any(CRMRegisterMemberRequest.class)))
                .thenReturn(internalCRMRestResponse);
        service.register(insidePClassRegisterRequest, ceRegisterLog);
        Assert.fail();
    }

    //@Test(expected = CRMSysException.class)
    public void registerCRMSysException() throws Exception {
        internalCRMRestResponse.setErrCode("201");
        Mockito.when(crmClient.registerMember(Mockito.any(CRMRegisterMemberRequest.class)))
                .thenReturn(internalCRMRestResponse);
        service.register(insidePClassRegisterRequest, ceRegisterLog);
        Assert.fail();
    }

    //@Test(expected = CRMOtherException.class)
    public void registerCRMOtherException() throws Exception {
        internalCRMRestResponse.setErrCode("301");
        Mockito.when(crmClient.registerMember(Mockito.any(CRMRegisterMemberRequest.class)))
                .thenReturn(internalCRMRestResponse);
        service.register(insidePClassRegisterRequest, ceRegisterLog);
        Assert.fail();
    }

    //@Test
    public void signIn() {
        when(pclassApplyQueryMapper.getSignInHistoryQrCode(anyString(), anyInt())).thenReturn(userSignInResponse);
        when(activityUserSigninMapper.getByActivityIdCellphone("1", cellphone)).thenReturn(new ActivityUserSignin());
        when(pnecClassService.getPersonQrCode("1")).thenReturn("1");
        when(pclassApplyQueryMapper.getSignInHistoryQrCode(cellphone, 1)).thenReturn(new UserSignInResponse());
        service.signIn(insidePClassSignInRequest);
    }

    //@Test(expected = PclassAlreadySignInException.class)
    public void signInPclassAlreadySignInException() {
        when(pnecClassService.getPersonQrCode("1")).thenReturn("1");
        when(activityUserSigninMapper.getByActivityIdCellphone("1", cellphone)).thenReturn(activityUserSignin);
        when(pclassApplyQueryMapper.getSignInHistoryQrCode(cellphone, 1))
                .thenReturn(userSignInResponse);
        service.signIn(insidePClassSignInRequest);
        Assert.fail();
    }

    //@Test(expected = SignInHasStartingClassException.class)
    public void signInSignInHasStartingClassException() {
        when(pclassInfoQueryMapper.getPclassInfoSignInCountById(1)).thenReturn(pclassInfoApplyCount);
        when(activitySignInMapper.getStartClassByCellphone(anyString(), any(Date.class), any(), anyString()))
                .thenReturn(Collections.singletonList(activity));
        service.signIn(insidePClassSignInRequest);
        Assert.fail();
    }

    //@Test(expected = PclassExpiredException.class)
    public void signInPclassExpiredException() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        pclassInfoApplyCount.setEndTime(calendar.getTime());
        service.signIn(insidePClassSignInRequest);
        Assert.fail();
    }

    //@Test(expected = PclassNotBeginException.class)
    public void signInPclassNotBeginException() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        pclassInfoApplyCount.setStartTime(calendar.getTime());
        service.signIn(insidePClassSignInRequest);
        Assert.fail();
    }

    @Test
    public void bindWeChat() {
        service.bindWeChat(insidePClassUpdateMemberInfoRequest);
    }

    @Test
    public void insertCeRegisterLogEntity() {
        service.insertCeRegisterLogEntity(ceRegisterLog);
    }
}