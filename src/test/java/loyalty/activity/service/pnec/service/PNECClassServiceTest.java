package loyalty.activity.service.pnec.service;

import com.cstools.data.internal.client.NCPV2Client;
import com.cstools.data.internal.domain.ncp.response.NCPQrCodeResponse;
import com.cstools.data.internal.domain.ncp.response.NCPQrcodeQueryResponse;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.pclass.service.InternalInsidePClassService;
import loyalty.activity.service.pclass.service.PclassEmailService;
import loyalty.activity.service.pnec.db.mapper.PNECClassMapper;
import loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper;
import loyalty.activity.service.pnec.db.model.PClassSignInData;
import loyalty.activity.service.pnec.db.model.PNECClassInfo;
import loyalty.activity.service.pnec.db.model.PNECClassInfoData;
import loyalty.activity.service.pnec.db.model.PNECClassInfoDetail;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;
import loyalty.activity.service.pnec.dto.PNECClassInfoDataDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDetailDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDto;
import loyalty.activity.service.pnec.dto.PclassResearchInfoDto;
import loyalty.activity.service.sharelib.rest.client.MallInternalClient;
import loyalty.activity.service.sharelib.rest.response.MallInternalResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class PNECClassServiceTest extends BaseTest {
    @InjectMocks
    private PNECClassService service;
    @Mock
    private NCPV2Client ncpClient;
    @Mock
    private MallInternalClient mallInternalClient;
    @Mock
    private PclassResearchInfoMapper pclassResearchMapper;
    @Mock
    private InternalInsidePClassService internalInsidePClassService;
    @Mock
    private PclassEmailService pclassEmailService;
    @Mock
    private SecurityContextHolder securityContextHolder;
    @Mock
    private SecurityContext securityContext;
    @Mock
    private SecurityContextHolderStrategy strategy;
    @Mock
    private Authentication authentication;
    @Mock
    private PNECClassMapper pnecClassMapper;
    @Mock
    private HttpServletResponse httpServletResponse;
    @Mock
    private CmpService cmpService;
    @Mock
    private CrmPgService crmPgService;
    private SearchCriteria searchCriteria;
    private SearchCriteria.Criteria criteria;
    private PNECClassInfo pnecClassInfo;
    private String userId;
    private MallInternalResponse<List<String>> mallInternalResponse;
    private PclassResearchInfo pclassResearchInfo;
    private PNECClassInfoDetail pnecClassInfoDetail;
    private PNECClassInfoData pnecClassInfoData;
    private NCPQrcodeQueryResponse ncpQrCodeResponse;
    private PClassSignInData pClassSignInData;
    @Mock
    private ClassInfoMapper classInfoMapper;

    @Before
    public void setUp() throws Exception {
        userId = cellphone;

        searchCriteria = new SearchCriteria();
        criteria = new SearchCriteria.Criteria();
        List<SearchCriteria.Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(criteria);
        searchCriteria.setCriterias(criteriaList);
        searchCriteria.setPage(1);
        searchCriteria.setLimit(10);

        pnecClassInfo = new PNECClassInfo();
        pnecClassInfo.setId("1");
        pnecClassInfo.setClassesCode(classesCode);
        pnecClassInfo.setClassTime("2021-03-16 06:00:00");
        pnecClassInfo.setEndTime("2021-03-16 23:59:59");
        pnecClassInfo.setChannel(ActivityChannel.INSIDE_PCLASS.name());
        pnecClassInfo.setStatus(true);

        mallInternalResponse = new MallInternalResponse<>();
        mallInternalResponse.setBody(Collections.singletonList("12345"));

        pclassResearchInfo = new PclassResearchInfo();
        pclassResearchInfo.setResearchId("1");
        pclassResearchInfo.setResearchInstitutionName("明州人民医院");

        pnecClassInfoDetail = new PNECClassInfoDetail();
        pnecClassInfoDetail.setClassCode(classesCode);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,29);
        pnecClassInfoDetail.setStartTime(instance.getTime());
        instance.add(Calendar.DATE,1);
        pnecClassInfoDetail.setEndTime(instance.getTime());
        pnecClassInfoDetail.setStatus(true);
        pnecClassInfoDetail.setPclassType("《非独家赞助妈妈班》- 院内");
        pnecClassInfoDetail.setChannel(ActivityChannel.INSIDE_PCLASS.name());

        pnecClassInfoData = new PNECClassInfoData();
        pnecClassInfoData.setSignInNum(1);

        ncpQrCodeResponse = new NCPQrcodeQueryResponse();
        ncpQrCodeResponse.setCode("0");
        NCPQrcodeQueryResponse.QrCodeInfo qrCodeInfo = new NCPQrcodeQueryResponse.QrCodeInfo();
        qrCodeInfo.setQrCode(qrcode);
        ncpQrCodeResponse.setQrCodeInfo(qrCodeInfo);

        pClassSignInData = new PClassSignInData();
        pClassSignInData.setClassesCode(classesCode);
        pClassSignInData.setCellphone(cellphone);

        ReflectionTestUtils.setField(service,"ncpUrl",qrcode);
        ReflectionTestUtils.setField(service,"excelGeneratePath","src/test/resources");
        ReflectionTestUtils.setField(securityContextHolder,"strategy",strategy);
        when(strategy.getContext()).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn("QYWX_CODE_TOKEN ");
        when(authentication.getDetails()).thenReturn(userId);
        when(pnecClassMapper.getPClassInfoDetailByPClassId("1"))
                .thenReturn(pnecClassInfoDetail);
        when(pnecClassMapper.getPClassInfoDetailByActivityId("1")).thenReturn(pnecClassInfoDetail);
        when(pnecClassMapper.getSignInDataByPclassId("1")).thenReturn(Collections.singletonList(pClassSignInData));
        when(httpServletResponse.getOutputStream()).thenReturn(new ServletOutputStream() {
            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setWriteListener(WriteListener writeListener) {

            }

            @Override
            public void write(int b) throws IOException {

            }
        });
    }

    @Test
    public void list() throws ParseException {
        List<PNECClassInfo> pnecClassInfos = new ArrayList<>();
        pnecClassInfos.add(pnecClassInfo);
        when(pnecClassMapper.list(any(SearchCriteria.class),anyString(),any()))
                .thenReturn(pnecClassInfos);
        PageResponse<PNECClassInfoDto> list = service.list(searchCriteria);
        Assert.assertEquals(pnecClassInfo.getClassesCode(),list.getResults().get(0).getClassesCode());
    }

    @Test
    public void listNcCode() throws ParseException {
        List<PNECClassInfo> pnecClassInfos = new ArrayList<>();
        pnecClassInfos.add(pnecClassInfo);
        when(mallInternalClient.getStoreCodeListByNcId(userId))
                .thenReturn(mallInternalResponse);
        when(pnecClassMapper.list(any(SearchCriteria.class),anyString(),any()))
                .thenReturn(pnecClassInfos);
        PageResponse<PNECClassInfoDto> list = service.list(searchCriteria);
        Assert.assertEquals(pnecClassInfo.getClassesCode(),list.getResults().get(0).getClassesCode());
    }

    @Test
    public void listResearch() throws ParseException {
        criteria.setKey("channel");
        criteria.setValue("RESEARCH");
        when(pclassResearchMapper.list(any(SearchCriteria.class)))
                .thenReturn(Collections.singletonList(pclassResearchInfo));
        PageResponse<PNECClassInfoDto> list = service.list(searchCriteria);
        Assert.assertEquals("明州人民医院",list.getResults().get(0).getTopic());
    }

    //@Test
    public void getDetail() throws ParseException {
        PNECClassInfoDetailDto detail = service.getDetail("1");
        Assert.assertEquals(detail.getClassCode(),classesCode);
    }

    //@Test
    public void getDetailGenCode() throws Exception {
        pnecClassInfoDetail.setStartTime(new Date());
        when(internalInsidePClassService.youShengSignInQrcodeGenerate(classesCode))
                .thenReturn(qrcode);
        when(classInfoMapper.getActivityTypeByCode("1")).thenReturn("妈妈班");
        PNECClassInfoDetailDto detail = service.getDetail("1");
        Assert.assertEquals(detail.getQrcodeUrl(),qrcode);
    }

    //@Test
    public void getData() {
        when(pnecClassMapper.getPClassInfoDataByActivityId("1"))
                .thenReturn(pnecClassInfoData);
        PNECClassInfoDataDto data = service.getData("1");
        Assert.assertSame(data.getSignInNum(),1);
    }

    @Test
    public void getPersonQrCode() {
        when(ncpClient.getContactWayQrCode(any()))
                .thenReturn(ncpQrCodeResponse);
        String personQrCode = service.getPersonQrCode(userId);
        Assert.assertEquals(personQrCode,qrcode);
    }

    @Test
    public void researchDetail() {
        when(pclassResearchMapper.getDetailByResearchId("1")).thenReturn(pclassResearchInfo);
        MallInternalResponse<Integer> internalResponse = new MallInternalResponse<>();
        internalResponse.setBody(1);
        when(mallInternalClient.getRecruitmentCount(userId,"1"))
                .thenReturn(internalResponse);
        PclassResearchInfoDto pclassResearchInfoDto = service.researchDetail("1");
        Assert.assertEquals(pclassResearchInfoDto.getResearchInstitutionName(),pclassResearchInfo.getResearchInstitutionName());
    }

    @Test
    public void listSignInData() {
        PageResponse<PClassSignInData> dataPageResponse = service.listSignInData("1", searchCriteria);
        Assert.assertEquals(dataPageResponse.getResults().get(0).getClassesCode(),classesCode);
    }

    //@Test
    public void exportSignInData() throws UnsupportedEncodingException {
        service.exportSignInData("1",httpServletResponse);
    }

//    @Test
//    public void sendEmailForSignInData() throws Exception {
//        service.sendEmailForSignInData("1","<EMAIL>");
//    }

}