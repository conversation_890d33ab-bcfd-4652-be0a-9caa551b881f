package loyalty.activity.service;

import org.junit.Before;
import org.mockito.MockitoAnnotations;


public abstract class BaseTest {
    protected final String classesCode = "PCEOC6-20230168549";
    protected final String cellphone = "13294193948";
    protected final String qrcode = "https://www.baidu.com";
    protected final String provinceName = "广东省";
    protected final String cityName = "广州市";
    protected final String openid = "oFDxX4woupj3qtiUG1gao_gq_f9s";

    @Before
    public void initMocks() {
        MockitoAnnotations.openMocks(this);
    }
}
