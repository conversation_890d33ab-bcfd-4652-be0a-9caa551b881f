package loyalty.activity.service.pclass.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.error.PclassExpiredException;
import loyalty.activity.service.error.PclassOutOfLimitException;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.db.model.SignUpHistory;
import loyalty.activity.service.pclass.domain.request.ExpectCitiesRequest;
import loyalty.activity.service.pclass.domain.request.SignUpHistoryRequest;
import loyalty.activity.service.pclass.domain.request.UserApplyRequest;
import loyalty.activity.service.pclass.domain.response.SignUpHistoryResponse;
import loyalty.activity.service.pclass.domain.response.UserSignUpResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Calendar;

import static org.mockito.Mockito.when;

public class PclassSignUpServiceTest extends BaseTest {

    @InjectMocks
    private PclassSignUpService service;
    @Mock
    private ActivityUserSignupMapper activityUserSignupMapper;
    @Mock
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Mock
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Mock
    private PclassUserExpectCityRecordMapper pclassUserExpectCityRecordMapper;
    @Mock
    private PclassUserSignupFormMapper pclassUserSignupFormMapper;
    @Mock
    private PNECClassService pnecClassService;
    private UserApplyRequest userApplyRequest;
    private PclassInfoApplyCount pclassInfoApplyCount;
    private ExpectCitiesRequest expectCitiesRequest;
    private SignUpHistoryRequest signUpHistoryRequest;

    @Before
    public void setUp() throws Exception {
        userApplyRequest = new UserApplyRequest();
        userApplyRequest.setActivityId(1);
        userApplyRequest.setCellphone(cellphone);

        pclassInfoApplyCount = new PclassInfoApplyCount();
        pclassInfoApplyCount.setId(1L);
        pclassInfoApplyCount.setIsDeleted(false);
        pclassInfoApplyCount.setIsEnabled(true);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,-1);
        pclassInfoApplyCount.setStartTime(instance.getTime());
        instance.add(Calendar.DATE,2);
        pclassInfoApplyCount.setEndTime(instance.getTime());
        pclassInfoApplyCount.setApplyCount(1);
        pclassInfoApplyCount.setLimitCount(2);
        pclassInfoApplyCount.setIsOnline(true);
        pclassInfoApplyCount.setOwnerId("1");

        expectCitiesRequest = new ExpectCitiesRequest();
        expectCitiesRequest.setExpectCity(cityName);
        expectCitiesRequest.setCellphone(cellphone);
        expectCitiesRequest.setProvince(provinceName);

        signUpHistoryRequest = new SignUpHistoryRequest();
        signUpHistoryRequest.setCellphone(cellphone);
        signUpHistoryRequest.setClassesCode(classesCode);
    }

    @Test
    public void userApply() {
        when(pclassInfoQueryMapper.getPclassInfoApplyCountById(1,cellphone))
                .thenReturn(pclassInfoApplyCount);
        when(pnecClassService.getPersonQrCode("1"))
                .thenReturn(qrcode);
        UserSignUpResponse userSignUpResponse = service.userApply(userApplyRequest);
        Assert.assertEquals(userSignUpResponse.getQrCode(),qrcode);
    }

    @Test(expected = PclassExpiredException.class)
    public void userApplyPclassExpiredException() {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,-1);
        pclassInfoApplyCount.setEndTime(instance.getTime());
        when(pclassInfoQueryMapper.getPclassInfoApplyCountById(1,cellphone))
                .thenReturn(pclassInfoApplyCount);
        when(pnecClassService.getPersonQrCode("1"))
                .thenReturn(qrcode);
        service.userApply(userApplyRequest);
        Assert.fail();
    }


    @Test(expected = PclassOutOfLimitException.class)
    public void userApplyPclassOutOfLimitException() {
        pclassInfoApplyCount.setLimitCount(1);
        when(pclassInfoQueryMapper.getPclassInfoApplyCountById(1,cellphone))
                .thenReturn(pclassInfoApplyCount);
        when(pnecClassService.getPersonQrCode("1"))
                .thenReturn(qrcode);
        service.userApply(userApplyRequest);
        Assert.fail();
    }

    @Test
    public void expectCities() {
        service.expectCities(expectCitiesRequest);
    }

    @Test
    public void getSignUpHistory() {
        SignUpHistory signUpHistory = new SignUpHistory();
        signUpHistory.setUserName("yzj");
        when(pclassApplyQueryMapper.getSignUpHistory(signUpHistoryRequest))
                .thenReturn(signUpHistory);
        SignUpHistoryResponse signUpHistoryResponse = service.getSignUpHistory(signUpHistoryRequest);
        Assert.assertEquals(signUpHistory.getUserName(),signUpHistoryResponse.getUserName());
    }
}