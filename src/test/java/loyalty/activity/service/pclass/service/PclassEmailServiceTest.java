package loyalty.activity.service.pclass.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pnec.db.model.PclassEmailInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.mail.javamail.MimeMessageHelper;


public class PclassEmailServiceTest extends BaseTest {

    @InjectMocks
    private PclassEmailService service;
    @Mock
    private MimeMessageHelper mimeMessageHelper;
    private PclassEmailInfo pclassEmailInfo;

    @Before
    public void setUp() throws Exception {
        pclassEmailInfo = new PclassEmailInfo();
        pclassEmailInfo.setClassCode(classesCode);
        pclassEmailInfo.setDescription("testDesc");
    }

    @Test
    public void enhanceMail() throws Exception {
        service.enhanceMail(mimeMessageHelper,pclassEmailInfo,"TEST");
    }
}