package loyalty.activity.service.pclass.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.SubscriptionStatus;
import loyalty.activity.service.pclass.domain.request.GetSubscriptionStatusRequest;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.pclass.domain.response.GetSubscriptionStatusResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.SubscribeStatus;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;

public class ClassesWxRemindServiceTest extends BaseTest {
    @InjectMocks
    private ClassesWxRemindService service;
    @Mock
    private PclassSubscribeMsgInfoMapper pclassSubscribeMsgInfoMapper;
    @Mock
    private PclassSubscribeMsgInfoQueryMapper pclassSubscribeMsgInfoQueryMapper;
    @Mock
    private WxMessageService wxMessageService;
    @Mock
    private WxmppSubscribeMsgInfoMapper wxmppSubscribeMsgInfoMapper;
    private GetSubscriptionStatusRequest getSubscriptionStatusRequest;
    private String type;
    private SubscriptionStatus subscriptionStatus;
    private SaveRemindSubscriptionRequest saveRemindSubscriptionRequest;
    private SaveRemindSubscriptionRequest.Template template;

    @Before
    public void setUp() throws Exception {
        type = "signUp";
        getSubscriptionStatusRequest = new GetSubscriptionStatusRequest();
        getSubscriptionStatusRequest.setCellphone(cellphone);
        getSubscriptionStatusRequest.setPlcassCode(classesCode);

        subscriptionStatus = new SubscriptionStatus();
        subscriptionStatus.setIsAccept(true);
        subscriptionStatus.setIsSend(true);
        subscriptionStatus.setType(type);

        saveRemindSubscriptionRequest = new SaveRemindSubscriptionRequest();
        saveRemindSubscriptionRequest.setCellphone(cellphone);
        template = new SaveRemindSubscriptionRequest.Template();
        template.setType(type);
        template.setSubscribeStatus(SubscribeStatus.accept.name());
        saveRemindSubscriptionRequest.setTemplate(Collections.singletonList(template));
    }

    @Test
    public void getSubscriptionStatus() {
        Mockito.when(pclassSubscribeMsgInfoQueryMapper.getSubscriptionStatus(cellphone,classesCode))
                .thenReturn(Collections.singletonList(subscriptionStatus));
        GetSubscriptionStatusResponse subscriptionStatus = service.getSubscriptionStatus(getSubscriptionStatusRequest);
        Assert.assertTrue(subscriptionStatus.getSubscribeTypeStatusList().get(0).getIsAccept());
    }

    @Test
    public void saveRemindSubscription() {
        service.saveRemindSubscription(saveRemindSubscriptionRequest);
    }

    @Test
    public void doInsertSubscribeMsgInfo() {
        service.doInsertSubscribeMsgInfo(saveRemindSubscriptionRequest,template);
    }
}