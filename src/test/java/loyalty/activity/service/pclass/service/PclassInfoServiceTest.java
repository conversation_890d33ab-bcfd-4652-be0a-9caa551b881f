package loyalty.activity.service.pclass.service;

import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper;
import loyalty.activity.service.pclass.db.model.ActivityDetail;
import loyalty.activity.service.pclass.domain.request.GetPclassListByCityRequest;
import loyalty.activity.service.pclass.domain.request.LocationPclassInSameCityRequest;
import loyalty.activity.service.pclass.domain.response.PclassInfoResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;


public class PclassInfoServiceTest extends BaseTest {

    @InjectMocks
    private PclassInfoService service;
    @Mock
    private ActivityQueryMapper activityQueryMapper;
    private ActivityDetail activityDetail;
    private GetPclassListByCityRequest listRequest;
    private LocationPclassInSameCityRequest sameCityRequest;
    private String classesCode;

    @Before
    public void setUp() throws Exception {
        classesCode = "PCEOC6-20230168549";
        String city = cityName;

        activityDetail = new ActivityDetail();
        activityDetail.setClassesCode(classesCode);
        activityDetail.setId(1L);
        activityDetail.setIsDeleted(false);
        activityDetail.setIsEnabled(true);
        activityDetail.setCity(city);

        listRequest = new GetPclassListByCityRequest();
        listRequest.setCity(city);
        listRequest.setListType("SIGN_IN");
        listRequest.setCellphone("13294193948");
        listRequest.setPnecId("123");
        listRequest.setPage(1);
        listRequest.setLimit(10);

        sameCityRequest = new LocationPclassInSameCityRequest();
        sameCityRequest.setPclassId(classesCode);
        sameCityRequest.setCity(city);
    }

    @Test
    public void getPclassById() {
        when(activityQueryMapper.getActivityDetailByPcalssId(classesCode))
                .thenReturn(activityDetail);
//        PclassInfoResponse response = service.getPclassById(classesCode);
//        Assert.assertEquals(response.getClassesCode(),classesCode);
    }

    @Test
    public void getPclassListByCity() {
        when(activityQueryMapper.getListByCity(anyString(),anyString(),
                anyList(),anyString(),Mockito.any(Date.class)))
                .thenReturn(Collections.singletonList(activityDetail));
        PageResponse<PclassInfoResponse> pclassListByCity = service.getPclassListByCity(listRequest);
        Assert.assertEquals(pclassListByCity.getResults().get(0).getClassesCode(),classesCode);

    }

    @Test
    public void locationPclassInSameCity() {
        when(activityQueryMapper.getActivityDetailByPcalssId(classesCode))
                .thenReturn(activityDetail);
        service.locationPclassInSameCity(sameCityRequest);
    }
}