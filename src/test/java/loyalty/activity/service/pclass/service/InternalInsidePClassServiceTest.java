package loyalty.activity.service.pclass.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.AssociationSurveyInfo;
import loyalty.activity.service.pclass.db.model.PclassQrcodeInfo;
import loyalty.activity.service.pclass.domain.request.AssociationSurveySubmitRequest;
import loyalty.activity.service.pclass.domain.request.UserSignInConditionRequest;
import loyalty.activity.service.pclass.domain.request.UserSurveySubConditionRequest;
import loyalty.activity.service.pclass.domain.response.AssociationSurveyQueryResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.rest.client.YoushengClient;
import loyalty.activity.service.sharelib.rest.response.ResearchResponse;
import loyalty.activity.service.sharelib.rest.response.YouShengSignInQrcodeResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Date;


public class InternalInsidePClassServiceTest extends BaseTest {
    @InjectMocks
    private InternalInsidePClassService service;
    @Mock
    private AssociationSurveyMapper associationSurveyMapper;
    @Mock
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Mock
    private YoushengClient youShengClient;
    @Mock
    private ActivitySignInMapper activitySignInMapper;
    @Mock
    private HttpServletRequest httpServletRequest;
    @Mock
    private PNECClassService pnecClassService;

    private AssociationSurveyInfo associationSurveyInfo;
    private AssociationSurveySubmitRequest associationSurveySubmitRequest;
    private AssociationSurveySubmitRequest.UserSurveyItem userSurveyItem;
    private ResearchResponse<YouShengSignInQrcodeResponse> qrcodeResponse;
    private YouShengSignInQrcodeResponse youShengSignInQrcodeResponse;
    private PclassQrcodeInfo pclassQrcodeInfo;
    private UserSignInConditionRequest userSignInConditionRequest;
    private String associationName;


    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(service,"ncpUrl",qrcode);
        httpServletRequest = new MockHttpServletRequest();

        associationName = "中国优生科学协会";
        associationSurveyInfo = new AssociationSurveyInfo();
        associationSurveyInfo.setAssociationId(1);
        associationSurveyInfo.setAssociationName(associationName);
        associationSurveyInfo.setTopicId(1);
        associationSurveyInfo.setItemSequence("A");
        associationSurveyInfo.setIsCorrectOption(true);
        associationSurveyInfo.setScore(100);
        associationSurveyInfo.setSurveyId(1);

        associationSurveySubmitRequest = new AssociationSurveySubmitRequest();
        associationSurveySubmitRequest.setPclassCode(classesCode);
        associationSurveySubmitRequest.setCellphone(cellphone);
        userSurveyItem = new AssociationSurveySubmitRequest.UserSurveyItem();
        userSurveyItem.setTopicId(1);
        userSurveyItem.setUserAnswer("A");
        associationSurveySubmitRequest.setUserSurveyItemList(Collections.singletonList(userSurveyItem));
        associationSurveySubmitRequest.setSurveyId(1);

        pclassQrcodeInfo = new PclassQrcodeInfo();
        pclassQrcodeInfo.setStartTime(new Date());
        pclassQrcodeInfo.setEndTime(new Date());

        qrcodeResponse = new ResearchResponse<>();

        youShengSignInQrcodeResponse = new YouShengSignInQrcodeResponse();
        youShengSignInQrcodeResponse.setQrcodeUrl(qrcode);
        youShengSignInQrcodeResponse.setExpireTime(String.valueOf(System.currentTimeMillis()));

        userSignInConditionRequest = new UserSignInConditionRequest();
        userSignInConditionRequest.setCellphone(cellphone);
        userSignInConditionRequest.setClassCode(classesCode);

    }

    @Test
    public void getSurveyInfo() {
        Mockito.when(associationSurveyMapper.getSurveyInfoByAssociationId(1))
                .thenReturn(Collections.singletonList(associationSurveyInfo));
        AssociationSurveyQueryResponse surveyInfo = service.getSurveyInfo(1);
        Assert.assertEquals(surveyInfo.getAssociationList().get(0).getAssociationName(),associationName);
    }

    @Test
    public void submitSurvey() {
        Mockito.when(associationSurveyMapper.getSubmitCountByCellphoneAndClassCode(classesCode,cellphone))
                .thenReturn(0);
        Mockito.when(associationSurveyMapper.getSurveyInfoBySurveyId(1))
                .thenReturn(Collections.singletonList(associationSurveyInfo));
        service.submitSurvey(associationSurveySubmitRequest,httpServletRequest);
    }

    @Test
    public void youShengSignInQrcodeGenerate() throws Exception {
        Mockito.when(pclassInfoQueryMapper.getPclassQrcodeInfoByClassCode(classesCode))
                .thenReturn(pclassQrcodeInfo);
        ResearchResponse<YouShengSignInQrcodeResponse> researchResponse = new ResearchResponse<>();
        researchResponse.setData(youShengSignInQrcodeResponse);
        Mockito.when(youShengClient.queryActivity(classesCode,"92"))
                .thenReturn(qrcodeResponse).thenReturn(researchResponse);
        String qrcode = service.youShengSignInQrcodeGenerate(classesCode);
        Assert.assertEquals(qrcode,researchResponse.getData().getQrcodeUrl());
    }

    @Test
    public void getUserSignInCondition() {
        Mockito.when(activitySignInMapper.getSignInCondition(classesCode,cellphone))
                .thenReturn(true);
        Assert.assertTrue(service.getUserSignInCondition(userSignInConditionRequest));
    }

    @Test
    public void ownerContactQrcodeGenerate() {
        Mockito.when(pclassInfoQueryMapper.getOwnerContactQrcode(classesCode)).thenReturn(cellphone);
        Mockito.when(pnecClassService.getPersonQrCode(cellphone)).thenReturn(qrcode);
        Assert.assertEquals(qrcode,service.ownerContactQrcodeGenerate(classesCode));
    }

    @Test
    public void getUserSurveySubCondition() {
        Mockito.when(associationSurveyMapper.getSubmitCountByCellphoneAndClassCode(classesCode,cellphone))
                .thenReturn(1);
        UserSurveySubConditionRequest request = new UserSurveySubConditionRequest();
        request.setClassCode(classesCode);
        request.setCellphone(cellphone);
        Assert.assertTrue(service.getUserSurveySubCondition(request));
    }
}