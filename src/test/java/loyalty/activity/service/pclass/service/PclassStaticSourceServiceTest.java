package loyalty.activity.service.pclass.service;

import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.domain.request.WxTemplateIdRequest;
import loyalty.activity.service.sharelib.domain.dto.Address;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.WxmppTemplateInfo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PclassStaticSourceServiceTest extends BaseTest {

    @InjectMocks
    private PclassStaticSourceService service;
    @Mock
    private RedisTemplate<String, Object> template;
    @Mock
    private WxmppTemplateInfoMapper wxmppTemplateInfoMapper;
    @Mock
    private ValueOperations<String, Object> valueOperations;
    @Mock
    private List<Address.Province> provinceList;
    private Address.Province province;
    private Address.City city;
    private WxTemplateIdRequest wxTemplateIdRequest;
    private WxmppTemplateInfo wxmppTemplateInfo;



    @Before
    public void setUp() throws Exception {
        province = new Address.Province();
        province.setProvinceName(provinceName);
        city = new Address.City();
        city.setCityName(cityName);
        city.setAreas(Collections.singletonList("海珠区"));
        province.setCities(Collections.singletonList(city));

        wxTemplateIdRequest = new WxTemplateIdRequest();
        wxTemplateIdRequest.setType(Collections.singletonList("SIGN_IN"));

        wxmppTemplateInfo = new WxmppTemplateInfo();
        wxmppTemplateInfo.setTemplateName("testTemp");
        when(template.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get("loyalty_city_all")).thenReturn(provinceList);
        when(provinceList.toString()).thenReturn("{\n" +
                "  \"provinces\": [\n" +
                "    {\n" +
                "      \"provinceName\": \"广东省\",\n" +
                "      \"cities\": [\n" +
                "        {\n" +
                "          \"cityName\": \"广州市\",\n" +
                "          \"areas\": [\n" +
                "            \"海珠区\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "}]}");
    }

    @Test
    public void getPclassCityList() {
        List<String> cityList = service.getPclassCityList(provinceName);
        Assert.assertEquals(cityList.get(0),cityName);
    }

    @Test
    public void getPclassProvinceList() {
        List<String> pclassProvinceList = service.getPclassProvinceList();
        Assert.assertEquals(pclassProvinceList.get(0),provinceName);
    }

    @Test
    public void getWxTemplateId() {
        when(wxmppTemplateInfoMapper.selectList(any()))
                .thenReturn(Collections.singletonList(wxmppTemplateInfo));
        List<WxmppTemplateInfo> wxTemplateId = service.getWxTemplateId(wxTemplateIdRequest);
        Assert.assertEquals(wxmppTemplateInfo.getTemplateName(),wxTemplateId.get(0).getTemplateName());
    }
}