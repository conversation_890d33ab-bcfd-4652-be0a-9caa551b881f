package loyalty.activity.service.pclass.service;

import com.cstools.data.internal.client.WxClient;
import com.cstools.data.internal.domain.wx.response.AbstractWechatResponse;
import common.wechat.serviceapi.service.RestWxMaTokenService;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.SubscribeStatus;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivitySignInRemindDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class WxMessageServiceTest extends BaseTest {

    @InjectMocks
    private WxMessageService service;
    @Mock
    private ActivitySignRemindMapper activitySignRemindMapper;
    @Mock
    private LogPclassSubscribeSendMsgMapper logPclassSubscribeSendMsgMapper;
    @Mock
    private PclassSubscribeMsgInfoMapper pclassSubscribeMsgInfoMapper;
    @Mock
    private WxClient wxClient;
    @Mock
    private RestWxMaTokenService restWxMaTokenService;
    private SaveRemindSubscriptionRequest saveRemindSubscriptionRequest;
    private SaveRemindSubscriptionRequest.Template template;
    private String type;
    private ActivitySignInRemindDto remindInfo;
    private AbstractWechatResponse wechatResponse;

    @Before
    public void setUp() throws Exception {
        type = "signUp";
        saveRemindSubscriptionRequest = new SaveRemindSubscriptionRequest();
        saveRemindSubscriptionRequest.setCellphone(cellphone);
        template = new SaveRemindSubscriptionRequest.Template();
        template.setType(type);
        template.setSubscribeStatus(SubscribeStatus.accept.name());
        saveRemindSubscriptionRequest.setTemplate(Collections.singletonList(template));
        saveRemindSubscriptionRequest.setPclassCode(classesCode);
        saveRemindSubscriptionRequest.setAppid("appid");

        remindInfo = new ActivitySignInRemindDto();
        remindInfo.setClassesCode(classesCode);
        remindInfo.setOpenid(openid);
        remindInfo.setTemplateData("{\n" +
                "  \"touser\": \"${openid}\",\n" +
                "  \"template_id\": \"12345\",\n" +
                "  \"page\": \"packages/campaign/pages/mother-class/index/index?classes_code=${classesCode}&pnec_id=${pnecid}\",\n" +
                "  \"miniprogram_state\": \"${miniProgramState}\",\n" +
                "  \"lang\": \"zh_CN\",\n" +
                "  \"data\": {\n" +
                "    \"thing1\": {\n" +
                "      \"value\": \"${activityName}\"\n" +
                "    },\n" +
                "    \"time3\": {\n" +
                "      \"value\": \"${activityStartTime}\"\n" +
                "    },\n" +
                "    \"thing4\": {\n" +
                "      \"value\": \"请提前到达课程地点，注意防疫安全，谢谢\"\n" +
                "    }\n" +
                "  }\n" +
                "}");
        remindInfo.setCourseName("courseName");
        remindInfo.setStartTime(new Date());
        remindInfo.setPlace("place");
        remindInfo.setAddress("address");

        ReflectionTestUtils.setField(service,"miniProgramState","miniProgramState");

        wechatResponse = new AbstractWechatResponse();
        wechatResponse.setErrcode(200);
        wechatResponse.setErrmsg("成功");
    }

    //@Test
    public void doSendSignUpMessage() {
        when(activitySignRemindMapper.getRemindInfoByCellphoneAndCode(cellphone,classesCode))
                .thenReturn(remindInfo);
        when(restWxMaTokenService.getAccessToken(anyString())).thenReturn("token");
        when(wxClient.sendSubscribeMessageWithToken(any(),any(),any(),anyString()))
                .thenReturn(wechatResponse);
        service.doSendSignUpMessage(saveRemindSubscriptionRequest);
    }
}