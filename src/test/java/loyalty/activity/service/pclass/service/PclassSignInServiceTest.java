package loyalty.activity.service.pclass.service;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMRetrieveMemberRequest;
import com.cstools.data.internal.domain.crm.response.CRMMemberBaseInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMRetrieveMemberResponse;
import loyalty.activity.service.BaseTest;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.domain.request.PnecQrCodeRequest;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.policy.pclass.signIn.CsSignIn;
import loyalty.activity.service.policy.pclass.signIn.OutsidePclassSignIn;
import loyalty.activity.service.policy.pclass.signIn.SignInPolicy;
import loyalty.activity.service.policy.pclass.signIn.UserSignIn;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;


import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;


public class PclassSignInServiceTest extends BaseTest {

    @InjectMocks
    private PclassSignInService service;
    @Spy
    private SignInPolicy signInPolicy;
    @Mock
    private ActivityUserSigninMapper activityUserSigninMapper;
    @Mock
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Mock
    private PNECClassService pnecClassService;
    @Mock
    private ActivitySignInMapper activitySignInMapper;
    @Mock
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Mock
    private PclassSignInService pclassSignInService;
    @Mock
    private CRMClient crmClient;
    private PnecQrCodeRequest pnecQrCodeRequest;
    private ActivityUserSignin activityUserSignin;
    private UserSignInRequest userSignInRequest;
    private CRMRetrieveMemberResponse memberResponse;
    private PclassInfoApplyCount pclassInfoApplyCount;
    private final Map<String, UserSignIn> SIGN_IN_STRATEGY_MAP = new HashMap<>();
    private CsSignIn csSignIn = new CsSignIn();
    private OutsidePclassSignIn outsidePclassSignIn = new OutsidePclassSignIn();

    @Before
    public void setUp() throws Exception {
        SIGN_IN_STRATEGY_MAP.put(ActivityChannel.CS.name(),csSignIn);
        SIGN_IN_STRATEGY_MAP.put(ActivityChannel.OUTSIDE_PCLASS.name(),outsidePclassSignIn);
        ReflectionTestUtils.setField(signInPolicy,"SIGN_IN_STRATEGY_MAP",SIGN_IN_STRATEGY_MAP);
        ReflectionTestUtils.setField(csSignIn,"activitySignInMapper",activitySignInMapper);
        ReflectionTestUtils.setField(csSignIn,"pclassApplyQueryMapper",pclassApplyQueryMapper);
        ReflectionTestUtils.setField(csSignIn,"pclassInfoQueryMapper",pclassInfoQueryMapper);
        ReflectionTestUtils.setField(csSignIn,"pclassSignInService",pclassSignInService);

        ReflectionTestUtils.setField(outsidePclassSignIn,"activitySignInMapper",activitySignInMapper);
        ReflectionTestUtils.setField(outsidePclassSignIn,"pclassApplyQueryMapper",pclassApplyQueryMapper);
        ReflectionTestUtils.setField(outsidePclassSignIn,"pclassInfoQueryMapper",pclassInfoQueryMapper);
        ReflectionTestUtils.setField(outsidePclassSignIn,"pclassSignInService",pclassSignInService);
        ReflectionTestUtils.setField(outsidePclassSignIn,"activityUserSigninMapper",activityUserSigninMapper);
        ReflectionTestUtils.setField(outsidePclassSignIn,"pnecClassService",pnecClassService);
        ReflectionTestUtils.setField(outsidePclassSignIn,"crmClient",crmClient);

        pnecQrCodeRequest = new PnecQrCodeRequest();
        pnecQrCodeRequest.setCellphone(cellphone);
        pnecQrCodeRequest.setActivityId(1);

        activityUserSignin = new ActivityUserSignin();
        activityUserSignin.setActivityId(1L);
        activityUserSignin.setInviterId("1");
        activityUserSignin.setId(1L);

        userSignInRequest = new UserSignInRequest();
        userSignInRequest.setActivityId(1);
        userSignInRequest.setCellphone(cellphone);

        memberResponse = new CRMRetrieveMemberResponse();
        CRMMemberBaseInfoResponse memberInfo = new CRMMemberBaseInfoResponse();
        memberResponse.setMemberInfo(memberInfo);
        memberResponse.setErrCode("200");
        memberResponse.setStatus("200");

        pclassInfoApplyCount = new PclassInfoApplyCount();
        pclassInfoApplyCount.setId(1L);
        pclassInfoApplyCount.setIsDeleted(false);
        pclassInfoApplyCount.setIsEnabled(true);
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE,-1);
        pclassInfoApplyCount.setStartTime(instance.getTime());
        instance.add(Calendar.DATE,2);
        pclassInfoApplyCount.setEndTime(instance.getTime());

        when(pclassInfoQueryMapper.getPclassInfoSignInCountById(1)).thenReturn(pclassInfoApplyCount);
        CRMRetrieveMemberRequest crmRetrieveMemberRequest = new CRMRetrieveMemberRequest();
        crmRetrieveMemberRequest.setCellPhone(userSignInRequest.getCellphone());
        when(crmClient.getMemberBaseInfo(crmRetrieveMemberRequest))
                .thenReturn(memberResponse);
    }

    @Test
    public void getPnecQrCode() {
        when(activityUserSigninMapper.getByActivityIdCellphone("1",cellphone))
                .thenReturn(activityUserSignin);
        when(pnecClassService.getPersonQrCode("1"))
                .thenReturn(qrcode);
        UserSignInResponse pnecQrCode = service.getPnecQrCode(pnecQrCodeRequest);
        Assert.assertEquals(pnecQrCode.getQrCode(),qrcode);
    }

    //@Test
    public void doInsertPclassSignInInfo() {
        service.doInsertPclassSignInInfo(userSignInRequest,new PclassInfoApplyCount(),"PNEC");
    }

    //@Test
    public void userCsSignIn() {
        userSignInRequest.setSignInType(ActivityChannel.CS.name());
        service.userSignIn(userSignInRequest);
    }

    //@Test
    public void userOutsideSignIn() {
        when(activitySignInMapper.getStartClassByCellphoneV2(anyString(),any(Date.class),anyString())).thenReturn(null);
        when(activityUserSigninMapper.getByActivityIdCellphone("1",cellphone))
                .thenReturn(activityUserSignin);
        userSignInRequest.setSignInType(ActivityChannel.OUTSIDE_PCLASS.name());
        userSignInRequest.setLongitude(new BigDecimal("113.2568074"));
        userSignInRequest.setLatitude(new BigDecimal("23.0944571"));
        pclassInfoApplyCount.setIsOnline(true);
        pclassInfoApplyCount.setLongitude(new BigDecimal("113.2568074"));
        pclassInfoApplyCount.setLatitude(new BigDecimal("23.0944571"));
        service.userSignIn(userSignInRequest);
    }
}