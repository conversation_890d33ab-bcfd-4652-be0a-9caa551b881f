package loyalty.activity.service.pnec.db.cmpmapper;

//import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

//@DS("cmp")
//@Mapper
public interface CmpDbMapper {

    @Select("<script>" +
            "SELECT\n" +
            "t1.store_code \n" +
            "FROM\n" +
            "store_info t1\n" +
            "INNER JOIN store_nc_info t2 ON t1.id = t2.store_info_id\n" +
            "INNER JOIN nc_info t3 ON t3.id = t2.nc_info_id \n" +
            "WHERE t3.nc_id = #{ncId} \n" +
            "</script>")
    List<String> getNcIdListByStoreCodeList(@Param("ncId")String ncId);
}
