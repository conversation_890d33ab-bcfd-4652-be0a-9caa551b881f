package loyalty.activity.service.pnec.db.model;

import lombok.Data;
import lombok.ToString;
import loyalty.activity.service.common.utils.DateUtils;
import loyalty.activity.service.common.utils.poi.Excel;

import java.util.Date;

@Data
@ToString
public class PClassSignInData {

    private String activityId;
    @Excel(name = "活动编码",sort = 1)
    private String classesCode;
    private String pnec;
    private Date startTime;
    private Date endTime;
    @Excel(name = "活动状态",readConverterExp = "UN_PLAYED=未开始,PLAYED=已结束,PLAYING=进行中",sort =3)
    private String status;
    @Excel(name = "会员手机号",sort = 4)
    private String cellphone;
    private String userName;
    @Excel(name = "签到时间",sort =5)
    private String triggerTime;
    @Excel(name = "活动时间",sort = 2)
    private String classTime;

    private String getClassTime(){
        return DateUtils.dateTimeNoSecond(startTime)+"-"+DateUtils.dateTimeNoSecond(endTime);
    }
}
