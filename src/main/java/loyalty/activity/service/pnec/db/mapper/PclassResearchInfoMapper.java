package loyalty.activity.service.pnec.db.mapper;

import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface PclassResearchInfoMapper {

    @SelectProvider(type = PclassResearchInfoQueryProvider.class, method = "select")
    List<PclassResearchInfo> list(SearchCriteria criteria);

    @Select("SELECT\n" +
            "t1.id,\n" +
            "t1.hcp_id hcpId,\n" +
            "t1.hcp_name hcpName,\n" +
            "t1.research_id researchId,\n" +
            "t1.research_institution_name researchInstitutionName,\n" +
            "t1.qrcode_url qrcodeUrl,\n" +
            "t1.pnr_id pnrId,\n" +
            "t1.pnr_name pnrName,\n" +
            "t1.pnr_mobile pnrMobile,\n" +
            "DATE_FORMAT(now(),'%Y-%m') `month`, \n" +
            "t2.count \n" +
            "FROM\n" +
            "pclass_research t1\n" +
            "LEFT JOIN pclass_research_statistics t2 ON t1.research_id = t2.research_id \n " +
            "WHERE\n" +
            "t1.research_id = #{researchId} \n" +
            "AND t2.month = DATE_FORMAT(now(),'%Y%c') \n" +
            "AND t2.statistics_type = 'SURVEY_RECORD'")
    PclassResearchInfo getDetailByResearchId(@Param("researchId")String researchId);

    @Select("SELECT\n" +
            "COUNT( DISTINCT mobile ) \n" +
            "FROM\n" +
            "ce_register_log \n" +
            "WHERE\n" +
            "CODE = #{researchId} \n" +
            "AND `type` = 3 \n" +
            "AND `is_suc` = 1 \n" +
            "AND DATE_FORMAT( create_time, '%Y%c' ) = DATE_FORMAT( now(), '%Y%c' )")
    Integer getRecruitmentCount(@Param("researchId") String researchId);

    @Select("SELECT " +
            " t1.id, " +
            " t1.hcp_id hcpId, " +
            " t1.hcp_name hcpName, " +
            " t1.research_id researchId, " +
            " t1.research_institution_name researchInstitutionName, " +
            " t1.qrcode_url qrcodeUrl, " +
            " t1.pnr_id pnrId, " +
            " t1.pnr_name pnrName, " +
            " t1.pnr_mobile pnrMobile " +
            " FROM " +
            " pclass_research t1 " +
            " WHERE " +
            " t1.research_id = #{researchId} ")
    PclassResearchInfo getDataByResearchId(@Param("researchId") String researchId);
}
