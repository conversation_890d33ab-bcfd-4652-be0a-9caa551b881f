package loyalty.activity.service.pnec.db.mapper;

import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.pnec.db.model.PClassSignInData;
import loyalty.activity.service.pnec.db.model.PNECClassInfo;
import loyalty.activity.service.pnec.db.model.PNECClassInfoData;
import loyalty.activity.service.pnec.db.model.PNECClassInfoDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface PNECClassMapper {
    @SelectProvider(type = PNECClassInfoQuerySQLProvider.class, method = "select")
    List<PNECClassInfo> list(SearchCriteria criteria, String userId, String storeCode);

    @Select("SELECT t1.classes_code classCode," +
            "t1.is_enabled status," +
            "t1.pclass_type pclassType," +
            "t1.pclass_type_2 pclassType2," +
            "t2.id activityId," +
            "t2.start_time startTime," +
            "t2.end_time endTime," +
            "t2.topic," +
            "t3.city," +
            "t3.place, " +
            "t2.channel, " +
            "COUNT(*) signUpNum," +
            "SUM(attendance) expectedAttendance," +
            "(SELECT COUNT(*) FROM activity_user_signin t6 WHERE t6.activity_id = t2.id AND t1.is_enabled = 1 ) signInNum " +
            "FROM pclass_info t1 " +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id " +
            "LEFT JOIN activity_address t3 ON t2.id = t3.activity_id " +
            "LEFT JOIN activity_user_signup t4 ON t4.activity_id = t2.id " +
            "LEFT JOIN pclass_user_signup_form t5 ON t4.id = t5.signup_id  and t5.status = '已报名' " +
            "WHERE t1.id = #{pClassInfoId}")
    PNECClassInfoDetail getPClassInfoDetailByPClassId(@Param("pClassInfoId") String pClassInfoId);

    @Select("SELECT\n" +
            "( SELECT COUNT(*) FROM activity_user_signin t4 INNER JOIN activity t5 ON t4.activity_id = t5.id WHERE t4.activity_id = #{activityId} AND t5.is_enabled = 1) signInNum,\n" +
            "t3.signUpNum,\n" +
            "t3.expectedAttendance \n" +
            "FROM\n" +
            "(\n" +
            "SELECT\n" +
            "COUNT(*) signUpNum,\n" +
            "SUM( attendance ) expectedAttendance \n" +
            "FROM\n" +
            "activity_user_signup t1\n" +
            "INNER JOIN pclass_user_signup_form t2 ON t1.id = t2.signup_id \n" +
            "AND t2.STATUS = '已报名' \n" +
            "WHERE\n" +
            "t1.activity_id = #{activityId} \n" +
            ") t3")
    PNECClassInfoData getPClassInfoDataByActivityId(@Param("activityId") String activityId);

    @Select({"<script>",
            "SELECT\n" +
            "( SELECT COUNT(*) FROM activity_user_signin t4 INNER JOIN activity t5 ON t4.activity_id = t5.id INNER JOIN pclass_info p on p.activity_id=t4.activity_id WHERE p.classes_code = #{classesCode} " +
             "<if test = \"inviterId != null and inviterId !='' \"> " ,
             "and t4.inviter_id=#{inviterId} ",
            "</if>\n" ,
            "and t4.is_md_person!=1 AND t5.is_enabled = 1) signInNum,\n" +
            "t3.signUpNum,\n" +
            "t3.expectedAttendance \n" +
            "FROM\n" +
            "(\n" +
            "SELECT\n" +
            "COUNT(*) signUpNum,\n" +
            "SUM( attendance ) expectedAttendance \n" +
            "FROM\n" +
            "activity_user_signup t1\n" +
            " INNER JOIN pclass_info p on p.activity_id=t1.activity_id"+
            " INNER JOIN pclass_user_signup_form t2 ON t1.id = t2.signup_id \n" +
            "AND t2.STATUS = '已报名' \n" +
            "WHERE\n" +
            " p.classes_code = #{classesCode} \n" +
            ") t3",
            "</script>"})
    PNECClassInfoData getPClassInfoDataByClassCode(@Param("classesCode") String classesCode,@Param("inviterId") String inviterId);

    @Select("select a.start_time as startTime,a.end_time as endTime,p.activity_id as activityId,p.classes_code as classesCode,p.director_exmployee_num as pnec,s.user_name as userName,INSERT(s.cellphone,4,4,'****') as cellphone,s.trigger_time as triggerTime,\n" +
            "case when a.start_time>now() then 'UN_PLAYED' when a.end_time<now() then 'PLAYED' else 'PLAYING' end as status,CONCAT(DATE_FORMAT(a.start_time,'%m月%d日 %H:%i'),' - ',DATE_FORMAT(a.end_time,'%m月%d日 %H:%i')) as classTime \n" +
            "from pclass_info p left join activity_user_signin s on p.activity_id=s.activity_id left join activity a on a.id=p.activity_id where p.activity_id=#{activityId}")
    List<PClassSignInData> getSignInDataByPclassId(@Param("activityId") String activityId);

    @Select("SELECT t1.classes_code classCode," +
            "t1.pclass_type pclassType," +
            "t1.pclass_type_2 pclassType2," +
            "t2.id activityId," +
            "t2.start_time startTime," +
            "t2.end_time endTime," +
            "t2.topic," +
            "t3.city," +
            "t3.place " +
            "FROM pclass_info t1 " +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id " +
            "LEFT JOIN activity_address t3 ON t2.id = t3.activity_id " +
            "WHERE t1.activity_id = #{activityId}")
    PNECClassInfoDetail getPClassInfoDetailByActivityId(@Param("activityId") String activityId);

}
