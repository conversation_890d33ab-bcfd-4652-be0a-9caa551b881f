package loyalty.activity.service.pnec.db.mapper;

import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityStatus;
import loyalty.activity.service.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;

@Component
public class PNECClassInfoQuerySQLProvider {
    public String select(SearchCriteria criteria, String userId , String storeCode) {
        return new SQL() {{
            SELECT("t1.id", "t1.classes_code classesCode", "t2.channel", "t2.topic", "t1.pclass_type_2 pclassType2", "DATE_FORMAT(t2.start_time,'%Y-%m-%d') classTime","DATE_FORMAT(t2.end_time,'%Y-%m-%d') endTime","t1.is_enabled status");
            FROM("pclass_info t1");
            INNER_JOIN("activity t2 ON t1.activity_id = t2.id");
            WHERE("t2.channel != 'CS' ");
            WHERE("(("+userId+" = (SELECT pnec_id FROM sync_pna_info WHERE REPLACE(pna_id,'P','') = REPLACE ( t1.director_exmployee_num, 'P', '' ))) OR ("+userId+" IN (SELECT REPLACE(pna_id,'P','') FROM sync_pna_info WHERE pnec_id = t1.director_exmployee_num)) OR "+ userId + " = REPLACE(t1.pna_employee_number,'P','') OR "+ userId +" = REPLACE(t1.director_exmployee_num,'P','') "
                    + (storeCode==null ? ")" : "OR FIND_IN_SET('"+storeCode+"',REPLACE ( t1.nc_code, ';', ',' )) )"));
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("id") && cri.getValue() != null) {
                        WHERE("t1.id = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("channel") && cri.getValue() != null) {
                        if (cri.getValue().equals("ALL")) {
                            continue;
                        }
                        WHERE("t2.channel = '" + cri.getValue() + "'");
                    } else if (cri.getKey().equals("status") && cri.getValue() != null) {
                        if (cri.getValue().equals("ALL")) {
                            continue;
                        }
                        Calendar calendar = Calendar.getInstance();
                        String nowDate = DateUtils.format(calendar.getTime(),DateUtils.yyyyMMdd);
                        if (cri.getValue().equals(ActivityStatus.UN_PLAYED.name())) {
//                            calendar.add(Calendar.DATE, 1);
//                            String startDate = sdf.format(calendar.getTime());
//                            calendar.add(Calendar.DATE, 6);
//                            String date = sdf.format(calendar.getTime());
//                            WHERE("t1.is_enabled = 1 AND" +
//                                    " DATE_FORMAT(t2.start_time,'%Y-%m-%d') between '" + startDate + "' and '" + date + "'");
                            WHERE("t1.is_enabled = 1 AND" +
                                    " DATE_FORMAT(t2.start_time,'%Y-%m-%d') > '" + nowDate + "'");
                        } else if (cri.getValue().equals(ActivityStatus.PLAYED.name())) {
//                            calendar.add(Calendar.DATE, -1);
//                            String endDate = sdf.format(calendar.getTime());
//                            calendar.add(Calendar.DATE, -6);
//                            String date = sdf.format(calendar.getTime());
//                            WHERE("t1.is_enabled = 1 AND " +
//                                    " DATE_FORMAT(t2.end_time,'%Y-%m-%d') between '" + date + "' and '" + endDate + "'");
                            WHERE("t1.is_enabled = 1 AND " +
                                    " DATE_FORMAT(t2.end_time,'%Y-%m-%d') < '" + nowDate + "'");
                        } else if (cri.getValue().equals(ActivityStatus.PLAYING.name())) {
                            WHERE("t1.is_enabled = 1 AND" +
                                    " '"+nowDate + "' between DATE_FORMAT(t2.start_time,'%Y-%m-%d') AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') ");
                        } else if (cri.getValue().equals(ActivityStatus.CANCELED.name())){
//                            calendar.add(Calendar.DATE, -7);
//                            String startDate = sdf.format(calendar.getTime());
//                            calendar.add(Calendar.DATE, 14);
//                            String date = sdf.format(calendar.getTime());
//                            WHERE("t1.is_enabled = 0 AND " +
//                                    " DATE_FORMAT(t2.end_time,'%Y-%m-%d') between '" + startDate + "' and '" + date + "'");
                            WHERE("t1.is_enabled = 0 ");
                        }
                    } else if (cri.getKey().equals("classTime")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            if (cri.getMinValue().equals(cri.getMaxValue())){
                                WHERE("'"+cri.getMinValue() + "' between DATE_FORMAT(t2.start_time,'%Y-%m-%d') AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') ");
                            }else {
                                WHERE("DATE_FORMAT(t2.end_time,'%Y-%m-%d') between '" + cri.getMinValue() + "' and '" + cri.getMaxValue() + "'");
                            }
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("DATE_FORMAT(t2.start_time,'%Y-%m-%d') >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("DATE_FORMAT(t2.end_time,'%Y-%m-%d') <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null)
                            WHERE("'"+cri.getValue() + "' between DATE_FORMAT(t2.start_time,'%Y-%m-%d') AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') ");
                    }
                }
            }
            ;
            ORDER_BY("classTime DESC");
        }}.toString();
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        String startDate = sdf.format(calendar.getTime());
        System.out.println(startDate);
        calendar.add(Calendar.DATE, 6);
        String date = sdf.format(calendar.getTime());
        System.out.println(date);
    }
}
