package loyalty.activity.service.pnec.db.mapper;

import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class PclassResearchInfoQueryProvider {

    public String select(SearchCriteria criteria) {
        return new SQL() {{
            SELECT("t1.id", "t1.hcp_id hcpId", "t1.hcp_name hcpName", "t1.research_id researchId", "t1.research_institution_name researchInstitutionName", "DATE_FORMAT(t1.create_time,'%Y年%m月%d日') month");
            FROM("pclass_research t1");
            WHERE("t1.is_enabled = 1");
            if (criteria.getCriterias() != null && criteria.getCriterias().size() > 0) {
                for (SearchCriteria.Criteria cri : criteria.getCriterias()) {
                    if (StringUtils.isBlank(cri.getKey())) {
                        continue;
                    }
                    if (cri.getKey().equals("classTime")) {
                        if (cri.getMinValue() != null && cri.getMaxValue() != null) {
                            WHERE("DATE_FORMAT(t1.create_time,'%Y-%m-%d') between '" + cri.getMinValue() + "' and '" + cri.getMaxValue() + "'");
                        } else if (cri.getMinValue() != null && cri.getMaxValue() == null) {
                            WHERE("DATE_FORMAT(t1.create_time,'%Y-%m-%d') >= '" + cri.getMinValue() + "'");
                        } else if (cri.getMinValue() == null && cri.getMaxValue() != null) {
                            WHERE("DATE_FORMAT(t1.create_time,'%Y-%m-%d') <= '" + cri.getMaxValue() + "'");
                        } else if (cri.getValue() != null)
                            WHERE("DATE_FORMAT(t1.create_time,'%Y-%m-%d') = '" + cri.getValue() + "'");
                    }
                }
            }
            ORDER_BY("t1.create_time DESC");
        }}.toString();
    }
}
