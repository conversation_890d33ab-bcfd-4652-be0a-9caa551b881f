package loyalty.activity.service.pnec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pnec.db.model.PNECClassInfoData;

@Data
public class PNECClassInfoDataDto {
    @ApiModelProperty("报名家庭数")
    private Integer signUpNum;
    @ApiModelProperty("预计到场人数")
    private Integer expectedAttendance;
    @ApiModelProperty("签到人数")
    private Integer signInNum;
    @ApiModelProperty("到会领取礼包人数")
    private Integer getGiftNum;

    public PNECClassInfoDataDto(PNECClassInfoData pnecClassInfoData) {
        this.signUpNum = pnecClassInfoData.getSignUpNum();
        this.expectedAttendance = pnecClassInfoData.getExpectedAttendance() == null ? 0 : pnecClassInfoData.getExpectedAttendance();
        this.signInNum = pnecClassInfoData.getSignInNum();
        this.getGiftNum = pnecClassInfoData.getGetGiftNum() == null ? 0 : pnecClassInfoData.getGetGiftNum();
    }
}
