package loyalty.activity.service.pnec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;

@Data
public class PclassResearchInfoDto {
    @ApiModelProperty("hcpId")
    private String hcpId;
    @ApiModelProperty("HCP名称")
    private String hcpName;
    @ApiModelProperty("调研ID")
    private String researchId;
    @ApiModelProperty("调研机构名称")
    private String researchInstitutionName;
    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("二维码")
    private String qrcodeUrl;
    @ApiModelProperty("pnrId")
    private String pnrId;
    @ApiModelProperty("pnrName")
    private String pnrName;
    @ApiModelProperty("pnrMobile")
    private String pnrMobile;
    @ApiModelProperty("参与调研数")
    private Integer count;
    @ApiModelProperty("招募数")
    private Integer recruitmentCount;

    public PclassResearchInfoDto(PclassResearchInfo pclassResearchInfo) {
        if (pclassResearchInfo!=null){
            this.hcpId = pclassResearchInfo.getHcpId();
            this.hcpName = pclassResearchInfo.getHcpName();
            this.researchId = pclassResearchInfo.getResearchId();
            this.researchInstitutionName = pclassResearchInfo.getResearchInstitutionName();
            this.month = pclassResearchInfo.getMonth();
            this.qrcodeUrl = pclassResearchInfo.getQrcodeUrl();
            this.pnrId = pclassResearchInfo.getPnrId();
            this.pnrName = pclassResearchInfo.getPnrName();
            this.pnrMobile = pclassResearchInfo.getPnrMobile();
            this.count = pclassResearchInfo.getCount();
        }
    }
}
