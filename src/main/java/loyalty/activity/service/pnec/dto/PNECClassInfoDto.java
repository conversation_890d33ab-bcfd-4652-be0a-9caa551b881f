package loyalty.activity.service.pnec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;
import loyalty.activity.service.pnec.db.model.PNECClassInfo;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityStatus;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class PNECClassInfoDto {
    @ApiModelProperty("课程id")
    private String id;
    @ApiModelProperty("活动编码")
    private String classesCode;
    @ApiModelProperty("活动主题")
    private String topic;
    @ApiModelProperty("活动形式")
    private String pclassType2;
    @ApiModelProperty("活动渠道")
    private String channel;
    @ApiModelProperty("活动时间")
    private String classTime;
    @ApiModelProperty("活动状态")
    private String status;

    public PNECClassInfoDto(PNECClassInfo pnecClassInfo) throws ParseException {
        this.id = pnecClassInfo.getId();
        this.classesCode = pnecClassInfo.getClassesCode();
        this.topic = pnecClassInfo.getTopic();
        this.pclassType2 = pnecClassInfo.getPclassType2();
        this.channel = pnecClassInfo.getChannel();
        this.classTime = pnecClassInfo.getClassTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long startMillisecondTime = sdf.parse(pnecClassInfo.getClassTime()).getTime();
        long endMillisecondTime = sdf.parse(pnecClassInfo.getEndTime()).getTime();
        long todayMillisecondTime = sdf.parse(sdf.format(new Date())).getTime();
        if (!pnecClassInfo.getStatus()){
            this.status = ActivityStatus.CANCELED.name();
        }else if (todayMillisecondTime < startMillisecondTime){
            this.status = ActivityStatus.UN_PLAYED.name();
        }else if (todayMillisecondTime > endMillisecondTime){
            this.status = ActivityStatus.PLAYED.name();
        }else {
            this.status = ActivityStatus.PLAYING.name();
        }
    }

    public PNECClassInfoDto(PclassResearchInfo researchInfo) {
        this.id = String.valueOf(researchInfo.getId());
        this.classesCode = researchInfo.getResearchId();
        this.classTime = researchInfo.getMonth();
        this.topic = researchInfo.getResearchInstitutionName();
        this.channel = "RESEARCH";
    }
}
