package loyalty.activity.service.pnec.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pnec.db.model.PNECClassInfoDetail;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityStatus;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class PNECClassInfoDetailDto {
    @ApiModelProperty("活动ID")
    private String activityId;
    @ApiModelProperty("活动状态")
    private String status;
    @ApiModelProperty("活动时间")
    private String classTime;
    @ApiModelProperty("活动编码")
    private String classCode;
    @ApiModelProperty("活动城市")
    private String city;
    @ApiModelProperty("授课主题")
    private String topic;
    @ApiModelProperty("活动形式")
    private String pclassType2;
    @ApiModelProperty("授课地点")
    private String place;

    @ApiModelProperty("院内二维码")
    private String qrcodeUrl;

    @ApiModelProperty("开课标识")
    private boolean startFlag;


    public PNECClassInfoDetailDto(PNECClassInfoDetail pnecClassInfoDetail) throws ParseException {
        if (pnecClassInfoDetail != null && pnecClassInfoDetail.getClassCode() != null) {
            this.activityId = pnecClassInfoDetail.getActivityId();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            long startMillisecondTime = sdf.parse(sdf.format(pnecClassInfoDetail.getStartTime())).getTime();
            long endMillisecondTime = sdf.parse(sdf.format(pnecClassInfoDetail.getEndTime())).getTime();
            long todayMillisecondTime = sdf.parse(sdf.format(new Date())).getTime();
            if (!pnecClassInfoDetail.getStatus()){
                this.status = ActivityStatus.CANCELED.getDesc();
            }else if (todayMillisecondTime < startMillisecondTime){
                this.status = ActivityStatus.UN_PLAYED.getDesc();
            }else if (todayMillisecondTime > endMillisecondTime){
                this.status = ActivityStatus.PLAYED.getDesc();
            }else {
                this.status = ActivityStatus.PLAYING.getDesc();
            }
            SimpleDateFormat sdf1 = new SimpleDateFormat("MM月dd日 HH:mm");
            String startTime = sdf1.format(pnecClassInfoDetail.getStartTime());
            String endTime = sdf1.format(pnecClassInfoDetail.getEndTime());
            this.classTime = startTime + " - " + endTime;
            this.classCode = pnecClassInfoDetail.getClassCode();
            this.city = pnecClassInfoDetail.getCity();
            this.topic = pnecClassInfoDetail.getTopic();
            this.pclassType2 = pnecClassInfoDetail.getPclassType2();
            this.place = pnecClassInfoDetail.getPlace();
        }
    }

}
