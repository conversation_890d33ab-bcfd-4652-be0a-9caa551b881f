package loyalty.activity.service.pnec.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.pnec.db.model.PClassSignInData;
import loyalty.activity.service.pnec.dto.PNECClassInfoDataDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDetailDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDto;
import loyalty.activity.service.pnec.dto.PclassResearchInfoDto;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.AuthUtils;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;

import static loyalty.activity.service.sharelib.domain.dto.InternalResponse.*;

@Slf4j
@RestController
@RequestMapping("/ncp/PNECClass")
@Api
public class PNECClassController {

    @Autowired
    private PNECClassService pnecClassService;

    @ApiLog
    @ApiOperation(value = "课程列表查询", notes = "查询字段可包含【channel(活动渠道:【INSIDE_PCLASS：院内妈妈班,OUTSIDE_PCLASS：院外妈妈班,CS(CS活动)】),classTime(活动日期),status(活动状态:【ALL:全部; UN_PLAYED:未开始; PLAYING: 执行中; PLAYED:已结束】)】")
    @RequestMapping(value = "/list", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<PNECClassInfoDto>> list(@Valid @RequestBody SearchCriteria criteria) throws ParseException {
        InternalResponse internalResponse = null;
        PageResponse<PNECClassInfoDto> pageResponse = pnecClassService.list(criteria);
        internalResponse = success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "课程详情查询", notes = "课程详情查询")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PNECClassInfoDetailDto> getDetail(@RequestParam("pclassId") String pclassId) throws ParseException {
        InternalResponse internalResponse = null;
        PNECClassInfoDetailDto detail = pnecClassService.getDetail(pclassId);
        internalResponse = success().withBody(detail);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "课程数据查询", notes = "课程数据查询")
    @RequestMapping(value = "/getData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PNECClassInfoDataDto> getData(@RequestParam("activityId") String activityId) {
        InternalResponse internalResponse = null;
        PNECClassInfoDataDto data = pnecClassService.getData(activityId);
        internalResponse = success().withBody(data);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "个人码生成", notes = "个人码生成")
    @RequestMapping(value = "/getPersonQrCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> getPersonQrCode() {
        InternalResponse internalResponse = null;
        String userId = AuthUtils.getUserId();
        String data = pnecClassService.getPersonQrCode(userId);
        internalResponse = success().withBody(data);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "调研码详情", notes = "调研码详情")
    @RequestMapping(value = "/researchDetail", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PclassResearchInfoDto> researchDetail(@RequestParam String researchId){
        InternalResponse internalResponse = null;
        internalResponse = success().withBody(pnecClassService.researchDetail(researchId));
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "课程签到明细查询", notes = "分页参数limit,第几页page")
    @RequestMapping(value = "/signIn/list/{activityId}", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PageResponse<PClassSignInData>> listSignInData(@PathVariable("activityId")String activityId,@Valid @RequestBody SearchCriteria criteria) throws ParseException {
        log.info("Start to exec path=【/signIn/list/{}】",activityId);
        InternalResponse internalResponse = null;
        PageResponse<PClassSignInData> pageResponse = pnecClassService.listSignInData(activityId,criteria);
        internalResponse = success().withBody(pageResponse);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "课程签到明细Excel导出")
    @RequestMapping(value = "/signIn/export/{activityId}", produces = "application/json")
    @ResponseBody
    public void exportSignInData(@PathVariable("activityId")String activityId, HttpServletResponse response) throws UnsupportedEncodingException {
        log.info("Start to exec path=【/signIn/export/{}】",activityId);
        pnecClassService.exportSignInData(activityId,response);
    }

    @ApiLog
    @ApiOperation(value = "课程签到明细邮箱发送")
    @RequestMapping(value = "/signIn/sendEmail/{activityId}",method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<Void> sendEmailForSignInData(@PathVariable("activityId")String activityId, @RequestParam("toUserEmail") String toUserEmail) throws Exception {
        log.info("Start to exec path=【/signIn/sendEmail/{}】",activityId);
        pnecClassService.sendEmailForSignInData(activityId,toUserEmail);
        return InternalResponse.success();
    }

}
