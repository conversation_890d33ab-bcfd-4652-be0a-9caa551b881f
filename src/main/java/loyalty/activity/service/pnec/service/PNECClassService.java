package loyalty.activity.service.pnec.service;

import cn.hutool.extra.qrcode.QrCodeUtil;
import com.cstools.data.internal.client.NCPV2Client;
import com.cstools.data.internal.domain.ncp.NCPBasicRequest;
import com.cstools.data.internal.domain.ncp.request.NCPRequest;
import com.cstools.data.internal.domain.ncp.request.NcQrcodeQueryRequest;
import com.cstools.data.internal.domain.ncp.response.NCPQrcodeQueryResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.common.service.BaseCallServiceImpl;
import loyalty.activity.service.common.utils.DateUtils;
import loyalty.activity.service.common.utils.poi.ExcelUtil;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.error.CRMSysException;
import loyalty.activity.service.error.EmailPatternException;
import loyalty.activity.service.error.NcClientException;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.error.handler.NCPResponseHandler;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.pclass.service.InternalInsidePClassService;
import loyalty.activity.service.pclass.service.PclassEmailService;
import loyalty.activity.service.pnec.db.mapper.PNECClassMapper;
import loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper;
import loyalty.activity.service.pnec.db.model.*;
import loyalty.activity.service.pnec.dto.PNECClassInfoDataDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDetailDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDto;
import loyalty.activity.service.pnec.dto.PclassResearchInfoDto;
import loyalty.activity.service.sharelib.rest.client.MallInternalClient;
import loyalty.activity.service.sharelib.rest.response.MallInternalResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.utils.AuthUtils;
import loyalty.activity.service.utils.QrCodeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static loyalty.activity.service.common.constants.AppConstants.DEFAULT_PAGE_SIZE;
import static loyalty.activity.service.common.constants.AppConstants.NCP_QRCODETYPE;
import static loyalty.activity.service.config.AppConstant.DAY_14;
import static loyalty.activity.service.config.AppConstant.HOUR_48_MILLSECCONDS;

@Service
@Slf4j
@RefreshScope
public class PNECClassService extends BaseCallServiceImpl {

    private static final String RESEARCH = "RESEARCH";

    @Autowired
    private PNECClassMapper pnecClassMapper;
    @Value("${ncp.client.url}")
    private String ncpUrl;
    @Value("${ncp.access.key.id}")
    private String accessKeyId;
    @Value("${pclass.signIn.generate.path}")
    private String excelGeneratePath;
    @Autowired
    private PclassResearchInfoMapper pclassResearchMapper;
    @Autowired
    private InternalInsidePClassService internalInsidePClassService;
    @Autowired
    private PclassEmailService pclassEmailService;

    @Autowired
    private MallInternalClient mallInternalClient;
    @Resource
    private NCPV2Client ncpV2Client;
    @Resource
    private ClassInfoMapper classInfoMapper;

    @Resource
    private JdbcTemplate ncpJdbcTemplate;

    @Value("${thirdparty.ncp.datasource.nc-sign-query}")
    private String ncpSignQuerySql;

    /**
     * 拼接 院内-pne链路生成二维码
     */
    public static String QR_CODE_URL = "https://mjn-loyalty.icyanstone.com/mother-class/?scene=MP_3121&classes_code=";


    //todo: to howard team: 过长、hardcode，拆分查询前和拆分后的处理逻辑，不要耦合
    public PageResponse<PNECClassInfoDto> list(SearchCriteria criteria) throws ParseException {
        String userId = AuthUtils.getUserId();
        log.info("userId = " + userId);
        List<SearchCriteria.Criteria> criterias = criteria.getCriterias();
        boolean isResearch = false;
//        boolean haveStatus = false;
//        boolean haveClassTime = false;
        if (CollectionUtils.isEmpty(criterias)) {
            SearchCriteria.Criteria criteria2 = new SearchCriteria.Criteria();
            criteria2.setKey("status");
            criteria2.setValue("ALL");
        } else {
            for (SearchCriteria.Criteria criteria1 : criterias) {
                if ("channel".equals(criteria1.getKey()) && RESEARCH.equals(criteria1.getValue())) {
                    isResearch = true;
                    break;
                }
//            if ("status".equals(criteria1.getKey())) {
//                haveStatus = true;
//            }
//            if ("classTime".equals(criteria1.getKey())) {
//                haveClassTime = true;
//            }
            }
        }
        //此分页为entity分页，如果有其他查询自行在前面增加不要导致以下分页失效
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<PNECClassInfoDto> responseList = new ArrayList<>();
        if (isResearch) {
            List<PclassResearchInfo> searchResult = pclassResearchMapper.list(criteria);
            for (PclassResearchInfo researchInfo : searchResult) {
                PNECClassInfoDto pnecClassInfoDto = new PNECClassInfoDto(researchInfo);
                responseList.add(pnecClassInfoDto);
            }
        } else {
//            if (!haveStatus && !haveClassTime){
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//                Calendar beginCalendar = Calendar.getInstance();
//                beginCalendar.add(Calendar.DATE, -7);
//                String beginDate = sdf.format(beginCalendar.getTime());
//                Calendar endCalendar = Calendar.getInstance();
//                endCalendar.add(Calendar.DATE, 7);
//                String endDate = sdf.format(endCalendar.getTime());
//                SearchCriteria.Criteria criteria2 = new SearchCriteria.Criteria();
//                criteria2.setKey("classTime");
//                criteria2.setMinValue(beginDate);
//                criteria2.setMaxValue(endDate);
//                criteria.getCriterias().add(criteria2);
//            }
            MallInternalResponse<List<String>> response = mallInternalClient.getStoreCodeListByNcId(userId);
            List<String> belongStoreCodeList = new ArrayList<>();
            if (response != null) {
                belongStoreCodeList = response.getBody();
            }
            log.info("belongStoreCodeList = " + belongStoreCodeList);
            userId = userId.replace("P", "");
            if (CollectionUtils.isNotEmpty(belongStoreCodeList)) {
                for (String belongStoreCode : belongStoreCodeList) {
                    List<PNECClassInfo> searchResult = pnecClassMapper.list(criteria, userId, belongStoreCode);
                    for (PNECClassInfo pnecClassInfo : searchResult) {
                        PNECClassInfoDto pnecClassInfoDto = new PNECClassInfoDto(pnecClassInfo);
                        responseList.add(pnecClassInfoDto);
                    }
                }
            } else {
                List<PNECClassInfo> searchResult = pnecClassMapper.list(criteria, userId, null);
                for (PNECClassInfo pnecClassInfo : searchResult) {
                    PNECClassInfoDto pnecClassInfoDto = new PNECClassInfoDto(pnecClassInfo);
                    responseList.add(pnecClassInfoDto);
                }
            }
        }
        List<PNECClassInfoDto> collect = responseList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(PNECClassInfoDto::getId))), ArrayList::new)).stream().sorted(Comparator.comparing(PNECClassInfoDto::getClassTime).reversed()).collect(Collectors.toList());
        PageResponse<PNECClassInfoDto> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(collect);
        return pageResponse;
    }

    public PNECClassInfoDetailDto getDetail(String pclassId) throws ParseException {
        PNECClassInfoDetail pClassInfoDetailByPClassId = pnecClassMapper.getPClassInfoDetailByPClassId(pclassId);
        PNECClassInfoDetailDto pnecClassInfoDetailDto = new PNECClassInfoDetailDto(pClassInfoDetailByPClassId);
        if (ActivityChannel.INSIDE_PCLASS.name().equalsIgnoreCase(pClassInfoDetailByPClassId.getChannel())) {
            if (pClassInfoDetailByPClassId.getStartTime() != null && pClassInfoDetailByPClassId.getStartTime().getTime() - System.currentTimeMillis() > DAY_14) {
                return pnecClassInfoDetailDto;
            }
            String qrcodeUrl = null;
            try {
                //判断当前 活动类型是否是 院内-PNE链路 等于的话自己拼接链路
                if (StringUtils.isNotBlank(pClassInfoDetailByPClassId.getClassCode())) {
                    //获取到该课堂的 活动类型
                    String activityType = classInfoMapper.getActivityTypeByCode(pClassInfoDetailByPClassId.getClassCode());
                    log.info("当前课程编号{}，获取到的活动类型为：{}", pClassInfoDetailByPClassId.getClassCode(), activityType);
                    if (AppConstant.INSIDE_PCLASS_PNE_TYPE.equals(activityType)) {
                        //pnec_id 店员id
                        String userId = AuthUtils.getUserId();
                        qrcodeUrl = QR_CODE_URL + pClassInfoDetailByPClassId.getClassCode() + "&pnec_id=" + userId + "&ncCode=" + userId + "&signInType=INSIDE_PCLASS";
                        log.info("活动类型是 内院-PNE链路 返回的qrcodeUrl 为：{}", qrcodeUrl);
                        //将链接转为二维码的 Base64
                        String qrcodeUrlBase = QrCodeUtil.generateAsBase64(qrcodeUrl, QrCodeUtils.initQrConfig(), "png");
                        log.info("qrcodeUrlBase is Base64 code is:{}", qrcodeUrlBase);
                        pnecClassInfoDetailDto.setQrcodeUrl(qrcodeUrlBase);
                        pnecClassInfoDetailDto.setStartFlag(true);
                    } else {
                        qrcodeUrl = internalInsidePClassService.youShengSignInQrcodeGenerate(pClassInfoDetailByPClassId.getClassCode());
                        pnecClassInfoDetailDto.setQrcodeUrl(qrcodeUrl);
                        pnecClassInfoDetailDto.setStartFlag(true);
                    }
                }
            } catch (Exception e) {
                log.error("院内妈妈班qrcode error ", e);
                throw new CRMSysException();
            }
        }
        return pnecClassInfoDetailDto;
    }

    public PNECClassInfoDataDto getData(String activityId) {
        PNECClassInfoData pClassInfoDataByActivityId = pnecClassMapper.getPClassInfoDataByActivityId(activityId);
        return new PNECClassInfoDataDto(pClassInfoDataByActivityId);
    }

    public PNECClassInfoDataDto getPClassData(String classesCode, String employeeNo) {
        PNECClassInfoData pClassInfoDataByActivityId = pnecClassMapper.getPClassInfoDataByClassCode(classesCode,employeeNo);
        return new PNECClassInfoDataDto(pClassInfoDataByActivityId);
    }

    public String getPersonQrCode(String userId) {
        NCPQrcodeQueryResponse.QrCodeInfo qrCodeInfo;
      /*  if (StringUtils.isNotBlank(userId) && userId.startsWith("P")) {
            userId = userId.substring(1);
        }*/
        try {
            if (StringUtils.isBlank(ncpUrl)) {
                throw new NcClientException("ncp url is null");
            }
            NCPBasicRequest<NCPRequest> request = NCPBasicRequest.builder()
                    .accessKeyId(accessKeyId)
                    .ncpUrl(ncpUrl)
                    .requestBody(new NcQrcodeQueryRequest(userId, NCP_QRCODETYPE))
                    .build();
            NCPQrcodeQueryResponse response = ncpV2Client.getContactWayQrCode(request);
            NCPResponseHandler.handleNCPV2Response(response);
            qrCodeInfo = response.getQrCodeInfo();
        } catch (NcClientException e) {
            log.error("ncp client getContactWayQrCode error = ", e);
            return null;
        }
        return qrCodeInfo.getQrCode() == null ? null : qrCodeInfo.getQrCode();
    }


    public PclassResearchInfoDto researchDetail(String researchId) {
        String userId = AuthUtils.getUserId();
        log.info("userId = " + userId);
        MallInternalResponse<Integer> recruitmentCount = null;
        PclassResearchInfoDto pclassResearchInfoDto = new PclassResearchInfoDto(pclassResearchMapper.getDetailByResearchId(researchId));
        try {
            recruitmentCount = mallInternalClient.getRecruitmentCount(userId, researchId);
            pclassResearchInfoDto.setRecruitmentCount(recruitmentCount.getBody());
        } catch (Exception e) {
            log.error("getRecruitmentCount failure with error= ", e);
        }
        return pclassResearchInfoDto;
    }

    public PageResponse<PClassSignInData> listSignInData(String activityId, SearchCriteria criteria) {
        if (criteria.getLimit() == null) {
            criteria.setLimit(DEFAULT_PAGE_SIZE);
        }
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<PClassSignInData> signInDataList = pnecClassMapper.getSignInDataByPclassId(activityId);
        PageResponse<PClassSignInData> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(signInDataList);
        return pageResponse;
    }

    public void exportSignInData(String activityId, HttpServletResponse response) throws UnsupportedEncodingException {
        PNECClassInfoDetail pnecClassInfoDetail = pnecClassMapper.getPClassInfoDetailByActivityId(activityId);
        if (pnecClassInfoDetail == null) {
            throw new PclassNotFoundException();
        }
        List<PClassSignInData> signInDataList = pnecClassMapper.getSignInDataByPclassId(activityId);
        ExcelUtil<PClassSignInData> util = new ExcelUtil<PClassSignInData>(PClassSignInData.class);
        util.exportExcel(pnecClassInfoDetail.getClassCode() + "签到明细" + DateUtils.dateTimeNow() + ".xlsx", response, signInDataList, "签到明细");
    }

    public void sendEmailForSignInData(String activityId, String toUserEamil) throws Exception {
        this.checkEmailAccount(toUserEamil);
        PNECClassInfoDetail pnecClassInfoDetail = pnecClassMapper.getPClassInfoDetailByActivityId(activityId);
        if (pnecClassInfoDetail == null) {
            throw new PclassNotFoundException();
        }
        List<PClassSignInData> signInDataList = pnecClassMapper.getSignInDataByPclassId(activityId);
        ExcelUtil<PClassSignInData> util = new ExcelUtil<PClassSignInData>(PClassSignInData.class);
        File signInDataFile = util.generateExcel(excelGeneratePath, pnecClassInfoDetail.getClassCode() + "签到明细" + DateUtils.dateTimeNow() + ".xlsx", signInDataList, "签到明细");
        PclassEmailInfo pclassEmailInfo = new PclassEmailInfo();
        pclassEmailInfo.setAttachment(signInDataFile);
        pclassEmailInfo.setClassCode(pnecClassInfoDetail.getClassCode());
        pclassEmailInfo.setDescription("课程编码【" + pnecClassInfoDetail.getClassCode() + "】签到明细如邮件附件，如有疑问，请联系（Caitlin）<EMAIL>。");
        pclassEmailService.sendMail(pclassEmailInfo, "课程" + pnecClassInfoDetail.getClassCode() + "签到明细通知", toUserEamil);
    }

    //todo: to howard team,  Rename the method to `validateEmail`，public修饰符？
    public void checkEmailAccount(String emailAccount) {
        final String regx = "^[0-9a-z]+\\w*@([0-9a-z]+\\.)+[0-9a-z]+$";
        final Pattern pattern = Pattern.compile(regx);
        if (!pattern.matcher(emailAccount).matches()) {
            throw new EmailPatternException();
        }
    }

    //todo: to howard team:自查全部main方法，删除，用UnitTest代替！
    public static void main(String[] args) {
        long s = DateUtils.parseDate("2023-02-24 12:48:00").getTime();
        long c = System.currentTimeMillis() - s;
        System.out.println(c);
        System.out.println(HOUR_48_MILLSECCONDS);
        Integer b=2;
        for(int i=0;i<100;i++) {
            System.out.println(i % b);
        }
    }

    public PageResponse listResearch(SearchCriteria criteria) {
        List<PNECClassInfoDto> responseList = new ArrayList<>();
        Page page = PageHelper.startPage(criteria.getPage(), criteria.getLimit(), true);
        List<PclassResearchInfo> searchResult = pclassResearchMapper.list(criteria);
        for (PclassResearchInfo researchInfo : searchResult) {
            PNECClassInfoDto pnecClassInfoDto = new PNECClassInfoDto(researchInfo);
            responseList.add(pnecClassInfoDto);
        }
        PageResponse<PNECClassInfoDto> pageResponse = new PageResponse<>();
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(criteria.getPage());
        pageResponse.setResults(responseList);
        return pageResponse;
    }

    public String getNcpSignFromNcp(String classCode, Integer signInCount) {
        Object[] args = new Object[]{classCode};
        List<String> ncpSignList=ncpJdbcTemplate.queryForList(ncpSignQuerySql,String.class,args);
        log.info("query cs_nc_sign from ncp with result={}",ncpSignList);
        if(ncpSignList==null ||ncpSignList.isEmpty()){
            return null;
        }
        if(ncpSignList.size()==1){
            return ncpSignList.get(0);
        }
        Integer ncNum=ncpSignList.size();
        return ncpSignList.get(signInCount%ncNum);
    }


}
