package loyalty.activity.service.pnec.service;

//import com.baomidou.dynamic.datasource.annotation.DS;
import loyalty.activity.service.pnec.db.crmpgmapper.CrmPgDbMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

//@DS("crmpg")
//@Service
public class CrmPgService {

    @Resource
    private CrmPgDbMapper crmPgDbMapper;

    public Integer getRecruitmentCount(String researchId) {
        return crmPgDbMapper.getRecruitmentCount(researchId);
    }
}
