package loyalty.activity.service.access.service;

import com.cstools.data.internal.client.WXCropClient;
import com.cstools.data.internal.domain.wxcrop.response.InternalQYWechatResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import loyalty.activity.service.access.domain.dto.CorpStaffGetUserinfo;
import loyalty.activity.service.common.utils.TokenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static loyalty.activity.service.common.constants.AppConstants.TOKEN_HEADER;

@Service
public class NcpAccessService {

    @Autowired
    private WXCropClient wxCropClient;
    @Value("${qywx.sass.getUserinfoUrl}")
    private String getUserinfoUrl;


    public CorpStaffGetUserinfo getUserinfo(String code) throws JsonProcessingException {
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        InternalQYWechatResponse<CorpStaffGetUserinfo> response = wxCropClient.sendMessages(CorpStaffGetUserinfo.class, getUserinfoUrl, paramMap, null, HttpMethod.GET, null);
        CorpStaffGetUserinfo body = response.getBody();
        body.setToken(TOKEN_HEADER + TokenUtils.getToken(body.getUserId()));
        return body;
    }

    public static void main(String[] args) {
        System.out.println(TOKEN_HEADER + TokenUtils.getToken("13294193947"));
    }
}
