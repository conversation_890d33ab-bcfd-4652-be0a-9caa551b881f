package loyalty.activity.service.access.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.access.domain.dto.CorpStaffGetUserinfo;
import loyalty.activity.service.access.service.NcpAccessService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("ncp/access")
public class NcpAccessController {

    @Autowired
    private NcpAccessService accessService;

    @ApiLog(storage = true, storageDescription = "企业微信登录")
    @ApiOperation(value = "login", notes = "企业微信登录", response = String.class)
    @RequestMapping(value = "/qy_wechat/login", method = RequestMethod.GET)
    @ResponseBody
    public InternalResponse qYWechatLogin(@RequestParam(value = "code", required = false) String code) throws JsonProcessingException {
        if (null == code) {
            return InternalResponse.fail("07");
        }
        log.info("Call /access/qy_wechat/login with code={}", code);
        CorpStaffGetUserinfo userinfo = accessService.getUserinfo(code);
        log.info("Return object for /access/qy_wechat/login is result={}", new Gson().toJson(userinfo));
        return InternalResponse.success().withBody(userinfo);
    }
}
