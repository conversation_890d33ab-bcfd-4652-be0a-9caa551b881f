package loyalty.activity.service;

import com.cstools.data.model.utils.IPUtils;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableEncryptableProperties
//@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
public class Application {

    public static void main(String[] args) {
        //String intIp = IPUtils.getLocalIp();
        //MDC.put("intranetIp",intIp);
        SpringApplication.run(Application.class, args);

    }
}
