package loyalty.activity.service.policy.pclass.signIn;

import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Component
public class SignInPolicy {
    private final Map<String, UserSignIn> SIGN_IN_STRATEGY_MAP = new HashMap<>();
    @Resource
    private CsSignIn csSignIn;
    @Resource
    private InsidePclassSignIn insidePclassSignIn;
    @Resource
    private OutsidePclassSignIn outsidePclassSignIn;
    @Resource
    private UnrecognizedSignIn unrecognizedSignIn;

    @Resource
    private EpsPclassSignIn epsPclassSignIn;


    public UserSignInResponse doSignIn(UserSignInRequest userSignInRequest) {
        return SIGN_IN_STRATEGY_MAP.getOrDefault(
                userSignInRequest.getSignInType(), null).signIn(userSignInRequest);
    }

    @PostConstruct
    private void init() {
        SIGN_IN_STRATEGY_MAP.put(unrecognizedSignIn.classType(), unrecognizedSignIn);
        SIGN_IN_STRATEGY_MAP.put(csSignIn.classType(), csSignIn);
        SIGN_IN_STRATEGY_MAP.put(insidePclassSignIn.classType(), insidePclassSignIn);
        SIGN_IN_STRATEGY_MAP.put(outsidePclassSignIn.classType(), outsidePclassSignIn);
        SIGN_IN_STRATEGY_MAP.put(epsPclassSignIn.classType(), epsPclassSignIn);
    }
}
