package loyalty.activity.service.policy.pclass.signIn;

import cn.hutool.core.date.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.utils.Converters;
import loyalty.activity.service.error.PclassAlreadySignInException;
import loyalty.activity.service.external.client.JmgActivityClient;
import loyalty.activity.service.external.dto.CheckInfo;
import loyalty.activity.service.external.util.AESUtil;
import loyalty.activity.service.member.db.model.MemberValidateBaseInfo;
import loyalty.activity.service.member.service.MemberService;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.domain.response.IsAddNcResponse;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.config.SignInMessageConfigService;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityUserSigninInviterType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import loyalty.activity.service.validator.pclass.chain.CsSignInValidatorChain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;


//todo:
@Service
@Slf4j
public class CsSignIn implements UserSignIn {
    @Resource
    private ActivitySignInMapper activitySignInMapper;
    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Resource
    private PclassSignInService pclassSignInService;

    @Resource
    private ActivityUserSigninMapper activityUserSigninMapper;

    @Resource
    private MemberService memberService;

    @Resource
    private PNECClassService pnecClassService;

    @Resource
    private  PclassSignRuleMapper pclassSignRuleMapper;
    @Resource
    private  JdbcTemplate ncpMasterJdbcTemplate;

    @Resource
    private JmgActivityClient jmgActivityClient;

    @Resource
    private SignInMessageConfigService signInMessageConfigService;

    @Override
    public String classType() {
        return ActivityChannel.CS.name();
    }

    @Override
    public UserSignInResponse signIn(UserSignInRequest userSignInRequest) {
        log.info("CS课程签到，param={}", userSignInRequest);
        PclassInfoApplyCount pclassInfo =
                pclassInfoQueryMapper.getPclassInfoSignInCountById(userSignInRequest.getActivityId());
        UserSignInResponse userSignInResponse = new UserSignInResponse();
        userSignInResponse.setIsAdd(false);
        userSignInResponse.setIsSigned(false);
        //课程签到有效验证
        try {
            validateSignInInfo(Converters.convertToNewPclassInfoBaseSignIn(pclassInfo, userSignInRequest),
                    userSignInRequest, pclassApplyQueryMapper, activitySignInMapper);
        } catch (PclassAlreadySignInException e) {
            log.info("user=[{}] has already signed", userSignInRequest);
            userSignInResponse.setIsAdd(false);
            userSignInResponse.setIsSigned(true);
        }
        //获取该课程签到数
        Integer signInCount = pclassInfoQueryMapper.getSiginInCount(userSignInRequest.getActivityId() + "");
        String ncCode = pnecClassService.getNcpSignFromNcp(pclassInfo.getPclassCode(), signInCount);
        pclassInfo.setExtAttr("randomNc", ncCode);
        if (!userSignInResponse.getIsSigned()) {
            //写入
            ActivityUserSignin activityUserSignin = pclassSignInService.doInsertPclassSignInInfo(userSignInRequest, pclassInfo,
                    ActivityUserSigninInviterType.TRS.name());
            //推送签到数据到jmg
            CheckInfo checkInfo = new CheckInfo(userSignInRequest.getOpenId(),userSignInRequest.getUnionId(),userSignInRequest.getUserName(), AESUtil.encrypt(userSignInRequest.getCellphone()), DateUtil.formatDateTime(activityUserSignin.getTriggerTime()));
            jmgActivityClient.notifyCheckData(pclassInfo.getPclassCode(), Arrays.asList(checkInfo));
        } else {
            log.info("已签到处理");
            ActivityUserSignin byActivityIdCellphone = activityUserSigninMapper.getByActivityIdCellphone(userSignInRequest.getActivityId().toString(), userSignInRequest.getCellphone());
            log.info("byActivityIdCellphone:{}", byActivityIdCellphone);
            if (byActivityIdCellphone != null) {
                String ext = byActivityIdCellphone.getExt0();
                if (StringUtils.isNotEmpty(ext)) {
                    Gson gson = new Gson();
                    Type type = new TypeToken<Map<String, String>>() {
                    }.getType();
                    Map<String, String> map = gson.fromJson(ext, type);
                    if (map != null) {
                        ncCode = Optional.ofNullable(map.get("randomNc"))
                                .filter(StringUtils::isNotEmpty)
                                .orElse(ncCode);
                    }
                }
            }
            log.info("重置randomNc：{}", ncCode);
        }

        //return pclassApplyQueryMapper.getSignInHistoryQrCode(userSignInRequest.getCellphone(),
        //        userSignInRequest.getActivityId());

        ActivityUserSignin byActivityIdCellphone = activityUserSigninMapper.getByActivityIdCellphone(userSignInRequest.getActivityId().toString(), userSignInRequest.getCellphone());
        userSignInResponse.setId(byActivityIdCellphone.getId().intValue());
        //判断是不是nc
        if (userSignInRequest.getCellphone() != null && userSignInRequest.getUnionId() != null) {
            IsAddNcResponse addNc = memberService.isAddNc(userSignInRequest.getCellphone(), userSignInRequest.getUnionId());
            log.info("IsAddNcResponse is valve:{}", addNc.toString());
            if (addNc.getIsAdd() != null) {
                userSignInResponse.setIsAdd(addNc.getIsAdd());
            }
        }
        if (StringUtils.isNotBlank(ncCode)) {
            log.info("start to get NcCode={}", ncCode);
            String personQrCode = pnecClassService.getPersonQrCode(ncCode);
            userSignInResponse.setQrCode(personQrCode);
        }

        // 设置签到成功提示语
        String successMessage = signInMessageConfigService.getSignInSuccessMessage("CS", pclassInfo.getActivityType());
        userSignInResponse.setMessage(successMessage);

        return userSignInResponse;
    }

    private void validateSignInInfo(PclassInfoBase convertToNewPclassInfoBase, UserSignInRequest request,
                                    PclassApplyQueryMapper pclassApplyQueryMapper,
                                    ActivitySignInMapper activitySignInMapper) {
        MemberValidateBaseInfo memberValidateBaseInfo = new MemberValidateBaseInfo();
        memberValidateBaseInfo.setCellphone(request.getCellphone());
        memberValidateBaseInfo.setOpenId(request.getOpenId());
        memberValidateBaseInfo.setUnionId(request.getUnionId());
        memberValidateBaseInfo.setBabyBirthday(request.getBabyBirthday());
        CsSignInValidatorChain csSignInValidatorChain = new CsSignInValidatorChain(
                pclassApplyQueryMapper, request, activitySignInMapper,pclassSignRuleMapper,ncpMasterJdbcTemplate);
        csSignInValidatorChain.doValidator(convertToNewPclassInfoBase);
    }

}
