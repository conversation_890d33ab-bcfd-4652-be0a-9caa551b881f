package loyalty.activity.service.policy.pclass.signIn;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMRetrieveMemberRequest;
import com.cstools.data.internal.domain.crm.response.CRMMemberBaseInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMRetrieveMemberResponse;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.utils.Converters;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.error.CRMNoSuchMemberException;
import loyalty.activity.service.error.PclassAlreadySignInException;
import loyalty.activity.service.member.db.model.MemberValidateBaseInfo;
import loyalty.activity.service.member.service.MemberService;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.domain.response.IsAddNcResponse;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.config.SignInMessageConfigService;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityUserSigninInviterType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import loyalty.activity.service.validator.pclass.chain.InsidePclassSignInValidatorChain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static loyalty.activity.service.common.constants.AppConstants.NCP_QUERY_PERSONSQL;
import static loyalty.activity.service.error.handler.CRMResponseChecker.checkCRMResponse;


@Service
@Slf4j
public class InsidePclassSignIn implements UserSignIn {

    @Resource
    private ActivitySignInMapper activitySignInMapper;
    @Resource
    private CRMClient crmClient;
    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Resource
    private PclassSignInService pclassSignInService;
    @Resource
    private ActivityUserSigninMapper activityUserSigninMapper;
    @Autowired
    private PNECClassService pnecClassService;
    @Resource
    private MemberService memberService;

    @Resource
    private PclassSignRuleMapper pclassSignRuleMapper;
    @Resource
    private JdbcTemplate ncpMasterJdbcTemplate;
    @Resource
    private SignInMessageConfigService signInMessageConfigService;

    @Override
    public String classType() {
        return ActivityChannel.INSIDE_PCLASS.name();
    }

    @Override
    public UserSignInResponse signIn(UserSignInRequest userSignInRequest) {
        log.info("院内课程签到，param={}", userSignInRequest);
        PclassInfoApplyCount pclassInfo =
                pclassInfoQueryMapper.getPclassInfoSignInCountById(userSignInRequest.getActivityId());
        UserSignInResponse signInHistoryQrCode = null;
        //课程签到有效验证
        try {
            validateSignInInfo(Converters.convertToNewPclassInfoBaseSignIn(pclassInfo, userSignInRequest),
                    userSignInRequest, pclassApplyQueryMapper, activitySignInMapper);
        } catch (PclassAlreadySignInException e) {
            log.info("user=[{}] has already signed", userSignInRequest);
            if (!AppConstant.INSIDE_PCLASS_PNE_TYPE.equals(pclassInfo.getActivityType())) {
                throw e;
            }
            signInHistoryQrCode = pclassApplyQueryMapper.getSignInHistoryQrCode(userSignInRequest.getCellphone(),
                    userSignInRequest.getActivityId());
            signInHistoryQrCode.setIsAdd(false);
            signInHistoryQrCode.setIsSigned(true);
        }
        if (signInHistoryQrCode == null || !signInHistoryQrCode.getIsSigned()) {

            //根据classCode获取到 活动类型 检验是否是 院内-PNE链路 等于 就不用查了
            if (StringUtils.isBlank(pclassInfo.getActivityType()) || !AppConstant.INSIDE_PCLASS_PNE_TYPE.equals(pclassInfo.getActivityType())) {
                //会员信息获取并验证
                CRMRetrieveMemberRequest crmRetrieveMemberRequest = new CRMRetrieveMemberRequest();
                crmRetrieveMemberRequest.setCellPhone(userSignInRequest.getCellphone());
                CRMRetrieveMemberResponse response = crmClient.getMemberBaseInfo(crmRetrieveMemberRequest);
                if ("104".equals(response.getStatus())) {
                    throw new CRMNoSuchMemberException();
                }
                checkCRMResponse(response.getErrCode(), response.getErrMsg());
                CRMMemberBaseInfoResponse memberInfo = response.getMemberInfo();
                userSignInRequest.setUserName(memberInfo.getName());
            }


            //写入
            pclassSignInService.doInsertPclassSignInInfo(userSignInRequest, pclassInfo,
                    ActivityUserSigninInviterType.PNEC.name());

            signInHistoryQrCode = pclassApplyQueryMapper.getSignInHistoryQrCode(userSignInRequest.getCellphone(),
                    userSignInRequest.getActivityId());

            signInHistoryQrCode.setIsAdd(false);
            signInHistoryQrCode.setIsSigned(false);
        }
        //判断是不是nc
        if (userSignInRequest.getCellphone() != null && userSignInRequest.getUnionId() != null) {
            IsAddNcResponse addNc = memberService.isAddNc(userSignInRequest.getCellphone(), userSignInRequest.getUnionId());
            log.info("IsAddNcResponse is valve:{}", addNc.toString());
            if (addNc.getIsAdd() != null) {
                signInHistoryQrCode.setIsAdd(addNc.getIsAdd());
            }
        }
        ActivityUserSignin byActivityIdCellphone = activityUserSigninMapper.getByActivityIdCellphone(userSignInRequest.getActivityId().toString(), userSignInRequest.getCellphone());
        /*String inviterId= byActivityIdCellphone.getInviterId();
        log.info("inviterId={}",inviterId);
        if(StringUtils.isBlank(inviterId)||"null".equalsIgnoreCase(inviterId)){
            inviterId= pclassInfoQueryMapper.getOwnerContactQrcode(pclassInfo.getPclassCode());
            log.info("query ownerid={}",inviterId);
        }*/
        //String qrCodeEmployeeNo=pclassSignInService.getQrCodeEmployeeNo(pclassInfo,userSignInRequest);
        //log.info("qrcode employeeNo={}",qrCodeEmployeeNo);
        String personQrCode = pnecClassService.getPersonQrCode(byActivityIdCellphone.getInviterId());
        signInHistoryQrCode.setQrCode(personQrCode);

        // 设置签到成功提示语
        String successMessage = signInMessageConfigService.getSignInSuccessMessage(pclassInfo.getPclassType(), pclassInfo.getActivityType());
        signInHistoryQrCode.setMessage(successMessage);

        return signInHistoryQrCode;
    }

    private void validateSignInInfo(PclassInfoBase convertToNewPclassInfoBase, UserSignInRequest request,
                                    PclassApplyQueryMapper pclassApplyQueryMapper,
                                    ActivitySignInMapper activitySignInMapper) {
        MemberValidateBaseInfo memberValidateBaseInfo = new MemberValidateBaseInfo();
        memberValidateBaseInfo.setCellphone(request.getCellphone());
        memberValidateBaseInfo.setOpenId(request.getOpenId());
        memberValidateBaseInfo.setUnionId(request.getUnionId());
        memberValidateBaseInfo.setBabyBirthday(request.getBabyBirthday());
        InsidePclassSignInValidatorChain insidePclassSignInValidatorChain = new InsidePclassSignInValidatorChain(
                crmClient, memberValidateBaseInfo, pclassApplyQueryMapper, request, activitySignInMapper, pclassSignRuleMapper, ncpMasterJdbcTemplate);
        insidePclassSignInValidatorChain.doValidator(convertToNewPclassInfoBase);
    }
}
