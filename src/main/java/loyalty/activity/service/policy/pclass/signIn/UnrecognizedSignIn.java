package loyalty.activity.service.policy.pclass.signIn;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.SignInTypeNotFoundException;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class UnrecognizedSignIn implements UserSignIn {
    @Override
    public String classType() {
        return null;
    }

    @Override
    public UserSignInResponse signIn(UserSignInRequest userSignInRequest) {
        log.error("签到错误，未知签到类型，param={}", userSignInRequest);
        throw new SignInTypeNotFoundException();
    }
}
