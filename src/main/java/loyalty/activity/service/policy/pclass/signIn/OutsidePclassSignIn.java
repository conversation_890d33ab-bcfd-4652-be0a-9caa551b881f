package loyalty.activity.service.policy.pclass.signIn;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMRetrieveMemberRequest;
import com.cstools.data.internal.domain.crm.response.CRMMemberBaseInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMRetrieveMemberResponse;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.utils.Converters;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.error.CRMNoSuchMemberException;
import loyalty.activity.service.error.PclassAlreadySignInException;
import loyalty.activity.service.member.db.model.MemberValidateBaseInfo;
import loyalty.activity.service.member.service.MemberService;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.domain.response.IsAddNcResponse;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.config.SignInMessageConfigService;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityUserSigninInviterType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import loyalty.activity.service.validator.pclass.chain.PclassSignInValidatorChain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static loyalty.activity.service.error.handler.CRMResponseChecker.checkCRMResponse;


@Service
@Slf4j
public class OutsidePclassSignIn implements UserSignIn {
    @Resource
    private ActivitySignInMapper activitySignInMapper;
    @Resource
    private ActivityUserSigninMapper activityUserSigninMapper;
    @Resource
    private CRMClient crmClient;
    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Resource
    private PclassSignInService pclassSignInService;
    @Autowired
    private PNECClassService pnecClassService;
    @Resource
    private MemberService memberService;

    @Resource
    private PclassSignRuleMapper pclassSignRuleMapper;
    @Resource
    private JdbcTemplate ncpMasterJdbcTemplate;
    @Resource
    private SignInMessageConfigService signInMessageConfigService;

    @Override
    public String classType() {
        return ActivityChannel.OUTSIDE_PCLASS.name();
    }

    @Override
    public UserSignInResponse signIn(UserSignInRequest userSignInRequest) {
        log.info("院外课程签到，param={}", userSignInRequest);
        PclassInfoApplyCount pclassInfo =
                pclassInfoQueryMapper.getPclassInfoSignInCountById(userSignInRequest.getActivityId());
        UserSignInResponse userSignInResponse = new UserSignInResponse();
        userSignInResponse.setIsAdd(false);
        userSignInResponse.setIsSigned(false);
        //课程签到有效验证
        try {
            validateSignInInfo(Converters.convertToNewPclassInfoBaseSignIn(pclassInfo, userSignInRequest),
                    userSignInRequest, pclassApplyQueryMapper, activitySignInMapper);
        }catch (PclassAlreadySignInException e){
            log.info("user=[{}] has already signed", userSignInRequest);
            userSignInResponse.setIsSigned(true);
        }
        if(!userSignInResponse.getIsSigned()) {
            //写入
            pclassSignInService.doInsertPclassSignInInfo(userSignInRequest, pclassInfo,
                    ActivityUserSigninInviterType.PNEC.name());
        }

        ActivityUserSignin byActivityIdCellphone = activityUserSigninMapper.getByActivityIdCellphone(userSignInRequest.getActivityId().toString(), userSignInRequest.getCellphone());
        userSignInResponse.setId(byActivityIdCellphone.getId().intValue());
        //判断是不是nc
        if (userSignInRequest.getCellphone() != null && userSignInRequest.getUnionId() != null) {
            IsAddNcResponse addNc = memberService.isAddNc(userSignInRequest.getCellphone(), userSignInRequest.getUnionId());
            log.info("IsAddNcResponse is valve:{}", addNc.toString());
            if (addNc.getIsAdd() != null) {
                userSignInResponse.setIsAdd(addNc.getIsAdd());
            }
        }
        //String qrCodeEmployeeNo=pclassSignInService.getQrCodeEmployeeNo(pclassInfo,userSignInRequest);
        //log.info("qrcode employeeNo={}",qrCodeEmployeeNo);
        String personQrCode = pnecClassService.getPersonQrCode(byActivityIdCellphone.getInviterId());
        userSignInResponse.setQrCode(personQrCode);

        // 设置签到成功提示语
        String successMessage = signInMessageConfigService.getSignInSuccessMessage(pclassInfo.getPclassType(), pclassInfo.getActivityType());
        userSignInResponse.setMessage(successMessage);

        return userSignInResponse;
    }

    private void validateSignInInfo(PclassInfoBase convertToNewPclassInfoBase, UserSignInRequest request,
                                    PclassApplyQueryMapper pclassApplyQueryMapper,
                                    ActivitySignInMapper activitySignInMapper) {
        MemberValidateBaseInfo memberValidateBaseInfo = new MemberValidateBaseInfo();
        memberValidateBaseInfo.setCellphone(request.getCellphone());
        memberValidateBaseInfo.setOpenId(request.getOpenId());
        memberValidateBaseInfo.setUnionId(request.getUnionId());
        memberValidateBaseInfo.setBabyBirthday(request.getBabyBirthday());
        PclassSignInValidatorChain pclassSignInValidatorChain = new PclassSignInValidatorChain(
                crmClient, memberValidateBaseInfo, pclassApplyQueryMapper, request, activitySignInMapper,pclassSignRuleMapper,ncpMasterJdbcTemplate);
        pclassSignInValidatorChain.doValidator(convertToNewPclassInfoBase);
    }

}
