package loyalty.activity.service.policy.pclass.signIn;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class UserSignInRequest {
    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程id不能为空")
    private Integer activityId;
    @ApiModelProperty(value = "婴儿生日")
//    @NotNull(message = "婴儿生日不能为空")
    private Date babyBirthday;
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$",
            message = "手机号格式错误")
    private String cellphone;
    @ApiModelProperty(value = "定位城市")
//    @NotBlank(message = "定位城市不能为空")
    private String city;
    @ApiModelProperty(value = "当前定位-纬度")
//    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;
    @ApiModelProperty(value = "当前定位-经度")
//    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;
    //    @ApiModelProperty(value = "课程编码")
//    @NotBlank(message = "课程编码不能为空")
//    private String pclassCode;
    @ApiModelProperty(value = "openId")
//    @NotBlank(message = "openId")
    private String openId;
    @ApiModelProperty(value = "出席人数")
    @Min(value = 1, message = "最少出席一人")
    private Integer attendance;
    @ApiModelProperty(value = "负责人id")
    private String pnecId;
    @ApiModelProperty(value = "签到类型（INSIDE_PCLASS(院内妈妈班)，OUTSIDE_PCLASS(院外妈妈班)，CS(CS活动)）")
    @NotBlank(message = "签到类型不能为空")
    private String signInType;
    @ApiModelProperty(value = "unionId")
//    @NotBlank(message = "unionId不能为空")
    private String unionId;
    @ApiModelProperty(value = "宝宝状态")
//    @NotNull(message = "宝宝状态不能为空")
    private Integer babyStatus;
    @ApiModelProperty(value = "留言")
    @Size(max = 30, message = "留言最长不得超过30个字")
    private String remark;
    @ApiModelProperty(value = "用户名")
    @NotNull(message = "用户名")
    private String userName;
    @ApiModelProperty(value = "pnaId")
    private String pnaId;
    @ApiModelProperty(value = "ncCode")
    private String ncCode;

    @ApiModelProperty(value = "tsrId")
    private String tsrId;

    private boolean isYousheng;

    @ApiModelProperty(value = "personId")
    private String personId;

    @ApiModelProperty(value = "personType")
    private String personType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InviterInfo{
        private String pnecId;
        private String pnaId;
        private String ncCode;
        private String tsrId;
        private String personId;
        private String personType;
    }
}
