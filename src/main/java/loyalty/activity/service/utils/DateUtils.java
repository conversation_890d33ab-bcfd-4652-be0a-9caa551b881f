package loyalty.activity.service.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

public class DateUtils {

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static DateTimeFormatter defaultFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);

    public static final String YYYYMMDD = "yyyy-MM-dd";
    public static final DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern(YYYYMMDD);

    public static Date defaultParse(String date) {
        LocalDateTime parse = LocalDateTime.parse(date, defaultFormatter);
        return Date.from(parse.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String defaultFormat(Date date) {
        LocalDateTime dateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return dateTime.format(defaultFormatter);
    }

    public static Date Parse(String date,DateTimeFormatter formatterPattern) {
        LocalDateTime parse = LocalDateTime.parse(date, formatterPattern);
        return Date.from(parse.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String format(Date date,DateTimeFormatter formatterPattern) {
        LocalDateTime dateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return dateTime.format(formatterPattern);
    }


    public static Date getNextDayEnd(Date currentDate) {
        // 解析输入的时间字符串为 LocalDateTime
        LocalDateTime parse= currentDate.toInstant().atZone(TimeZone.getTimeZone("Asia/Shanghai").toZoneId()).toLocalDateTime();

        // 将时间设置为当天的最后一秒（23:59:59）
        LocalDateTime todayEnd = parse.with(LocalTime.MAX);

        // 加一天，得到次日的最后一秒
        LocalDateTime nextDayEnd = todayEnd.plusDays(1);

        // 转换为 Date 返回
        return Date.from(nextDayEnd.atZone(TimeZone.getTimeZone("Asia/Shanghai").toZoneId()).toInstant());
    }

}
