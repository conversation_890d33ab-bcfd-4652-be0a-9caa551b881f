package loyalty.activity.service.validator.pclass.pclassSignUpValidators;


import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassExpiredException;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 10:59
 * @describe
 */
@Slf4j
public class PclassSignUpEffectivenessValidator extends AbstractPclassValidator {
    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if (pclassInfoBase.getEndTime().before(new Date())) {
            log.error("课程已失效（课程已结束），param={}", pclassInfoBase);
            throw new PclassExpiredException();
        }
        doNextValidator(pclassInfoBase);
    }
}
