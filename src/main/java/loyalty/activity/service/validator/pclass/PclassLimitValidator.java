package loyalty.activity.service.validator.pclass;


import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassOutOfLimitException;


//todo: to howard team，删除所有Author信息，代码不要添加这些东西，交接和申请材料都会麻烦
//todo: 自查所有SimpleDateFormat
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 11:03
 * @describe
 */
@Slf4j
public class PclassLimitValidator extends AbstractPclassValidator {
    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if(pclassInfoBase.getLimitCount()==null){
            doNextValidator(pclassInfoBase);
            return;
        }
        if (pclassInfoBase.getApplyCount() >= pclassInfoBase.getLimitCount()) {
            log.error("课程签到或报名超出人数限制，param={}", pclassInfoBase);
            throw new PclassOutOfLimitException();
        }
        doNextValidator(pclassInfoBase);
    }
}
