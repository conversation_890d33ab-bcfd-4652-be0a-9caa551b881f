package loyalty.activity.service.validator.pclass;


import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassIsDeletedException;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.error.PclassUnableException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 09:52
 * @describe
 */
@Slf4j
public class PclassEnableValidator extends AbstractPclassValidator {
    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if (pclassInfoBase == null || pclassInfoBase.getId() == null) {
            log.error("课程不存在，param={}", pclassInfoBase);
            throw new PclassNotFoundException();
        }
     /*   if (!pclassInfoBase.getIsEnabled()) {
            log.error("课程不可用，param={}", pclassInfoBase);
            throw new PclassUnableException();
        }*/
        if (pclassInfoBase.getIsDeleted()) {
            log.error("课程已删除，param={}", pclassInfoBase);
            throw new PclassIsDeletedException();
        }
        doNextValidator(pclassInfoBase);
    }
}
