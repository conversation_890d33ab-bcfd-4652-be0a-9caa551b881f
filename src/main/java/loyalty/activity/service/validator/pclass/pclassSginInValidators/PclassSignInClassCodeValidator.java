package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassSignInDiffCodeException;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 下午 12:14
 * @describe
 */
@Slf4j
public class PclassSignInClassCodeValidator extends AbstractPclassValidator {
    private String submitCode;

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if (!pclassInfoBase.getClassCode().equals(submitCode)) {
            log.error("签到提供课程编码与系统课程编码不一致，param={}", pclassInfoBase);
            throw new PclassSignInDiffCodeException();
        }
        doNextValidator(pclassInfoBase);
    }

    public void setSubmitCode(String submitCode) {
        this.submitCode = submitCode;
    }
}
