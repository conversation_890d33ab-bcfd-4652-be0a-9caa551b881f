package loyalty.activity.service.validator.pclass;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 09:45
 * @describe
 */
@Data
public class PclassInfoBase {
    private Long id;
    private Date startTime;
    private Date endTime;
    private Boolean isEnabled;
    private Integer limitCount;
    private Integer applyCount;
    private String status;
    private Boolean isDeleted;
    private String ClassCode;
    private Boolean hasStartedClass;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private BigDecimal targetLatitude;
    private BigDecimal targetLongitude;
    private String province;
    private String city;
    private Boolean isOnline;
    private String activityType;
    private String pclassType;
}
