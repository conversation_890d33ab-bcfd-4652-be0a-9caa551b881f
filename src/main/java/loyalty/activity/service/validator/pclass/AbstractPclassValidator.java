package loyalty.activity.service.validator.pclass;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 02:30
 * @describe
 */
public abstract class AbstractPclassValidator {
    private AbstractPclassValidator validator;

    public void doNextValidator(PclassInfoBase pclassInfoBase) {
        if (validator != null) {
            validator.doValidator(pclassInfoBase);
        }
    }

    public abstract void doValidator(PclassInfoBase pclassInfoBase);

    public AbstractPclassValidator setNextValidator(AbstractPclassValidator validator) {
        this.validator = validator;
        return validator;
    }
}
