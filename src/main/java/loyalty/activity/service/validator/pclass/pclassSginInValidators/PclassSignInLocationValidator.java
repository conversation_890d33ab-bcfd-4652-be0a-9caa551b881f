package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.constants.LocationConstants;
import loyalty.activity.service.common.utils.LocationUtils;
import loyalty.activity.service.error.PclassSignInOutOfRangeException;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 下午 12:14
 * @describe
 */
@Slf4j
public class PclassSignInLocationValidator extends AbstractPclassValidator {

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if (!LocationUtils.inRange(pclassInfoBase.getTargetLatitude(),
                pclassInfoBase.getTargetLongitude(), pclassInfoBase.getLatitude(),
                pclassInfoBase.getLongitude(), LocationConstants.PCLASS_PUNCH_RADIUS)) {
            log.info("超出签到范围，param={}", pclassInfoBase);
            throw new PclassSignInOutOfRangeException();
        }
        doNextValidator(pclassInfoBase);
    }

}
