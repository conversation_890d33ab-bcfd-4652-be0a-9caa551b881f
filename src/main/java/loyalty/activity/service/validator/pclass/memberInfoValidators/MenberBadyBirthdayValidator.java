package loyalty.activity.service.validator.pclass.memberInfoValidators;


import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMRetrieveMemberRequest;
import com.cstools.data.internal.domain.crm.response.CRMMemberBaseInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMRetrieveMemberResponse;
import loyalty.activity.service.common.utils.Converters;
import loyalty.activity.service.error.CRMNoSuchMemberException;
import loyalty.activity.service.error.CRMSysException;
import loyalty.activity.service.error.MemberInfoIsMissingException;
import loyalty.activity.service.error.PclassSignInMemberBirthdayNotMatchException;
import loyalty.activity.service.member.db.model.MemberValidateBaseInfo;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import static loyalty.activity.service.error.handler.CRMResponseChecker.checkCRMResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 10:59
 * @describe
 */

public class MenberBadyBirthdayValidator extends AbstractPclassValidator {
    private final CRMClient crmClient;
    private final MemberValidateBaseInfo memberValidateBaseInfo;

    public MenberBadyBirthdayValidator(CRMClient crmClient, MemberValidateBaseInfo memberValidateBaseInfo) {
        this.crmClient = crmClient;
        this.memberValidateBaseInfo = memberValidateBaseInfo;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        CRMRetrieveMemberRequest crmRetrieveMemberRequest = new CRMRetrieveMemberRequest();
        crmRetrieveMemberRequest.setCellPhone(memberValidateBaseInfo.getCellphone());
        CRMRetrieveMemberResponse memberInfo = crmClient.getMemberBaseInfo(crmRetrieveMemberRequest);
        if ("104".equals(memberInfo.getStatus())) {
            throw new CRMNoSuchMemberException();
        }
        checkCRMResponse(memberInfo.getErrCode(), memberInfo.getErrMsg());
        CRMMemberBaseInfoResponse memberInfo1 = memberInfo.getMemberInfo();
        try {
            if (memberInfo1.getSecbabyBirth() != null) {
                if (memberValidateBaseInfo.getBabyBirthday()
                        .compareTo(Converters.StringFormatDate(memberInfo1.getSecbabyBirth())) != 0) {
                    throw new PclassSignInMemberBirthdayNotMatchException();
                }
            } else if (memberInfo1.getBabyBirth() != null) {
                if (memberValidateBaseInfo.getBabyBirthday()
                        .compareTo(Converters.StringFormatDate(memberInfo1.getBabyBirth())) != 0) {
                    throw new PclassSignInMemberBirthdayNotMatchException();
                }
            } else {
                throw new MemberInfoIsMissingException();
            }
        } catch (Exception e) {
            throw new CRMSysException();
        }
        doNextValidator(pclassInfoBase);
    }

}
