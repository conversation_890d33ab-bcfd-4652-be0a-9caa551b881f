package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassAlreadySignInException;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 下午 12:14
 * @describe 重复签到
 */
@Slf4j
public class PclassSignInOverOfTimeLimitValidator extends AbstractPclassValidator {
    private final Integer activityId;
    private final String cellphone;
    private final PclassApplyQueryMapper pclassApplyQueryMapper;

    public PclassSignInOverOfTimeLimitValidator(String cellphone,
                                                Integer activityId, PclassApplyQueryMapper pclassApplyQueryMapper) {
        this.cellphone = cellphone;
        this.activityId = activityId;
        this.pclassApplyQueryMapper = pclassApplyQueryMapper;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        UserSignInResponse applyHistoryQrCode = pclassApplyQueryMapper.getSignInHistoryQrCode(
                cellphone, activityId);
        if (applyHistoryQrCode != null && applyHistoryQrCode.getId() != null) {
            log.info("课程已签到，param={}", pclassInfoBase);
            throw new PclassAlreadySignInException();
        }
        doNextValidator(pclassInfoBase);
    }

}
