package loyalty.activity.service.validator.pclass.pclassSginInValidators;


import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.PclassExpiredException;
import loyalty.activity.service.error.PclassNotBeginException;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 10:59
 * @describe
 */
@Slf4j
public class PclassSignInEffectivenessValidator extends AbstractPclassValidator {
    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        Date now = new Date();
     /*   if (pclassInfoBase.getEndTime().before(now)) {
            log.info("课程已失效，param={}", pclassInfoBase);
            throw new PclassExpiredException();
        }
        LocalDate nowLocalDate = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate startTimeLocalDate =
                pclassInfoBase.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        //当前时间的 日期（年-月-日） 比开始时间早
        if (nowLocalDate.isBefore(startTimeLocalDate)) {
            log.info("课程未开始，param={}", pclassInfoBase);
            throw new PclassNotBeginException();
        }*/
        if (now.after(pclassInfoBase.getEndTime())) {
            log.info("课程已失效，param={}", pclassInfoBase);
            throw new PclassExpiredException();
        }

        //当前时间的 日期（年-月-日） 比开始时间早
        if (now.before(pclassInfoBase.getStartTime())) {
            log.info("课程未开始，param={}", pclassInfoBase);
            throw new PclassNotBeginException();
        }
        doNextValidator(pclassInfoBase);
    }
}
