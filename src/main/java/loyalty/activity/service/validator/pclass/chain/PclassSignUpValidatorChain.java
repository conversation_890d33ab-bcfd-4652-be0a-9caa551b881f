package loyalty.activity.service.validator.pclass.chain;

import loyalty.activity.service.external.db.mapper.PClassExtMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.validator.pclass.PclassEnableValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import loyalty.activity.service.validator.pclass.PclassLimitValidator;
import loyalty.activity.service.validator.pclass.pclassSignUpValidators.PClassExtValidator;
import loyalty.activity.service.validator.pclass.pclassSignUpValidators.PclassSignUpEffectivenessValidator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 11:08
 * @describe
 */
@Component
public class PclassSignUpValidatorChain implements PclassValidatorChain {
    private final PClassExtMapper pClassExtMapper;

    public PclassSignUpValidatorChain(PClassExtMapper pClassExtMapper) {
        this.pClassExtMapper = pClassExtMapper;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        PclassEnableValidator pclassEnableValidator = new PclassEnableValidator();
        pclassEnableValidator
                .setNextValidator(new PclassSignUpEffectivenessValidator())
                .setNextValidator(new PclassLimitValidator())
                .setNextValidator(new PClassExtValidator(pClassExtMapper));
        pclassEnableValidator.doValidator(pclassInfoBase);
    }
}
