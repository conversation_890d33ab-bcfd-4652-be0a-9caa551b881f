package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.SignInHasStartingClassException;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 下午 12:14
 * @describe 同一时间不能参加两档课程
 */
@Slf4j
public class PclassSignInInSameTimeValidator extends AbstractPclassValidator {
    private final ActivitySignInMapper activitySignInMapper;
    private final String cellphone;
    private final String city;
    private final String type;

    public PclassSignInInSameTimeValidator(String cellphone,
                                           String city, ActivitySignInMapper activitySignInMapper, String type) {
        this.cellphone = cellphone;
        this.city = city;
        this.activitySignInMapper = activitySignInMapper;
        this.type = type;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        List<Activity> list = activitySignInMapper.getStartClassByCellphone(cellphone, new Date(),
            city, type);
        if (list != null && !list.isEmpty()) {
            for (Activity activity : list) {
                if (!(pclassInfoBase.getStartTime().getTime() >= activity.getEndTime().getTime()
                    || pclassInfoBase.getEndTime().getTime() <= activity.getStartTime().getTime())) {
                    log.info("课程未没结束，param={}", pclassInfoBase);
                    throw new SignInHasStartingClassException();
                }
            }
        }
        doNextValidator(pclassInfoBase);
    }
}
