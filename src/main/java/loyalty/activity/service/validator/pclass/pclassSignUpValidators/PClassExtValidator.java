package loyalty.activity.service.validator.pclass.pclassSignUpValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.*;
import loyalty.activity.service.external.db.mapper.PClassExtMapper;
import loyalty.activity.service.external.entity.PClassExt;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import java.util.Date;

/**
 * PClassExt表数据验证器
 * 校验报名时间、报名人数和状态
 */
@Slf4j
public class PClassExtValidator extends AbstractPclassValidator {
    private final PClassExtMapper pClassExtMapper;

    public PClassExtValidator(PClassExtMapper pClassExtMapper) {
        this.pClassExtMapper = pClassExtMapper;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        // 查询PClassExt数据
        PClassExt pClassExt = pClassExtMapper.selectByClassesCode(pclassInfoBase.getClassCode());
        if (pClassExt == null) {
            log.error("未找到活动扩展信息，classCode={}", pclassInfoBase.getClassCode());
            doNextValidator(pclassInfoBase);
            return;
        }

        // 校验活动状态
        if (pClassExt.getStatus() != null && pClassExt.getStatus() == 0) {
            log.error("活动已取消，param={}", pClassExt);
            throw new PclassUnableException();
        }

        // 校验报名时间
        Date now = new Date();
        if (pClassExt.getRegistrationEndDate() != null && now.after(pClassExt.getRegistrationEndDate())) {
            log.error("报名已结束，param={}", pClassExt);
            throw new PclassSignUpEndException();
        }

        if (pClassExt.getRegistrationBeginDate() != null && now.before(pClassExt.getRegistrationBeginDate())) {
            log.error("报名未开始，param={}", pClassExt);
            throw new PclassSignUpNotBeginException();
        }

        // 校验报名人数
        if (pClassExt.getRestrictAttendance() != null ){
            // 查询当前报名人数
            Integer currentApplyCount = pclassInfoBase.getApplyCount();
             if(currentApplyCount >= pClassExt.getRestrictAttendance()) {
                 log.error("报名人数已达上限，param={}", pClassExt);
                 throw new PclassOutOfLimitException();
             }
        }

        doNextValidator(pclassInfoBase);
    }
} 