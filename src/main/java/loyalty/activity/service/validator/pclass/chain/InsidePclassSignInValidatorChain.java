package loyalty.activity.service.validator.pclass.chain;


import com.cstools.data.internal.client.CRMClient;
import loyalty.activity.service.common.utils.CityUtils;
import loyalty.activity.service.member.db.model.MemberValidateBaseInfo;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.validator.pclass.PclassEnableValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInEffectivenessValidator;
import loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInInSameTimeValidator;
import loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInOverOfTimeLimitValidator;
import loyalty.activity.service.validator.pclass.pclassSginInValidators.PclassSignInRuleValidator;
import org.springframework.jdbc.core.JdbcTemplate;


public class InsidePclassSignInValidatorChain implements PclassValidatorChain {
    private final CRMClient crmClient;
    private final MemberValidateBaseInfo memberValidateBaseInfo;
    private final ActivitySignInMapper activitySignInMapper;
    private final PclassApplyQueryMapper pclassApplyQueryMapper;
    private final UserSignInRequest request;
    private final PclassSignRuleMapper pclassSignRuleMapper;
    private final JdbcTemplate ncpMasterJdbcTemplate;

    public InsidePclassSignInValidatorChain(CRMClient crmClient, MemberValidateBaseInfo memberValidateBaseInfo,
                                            PclassApplyQueryMapper pclassApplyQueryMapper, UserSignInRequest request,
                                            ActivitySignInMapper activitySignInMapper, PclassSignRuleMapper pclassSignRuleMapper, JdbcTemplate ncpMasterJdbcTemplate) {
        this.request = request;
        this.crmClient = crmClient;
        this.memberValidateBaseInfo = memberValidateBaseInfo;
        this.pclassApplyQueryMapper = pclassApplyQueryMapper;
        this.activitySignInMapper = activitySignInMapper;
        this.pclassSignRuleMapper = pclassSignRuleMapper;
        this.ncpMasterJdbcTemplate = ncpMasterJdbcTemplate;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        PclassEnableValidator pclassEnableValidator = new PclassEnableValidator();
        pclassEnableValidator
                .setNextValidator(new PclassSignInOverOfTimeLimitValidator(request.getCellphone(),
                        request.getActivityId(), pclassApplyQueryMapper))
                .setNextValidator(new PclassSignInRuleValidator(pclassSignRuleMapper, activitySignInMapper, request, ncpMasterJdbcTemplate))
               // .setNextValidator(new PclassSignInInSameTimeValidator(request.getCellphone(),
               //         CityUtils.getCity(request.getCity()), activitySignInMapper, ActivityChannel.INSIDE_PCLASS.name()))
                .setNextValidator(new PclassSignInEffectivenessValidator());
        pclassEnableValidator.doValidator(pclassInfoBase);
    }

}
