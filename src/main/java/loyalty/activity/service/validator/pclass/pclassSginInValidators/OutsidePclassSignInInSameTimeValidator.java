package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.SignInHasStartingClassException;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 下午 12:14
 * @describe 同一时间不能参加两档课程
 */
@Slf4j
public class OutsidePclassSignInInSameTimeValidator extends AbstractPclassValidator {
    private final ActivitySignInMapper activitySignInMapper;
    private final String cellphone;
    private final String type;

    public OutsidePclassSignInInSameTimeValidator(String cellphone, ActivitySignInMapper activitySignInMapper,
                                                  String type) {
        this.cellphone = cellphone;
        this.activitySignInMapper = activitySignInMapper;
        this.type = type;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        if (activitySignInMapper.getStartClassByCellphoneV2(cellphone, new Date(), type) != null) {
            log.info("课程未没结束，param={}", pclassInfoBase);
            throw new SignInHasStartingClassException();
        }
        doNextValidator(pclassInfoBase);
    }
}
