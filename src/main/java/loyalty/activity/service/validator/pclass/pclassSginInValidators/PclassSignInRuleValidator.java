package loyalty.activity.service.validator.pclass.pclassSginInValidators;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.SignInHistory;
import loyalty.activity.service.error.SignInValidateException;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassSignRuleMapper;
import loyalty.activity.service.pclass.db.model.PclassSignRule;
import loyalty.activity.service.pclass.domain.enums.SignRuleDimension;
import loyalty.activity.service.pclass.domain.enums.SignRuleType;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.validator.pclass.AbstractPclassValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static loyalty.activity.service.config.AppConstant.INSIDE_PCLASS_PNE_TYPE;

@Slf4j
@RefreshScope
public class PclassSignInRuleValidator extends AbstractPclassValidator {

    private final PclassSignRuleMapper pclassSignRuleMapper;

    private final ActivitySignInMapper activitySignInMapper;

    private final UserSignInRequest request;

    private final JdbcTemplate ncpMasterJdbcTemplate;

    private static String ncpQueryPersonSql="select count(1) from md_person where mobile=? limit 1";


    public PclassSignInRuleValidator(PclassSignRuleMapper pclassSignRuleMapper, ActivitySignInMapper activitySignInMapper, UserSignInRequest request, JdbcTemplate ncpMasterJdbcTemplate) {
        this.pclassSignRuleMapper = pclassSignRuleMapper;
        this.activitySignInMapper = activitySignInMapper;
        this.request = request;
        this.ncpMasterJdbcTemplate = ncpMasterJdbcTemplate;
    }

    @Override
    public void doValidator(PclassInfoBase pclassInfoBase) {
        boolean isNcPerson = ncpMasterJdbcTemplate.queryForObject(ncpQueryPersonSql, Integer.class, request.getCellphone()) > 0;
        if (isNcPerson) return;
        List<PclassSignRule> signRuleList=pclassSignRuleMapper.selectAll();
        List<SignInHistory> signInHistoryList=activitySignInMapper.getSignInHistoryForCurrentMonth(request.getCellphone());
        Map<String, List<PclassSignRule>> groupedByRuleType = signRuleList.stream()
                .collect(Collectors.groupingBy(PclassSignRule::getRuleType));
        Map<String,List<String>> provinceRuleMapping=signRuleList.stream().filter(r->StringUtils.isNotBlank(r.getProvince())).collect(Collectors.groupingBy(PclassSignRule::getRuleType,Collectors.mapping(PclassSignRule::getProvince, Collectors.toList())));
        Map<String,List<String>> cityRuleMapping=signRuleList.stream().filter(r->StringUtils.isNotBlank(r.getCity())).collect(Collectors.groupingBy(PclassSignRule::getRuleType,Collectors.mapping(PclassSignRule::getCity, Collectors.toList())));
        for(PclassSignRule pclassSignRule:signRuleList){
            checkRule(pclassInfoBase,pclassSignRule,signInHistoryList,provinceRuleMapping,cityRuleMapping);
        }
        doNextValidator(pclassInfoBase);
    }

    private void checkRule(PclassInfoBase pclassInfoBase,PclassSignRule pclassSignRule, List<SignInHistory> signInHistoryList,Map<String,List<String>> provinceRuleMapping,Map<String,List<String>> cityRuleMapping) {
        switch (pclassSignRule.getRuleType()){
            case "ALL":
                if(pclassSignRule.getProvince()==null&&pclassSignRule.getCity()==null) {
                    validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                        @Override
                        public List<SignInHistory> get() {
                            return signInHistoryList.stream().filter(s->checkDateRule(pclassSignRule,s.getSignInTime())).collect(Collectors.toList());
                        }
                    });
                }
                if(pclassSignRule.getProvince()!=null&&pclassSignRule.getCity()==null){
                    if(!provinceRuleMapping.get(pclassSignRule.getRuleType()).contains(pclassInfoBase.getProvince())){
                        return;
                    }
                    validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                        @Override
                        public List<SignInHistory> get() {
                            return signInHistoryList.stream().filter(s->checkDateRule(pclassSignRule,s.getSignInTime())&&provinceRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getProvince())).collect(Collectors.toList());
                        }
                    });
                }
                if(pclassSignRule.getProvince()!=null&&pclassSignRule.getCity()!=null){
                    if(!cityRuleMapping.get(pclassSignRule.getRuleType()).contains(pclassInfoBase.getCity())){
                        return;
                    }
                    validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                        @Override
                        public List<SignInHistory> get() {
                            return signInHistoryList.stream().filter(s->checkDateRule(pclassSignRule,s.getSignInTime())
                                    &&cityRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getCity())).collect(Collectors.toList());
                        }
                    });
                }
                break;
            case "PCLASS_TYPE":
                if(pclassSignRule.getPclassType().equalsIgnoreCase(request.getSignInType())) {
                    if (pclassSignRule.getProvince() == null && pclassSignRule.getCity() == null) {
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                return signInHistoryList.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())
                                        && checkPclassType(s,pclassInfoBase)).collect(Collectors.toList());
                            }
                        });
                    }
                    if (pclassSignRule.getProvince() != null && pclassSignRule.getCity() == null) {
                        if(!provinceRuleMapping.get(pclassSignRule.getRuleType()).contains(pclassInfoBase.getProvince())){
                            return;
                        }
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                return signInHistoryList.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())
                                        && checkPclassType(s,pclassInfoBase)
                                        && provinceRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getProvince())).collect(Collectors.toList());
                            }
                        });
                    }
                    if (pclassSignRule.getProvince() != null && pclassSignRule.getCity() != null) {
                        if(!cityRuleMapping.get(pclassSignRule.getRuleType()).contains(pclassInfoBase.getCity())){
                            return;
                        }
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                return signInHistoryList.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())
                                        && checkPclassType(s,pclassInfoBase)
                                        && cityRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getCity())).collect(Collectors.toList());
                            }
                        });
                    }
                }
                break;
            case "ACTIVITY_TYPE":
                List<SignInHistory> allSignInHistory=activitySignInMapper.getSignInHistory(request.getCellphone(), DateUtil.formatDateTime(pclassSignRule.getStartTime()));
                List<String> activityTypeList= Arrays.asList(Optional.ofNullable(pclassSignRule.getPclassType()).map(pclassTypeList->pclassTypeList.split(",")).orElse(new String[]{}));
                if(activityTypeList.contains(pclassInfoBase.getActivityType())) {
                    if (pclassSignRule.getProvince() == null && pclassSignRule.getCity() == null) {
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                List<SignInHistory> signInHistories=SignRuleDimension.WHOLE_LIFE.name().equalsIgnoreCase(pclassSignRule.getDimension())?allSignInHistory:signInHistoryList;
                                return signInHistories.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())&&activityTypeList.contains(pclassInfoBase.getActivityType())).collect(Collectors.toList());
                            }
                        });
                    }
                    if (pclassSignRule.getProvince() != null && pclassSignRule.getCity() == null) {
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                List<SignInHistory> signInHistories=SignRuleDimension.WHOLE_LIFE.name().equalsIgnoreCase(pclassSignRule.getDimension())?allSignInHistory:signInHistoryList;
                                return signInHistories.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())
                                        && activityTypeList.contains(s.getActivityType())
                                        && provinceRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getProvince())).collect(Collectors.toList());
                            }
                        });
                    }
                    if (pclassSignRule.getProvince() != null && pclassSignRule.getCity() != null) {
                        validateSignCount(pclassSignRule, new Supplier<List<SignInHistory>>() {
                            @Override
                            public List<SignInHistory> get() {
                                List<SignInHistory> signInHistories=SignRuleDimension.WHOLE_LIFE.name().equalsIgnoreCase(pclassSignRule.getDimension())?allSignInHistory:signInHistoryList;
                                return signInHistories.stream().filter(s -> checkDateRule(pclassSignRule, s.getSignInTime())
                                        && activityTypeList.contains(s.getActivityType())
                                        && cityRuleMapping.get(pclassSignRule.getRuleType()).contains(s.getCity())).collect(Collectors.toList());
                            }
                        });
                    }
                }
                break;
            default:
                break;
        }
    }

    private boolean checkPclassType(SignInHistory s,PclassInfoBase pclassInfoBase) {
        if(!ActivityChannel.INSIDE_PCLASS.name().equalsIgnoreCase(request.getSignInType())) {
            return s.getChannel().equalsIgnoreCase(request.getSignInType());
        }
        if(request.isYousheng()){
            return s.getChannel().equalsIgnoreCase(request.getSignInType())&&!INSIDE_PCLASS_PNE_TYPE.equalsIgnoreCase(s.getActivityType());
        }
        return s.getChannel().equalsIgnoreCase(request.getSignInType())&&INSIDE_PCLASS_PNE_TYPE.equalsIgnoreCase(s.getActivityType());
    }

    private static void validateSignCount(PclassSignRule pclassSignRule, Supplier<List<SignInHistory>> getSignInHistory) {
        List<SignInHistory> currentDaySignInHistoryList = getSignInHistory.get();
        if (currentDaySignInHistoryList.size() >= pclassSignRule.getLimitCount()) {
            log.info("sign rule not pass with rule={}", JSONUtil.toJsonStr(pclassSignRule));
            throw new SignInValidateException(pclassSignRule.getTip());
        }
    }

    public static boolean isToday(Date date) {
        LocalDate today = LocalDate.now();
        LocalDate dateToCheck = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        return today.equals(dateToCheck);
    }

    public static boolean isThisMonth(Date date) {
        LocalDate currentDate = LocalDate.now();
        LocalDate dateToCheck = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        return currentDate.getMonthValue() == dateToCheck.getMonthValue() &&
                currentDate.getYear() == dateToCheck.getYear();
    }

    public static boolean checkDateRule(PclassSignRule pclassSignRule,Date date){
        if(SignRuleDimension.BY_DAY.name().equalsIgnoreCase(pclassSignRule.getDimension())){
            return isToday(date);
        }else if(SignRuleDimension.BY_MONTH.name().equalsIgnoreCase(pclassSignRule.getDimension())){
            return isThisMonth(date);
        }else if(SignRuleDimension.WHOLE_LIFE.name().equalsIgnoreCase(pclassSignRule.getDimension())){
            return true;
        }
        return false;
    }
}
