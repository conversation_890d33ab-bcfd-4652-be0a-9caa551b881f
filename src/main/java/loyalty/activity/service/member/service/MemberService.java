package loyalty.activity.service.member.service;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMMemberInfoRequest;
import com.cstools.data.internal.domain.crm.request.CRMQueryMemberIsAddRequest;
import com.cstools.data.internal.domain.crm.response.CRMQueryMemberInfoResponse;
import com.cstools.data.internal.domain.crm.response.CRMQueryMemberIsAddResponse;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.CRMNoSuchMemberException;
import loyalty.activity.service.member.domain.request.PclassMemberRequest;
import loyalty.activity.service.member.domain.response.PclassMemberResponse;
import loyalty.activity.service.pclass.domain.response.IsAddNcResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static loyalty.activity.service.common.utils.Converters.convertToPclassMemberResponse;
import static loyalty.activity.service.error.handler.CRMResponseChecker.checkCRMResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21 上午 10:55
 * @describe
 */
@Service
@Slf4j
public class MemberService {
    @Resource
    private CRMClient crmClient;

    public PclassMemberResponse getPclassMemberBaseInfo(PclassMemberRequest pclassMemberRequest) throws Exception {
        CRMQueryMemberInfoResponse memberInfoResponse = crmClient.queryMemberInfo(
                new CRMMemberInfoRequest(pclassMemberRequest.getCellphone(), null, pclassMemberRequest.getUnionId()));
        checkCRMResponse(memberInfoResponse.getErrCode(), memberInfoResponse.getErrMsg());
        if (null == memberInfoResponse.getCrmMemberInfoResponse()) {
            throw new CRMNoSuchMemberException();
        }
        return convertToPclassMemberResponse(memberInfoResponse);
    }

    /**
     * 判断是不是nc
     *
     * @param cellphone 手机号
     * @param unionId   unionId
     */
    public IsAddNcResponse isAddNc(String cellphone, String unionId) {
        IsAddNcResponse response = new IsAddNcResponse();
        CRMQueryMemberIsAddRequest request = new CRMQueryMemberIsAddRequest();
        request.setCellphone(cellphone);
        request.setUnionid(unionId);
        //更新会员
        try {
            CRMQueryMemberIsAddResponse crmResponse = crmClient.queryMemberIsAddInfo(request);
            if (null != crmResponse.getBody()) {
                response.setIsAdd(crmResponse.getBody().getIsadd());
                response.setQrCode(crmResponse.getBody().getQrcode());
            }
        } catch (Exception e) {
            log.info("error request to crm isAdd cellphone ={}", cellphone);
        }
        return response;
    }

}
