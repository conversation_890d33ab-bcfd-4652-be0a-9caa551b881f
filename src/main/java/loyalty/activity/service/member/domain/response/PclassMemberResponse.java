package loyalty.activity.service.member.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/20 上午 10:06
 * @describe
 */
@Data
public class PclassMemberResponse {
    private String cellphone;

    private String userName;

    private Date birthday;
    @ApiModelProperty(value = "0备孕 1已有宝宝")
    private Integer babyStatus;
}
