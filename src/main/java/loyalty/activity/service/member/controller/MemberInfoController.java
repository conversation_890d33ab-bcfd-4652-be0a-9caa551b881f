package loyalty.activity.service.member.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.member.domain.request.PclassMemberRequest;
import loyalty.activity.service.member.domain.response.PclassMemberResponse;
import loyalty.activity.service.member.service.MemberService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/member")
@RestController
@Slf4j
public class MemberInfoController {

    private final MemberService memberService;

    public MemberInfoController(MemberService memberService) {
        this.memberService = memberService;
    }

    @ApiOperation(value = "课程获取会员信息", notes = "getPclassMemberBaseInfo")
    @PostMapping(value = "/getPclassMemberBaseInfo")
    public InternalResponse<PclassMemberResponse> getPclassMemberBaseInfo(@RequestBody PclassMemberRequest pclassMemberRequest) throws Exception {
        InternalResponse<PclassMemberResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(memberService.getPclassMemberBaseInfo(pclassMemberRequest));
        return internalResponse;
    }


}
