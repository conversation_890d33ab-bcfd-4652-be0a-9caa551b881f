package loyalty.activity.service.error;


import com.cstools.data.model.error.InternalException;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;

public class SignInValidateException extends InternalException {

    private final String errorMsg;

    public SignInValidateException(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String getErrorCode() {
        return "03";
    }

    @Override
    public String getCustomizeMessage() {
        return this.errorMsg;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{this.errorMsg};
    }
}
