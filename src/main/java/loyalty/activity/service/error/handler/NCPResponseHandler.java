package loyalty.activity.service.error.handler;

import com.cstools.data.internal.domain.ncp.response.InternalNCPRestV2Response;
import com.cstools.data.internal.domain.ncp.response.NCPQrcodeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.NcClientException;

@Slf4j
public class NCPResponseHandler {

    private final static String NCP_SUCCESS = "200";
    private final static String NCP_V2_SUCCESS = "0";

    public static void handleNCPResponse(InternalNCPRestV2Response internalNCPRestResponse) {
        if (!NCP_SUCCESS.equals(internalNCPRestResponse.getErrCode())) {
            throw new NcClientException(String.format("ncp code [%s] msg [%s]", internalNCPRestResponse.getErrCode(), internalNCPRestResponse.getErrMsg()));
        }

    }

    public static void handleNCPV2Response(NCPQrcodeQueryResponse response) {
        if (!NCP_V2_SUCCESS.equals(response.getCode())) {
            throw new NcClientException(String.format("ncp code [%s] msg [%s]", response.getCode(), response.getMsg()));
        }
    }

}
