package loyalty.activity.service.error.handler;


import loyalty.activity.service.error.CRMOtherException;
import loyalty.activity.service.error.CRMSysException;

import java.util.HashMap;
import java.util.Map;

public final class CRMResponseChecker {

    private static final Map<String, String> codeMap = new HashMap<String, String>() {{
        put("301", "手机号码格式不正确");
        put("302", "姓名格式不正确");
        put("308", "验证码不正确");
        put("312", "二胎与一胎时间间隔不正确");
        put("314", "必须接受推送信息");
        put("331", "一胎预产期不正确");
        put("351", "二胎预产期不正确");
    }};
    private final static String CRM_SYS_ERROR = "201";

    public static void checkCRMResponse(String code, String errMsg) {
        if ("200".equals(code)) {
            return;
        }

        if (CRM_SYS_ERROR.equals(code)) {
            throw new CRMSysException();
        }

        if (code.equals("301") || code.equals("302") || code.equals("308") || code.equals("312")
                || code.equals("314") || code.equals("331") || code.equals("351")) {
            String mes = codeMap.get(code).toString();
            throw new CRMOtherException(code, codeMap.get(code).toString());
        }
        throw new RuntimeException("Call crm failled with errorCode = " + code + ", msg = " + errMsg);

    }
}
