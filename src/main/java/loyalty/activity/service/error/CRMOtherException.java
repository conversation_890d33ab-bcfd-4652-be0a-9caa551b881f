package loyalty.activity.service.error;

import loyalty.activity.service.sharelib.errors.InternalException;

public class CRMOtherException extends InternalException {
    private String errMsg;
    private String errorCode;

    public CRMOtherException(String errorCode, String errMsg) {
        this.errorCode = errorCode;
        this.errMsg = errMsg;
    }


    @Override
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getCustomizeMessage() {
        return String.format("errorcode=[%s], errMsg=[%s]", errorCode, errMsg);
    }

    public String getErrMsg() {
        return errMsg;
    }
}
