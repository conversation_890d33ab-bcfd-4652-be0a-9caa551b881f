package loyalty.activity.service.error;

import loyalty.activity.service.sharelib.errors.InternalException;

public class DataException extends InternalException {
    private final String message;

    public DataException(String message) {
        this.message = message;
    }

    @Override
    public String getErrorCode() {
        return "51";
    }

    @Override
    public Object[] getParams() {
        return new Object[]{message};
    }
}