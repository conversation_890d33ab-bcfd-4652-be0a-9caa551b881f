package loyalty.activity.service.error;


import loyalty.activity.service.sharelib.errors.InternalException;

public class NcClientException extends InternalException {
    private String message;

    public NcClientException(String message) {
        this.message = message;
    }

    @Override
    public String getErrorCode() {
        return "35";
    }


    @Override
    public String getCustomizeMessage() {
        return String.format("Request to NCP client fail 【{%s}】 ", message);
    }
}
