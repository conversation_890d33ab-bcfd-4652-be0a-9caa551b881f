package loyalty.activity.service.error;


import com.cstools.data.model.error.InternalException;

public class CustomizeException extends InternalException {

    private final String errorMsg;

    public CustomizeException(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String getErrorCode() {
        return "03";
    }

    @Override
    public String getCustomizeMessage() {
        return this.errorMsg;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{this.errorMsg};
    }
}
