package loyalty.activity.service.external.util;
import lombok.SneakyThrows;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.Base64;
public class UrlParseUtils {

    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String SECRET_KEY = "WSVwZlF70yX5edqH69qW-6XUTTeHZN6O8uENPgiemN8"; // 16/24/32字节密钥


    public static String generateAESKey(int keySize) throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(keySize, new SecureRandom()); // 添加安全随机数源
        return Base64.getUrlEncoder().withoutPadding()
                .encodeToString(keyGen.generateKey().getEncoded());
    }

    public static String encrypt(String input) throws Exception {
        byte[] keyBytes = Base64.getUrlDecoder().decode(SECRET_KEY); // 解码Base64密钥
        SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encrypted = cipher.doFinal(input.getBytes());
        return Base64.getUrlEncoder().withoutPadding().encodeToString(encrypted);
    }

    @SneakyThrows
    public static void main(String[] args) {
       String key= generateAESKey(256);
       System.out.println(key);
       String url=encrypt("https://testop.ismartgo.cn/mjroadshow/h5/signup-ready.html");
       System.out.println(url);
    }
}

