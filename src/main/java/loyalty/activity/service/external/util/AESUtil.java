package loyalty.activity.service.external.util;

import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

public class AESUtil {

    public final static String SHA1PRNGAESKEY = "951a9yB1z3AAi3kAmMKDfatjA61sjfe2";
    
    public static String encrypt(String content) {
        try {
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(SHA1PRNGAESKEY.getBytes("utf-8"));
            
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128, random);
            
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = content.getBytes("utf-8");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            
            byte[] result = cipher.doFinal(byteContent);
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }

    public static String decrypt(String sBase64Content) {
        try
        {
            //将Base64转换为数组
            byte[] content = Base64.decodeBase64(sBase64Content);

            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(SHA1PRNGAESKEY.getBytes("utf-8"));

            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128,random);

            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");

            // 创建密码器
            Cipher cipher = Cipher.getInstance("AES");
            // 初始化
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] result = cipher.doFinal(content);

            //将解密后得到的字节转换为字符串
            String sResult = new String(result, "utf-8");
            return sResult;
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String encryptPhone= encrypt("13800138001");
        System.out.println(encryptPhone);
        System.out.println(decrypt(encryptPhone));
    }
} 