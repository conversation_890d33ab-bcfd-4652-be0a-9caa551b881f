package loyalty.activity.service.external.util;

import com.alibaba.nacos.common.utils.MD5Utils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SignUtil {
    

    public static String generateSign(Map<String, String> params, String apiSecret) {
        // 获取所有参数值并排序,判断value是否为空
        List<String> values = new ArrayList<>();
        for (String value : params.values()) {
            if (StringUtils.isNotBlank(value)) {
                values.add(value);
            }
        }   
        
        Collections.sort(values);
        
        // 拼接参数值
        StringBuilder sb = new StringBuilder();
        for (String value : values) {
            sb.append(value);
        }
        
        // 拼接apiSecret并计算MD5
        sb.append(apiSecret);
        //MD5Utils.md5Hex(sb.toString(),"utf-8").toUpperCase();
        return DigestUtils.md5Hex(sb.toString()).toUpperCase();
    }
} 