package loyalty.activity.service.external.controller;


import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.external.domain.request.CSActivitySyncRequest;
import loyalty.activity.service.external.domain.response.ActivitySyncResponse;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import loyalty.activity.service.external.domain.request.CSActivityQuerySignupRequest;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.response.CSActivityQuerySignupResponse;
import loyalty.activity.service.external.service.CSActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;



@Slf4j
@RestController
@RequestMapping("/external/cs/activity")
public class CSActivityController {

    @Autowired
    private CSActivityService csActivityService;

    @ResponseBody
    @RequestMapping(value = "/sync", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/sync", notes = "CS同步活动数据", response = InternalResponse.class)
    @ApiLog
    public InternalResponse sync(@RequestBody CSActivitySyncRequest syncRequest) {
        InternalResponse internalResponse = InternalResponse.success();
        ActivitySyncResponse errorResponse = csActivityService.sync(syncRequest);
        if (errorResponse!=null&&errorResponse.getErrorDataList()!=null&&errorResponse.getErrorDataList().size()>0){
            internalResponse = InternalResponse.fail("03","数据格式错误").withBody(errorResponse);
        }
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/querySignup", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/querySignup", notes = "CS活动签名统计查询", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<CSActivityQuerySignupResponse> querySignup(@RequestBody CSActivityQuerySignupRequest querySignupRequest) {
        InternalResponse<CSActivityQuerySignupResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(csActivityService.querySignup(querySignupRequest));
        return internalResponse;
    }
}
