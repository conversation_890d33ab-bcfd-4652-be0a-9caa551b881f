package loyalty.activity.service.external.controller;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.external.service.JmgActivityService;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.web.bind.annotation.*;
import loyalty.activity.service.external.dto.JmgActivityApplyRequest;
import loyalty.activity.service.external.dto.JmgActivityRequest;
import loyalty.activity.service.external.dto.JmgSubscribeMsgRequest;
import loyalty.activity.service.external.dto.NotifyCheckDataRequest;
import loyalty.activity.service.external.dto.JmgSignDataRequest;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@Api(tags = "精明购活动接口")
@RestController
@RequestMapping("/external/jmg")
public class JmgActivityController {

    @Resource
    private JmgActivityService jmgActivityService;

    @ApiOperation(value = "活动报名", notes = "活动报名", response = InternalResponse.class)
    @RequestMapping(value = "/userApply", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLog(storage =true)
    public InternalResponse<Void> userApply(@RequestBody @Valid JmgActivityApplyRequest request) {
        log.info("start to request /jmg/userApply with request={}", JSONUtil.toJsonStr(request));
        jmgActivityService.userApply(request);
        return InternalResponse.success();
    }

    @ApiOperation(value = "订阅消息发送", notes = "订阅消息发送", response = InternalResponse.class)
    @RequestMapping(value = "/sendSubscribeMsg", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLog(storage =true)
    public InternalResponse<Void> sendSubscribeMsg(@RequestBody @Valid JmgSubscribeMsgRequest request) {
        log.info("start to request /jmg/sendSubscribeMsg with request={}", JSONUtil.toJsonStr(request));
        jmgActivityService.sendSubscribeMsg(request);
        return InternalResponse.success();
    }

    @ApiOperation("签到数据推送")
    @RequestMapping(value = "/bkapi/activity/notifycheckdata", method = RequestMethod.POST)
    @ResponseBody
    public InternalResponse<Void> notifyCheckData(@RequestBody NotifyCheckDataRequest request) {
        jmgActivityService.notifyCheckData(request);
        return InternalResponse.success();
    }

    @ApiOperation("活动信息同步")
    @RequestMapping(value = "/activitySync", method = RequestMethod.POST)
    @ResponseBody
    @ApiLog(storage = true)
    public InternalResponse<Void> syncActivityInfo(@RequestBody @Valid JmgActivityRequest request) {
        log.info("开始同步活动信息, request={}", JSONUtil.toJsonStr(request));
        jmgActivityService.syncActivityInfo(request);
        return InternalResponse.success();
    }

    @ApiOperation(value = "报名数据推送", notes = "推送报名数据到美赞荟", response = InternalResponse.class)
    @RequestMapping(value = "/notifySignData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLog(storage = true)
    public InternalResponse<Void> notifySignData(@RequestBody @Valid JmgSignDataRequest request) {
        log.info("开始推送报名数据到美赞荟, request={}", JSONUtil.toJsonStr(request));
        jmgActivityService.notifySignData(request.getActivityCode(), request.getSignInfoList());
        return InternalResponse.success();
    }
}
