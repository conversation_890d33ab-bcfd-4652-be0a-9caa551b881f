package loyalty.activity.service.external.controller;


import com.google.gson.Gson;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.external.db.app.*;
import loyalty.activity.service.external.service.NcpQueryService;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/external/ncpquery")
public class NcpQueryController {

    @Autowired
    private NcpQueryService ncpQueryService;


    @ApiLog
    @ApiOperation(value = "NCP 课程列表查询", notes = "查询字段 store_id")
    @RequestMapping(value = "/excutecampaign/getcampaignlist", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public StoreClassResponse runflow(@RequestBody @Valid StoreClassRequest storeClassRequest) {
        StoreClassResponse response = null;
        try {
            log.info("Start to run /excutecampaign/getcampaignlist ");
            List<StoreClassInfo> storeClassInfo = ncpQueryService.queryClassInfoForStore(storeClassRequest.getStoreId());
            response = new StoreClassResponse();
            response.setErrCode("200");
            response.setErrMsg("Success");
            response.setBody(storeClassInfo);
            return response;

        } catch (Exception e) {
            log.error("Error occurred while executing run /excutecampaign/getcampaignlist", e);
            response = new StoreClassResponse();
            response.setErrCode("201");
            response.setErrMsg(e.getMessage());
            return response;
        } finally {
            log.info("Completed executing run /excutecampaign/getcampaignlist {}", new Gson().toJson(response));
        }
    }

    @ApiLog
    @ApiOperation(value = "NCP 批量查询课程数据", notes = "查询字段 开始结束时间、课程编码、课程状态")
    @RequestMapping(value = "/excutecampaign/getclasseslist", method = RequestMethod.POST, produces = "application" +
        "/json")
    @ResponseBody
    public ParamQueryClassResponse getClassesList(@RequestBody @Valid ParamQueryClassRequest request) {
        ParamQueryClassResponse response = null;
        try {
            log.info("Start to run /excutecampaign/getclasseslist");
            PageResponse<ParamClassInfo> paramClassInfos = ncpQueryService.queryClassInfoForQueryParam(request);
            response = new ParamQueryClassResponse();
            response.setErrCode("200");
            response.setErrMsg("Success");
            response.setBody(paramClassInfos);
            return response;

        } catch (Exception e) {
            log.error("Error occurred while executing run /excutecampaign/getclasseslist", e);
            response = new ParamQueryClassResponse();
            response.setErrCode("201");
            response.setErrMsg(e.getMessage());
            return response;
        } finally {
            log.info("Completed executing run /excutecampaign/getclasseslist {}", new Gson().toJson(response));
        }
    }

}
