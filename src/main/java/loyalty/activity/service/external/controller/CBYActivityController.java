package loyalty.activity.service.external.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.request.CBYGetResearchRequest;
import loyalty.activity.service.external.domain.request.CBYQrcodeRequest;
import loyalty.activity.service.external.domain.response.CBYQrcodeResponse;
import loyalty.activity.service.external.service.CBYService;
import loyalty.activity.service.pnec.dto.PNECClassInfoDataDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDetailDto;
import loyalty.activity.service.pnec.dto.PNECClassInfoDto;
import loyalty.activity.service.pnec.dto.PclassResearchInfoDto;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;

import static loyalty.activity.service.sharelib.domain.dto.InternalResponse.success;

@Slf4j
@RestController
@RequestMapping("/external/cby")
@Api
public class CBYActivityController {

    @Autowired
    private CBYService cbyService;

    private final PNECClassService pnecClassService;

    public CBYActivityController(PNECClassService pnecClassService) {
        this.pnecClassService = pnecClassService;
    }

    @ResponseBody
    @RequestMapping(value = "/getQrcode", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "获取院内/调研二维码", notes = "获取院内/调研二维码", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<CBYQrcodeResponse> getQrcode(@RequestBody @Valid CBYQrcodeRequest cbyQrcodeRequest) {
        InternalResponse<CBYQrcodeResponse> internalResponse = InternalResponse.success();
        CBYQrcodeResponse qrcode = cbyService.getQrcode(cbyQrcodeRequest);
        internalResponse.withBody(qrcode);
        return internalResponse;
    }


    @ApiLog
    @ApiOperation(value = "妈妈班课程数据查询", notes = "妈妈班课程数据查询", response = InternalResponse.class)
    @RequestMapping(value = "/getData", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PNECClassInfoDataDto> getData(@ApiParam(required = true, value = "妈妈班课程编码") @RequestParam("classesCode") String classesCode,
                                                          @ApiParam(required = false, value = "员工号(查看员工邀约签到数使用)")@RequestParam(name="employeeNo",required = false) String employeeNo) {
        InternalResponse internalResponse = null;
        PNECClassInfoDataDto data = pnecClassService.getPClassData(classesCode,employeeNo);
        internalResponse = InternalResponse.success().withBody(data);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "调研码详情", notes = "调研码详情", response = InternalResponse.class)
    @RequestMapping(value = "/researchDetail", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PclassResearchInfoDto> researchDetail(@ApiParam(required = true, value = "调研编码") @RequestParam String researchId) {
        InternalResponse internalResponse = null;
        internalResponse = InternalResponse.success().withBody(pnecClassService.researchDetail(researchId));
        return internalResponse;
    }


    @ResponseBody
    @RequestMapping(value = "/getResearchList", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "获取调研数据", notes = "获取调研数据", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<PageResponse<PNECClassInfoDto>> getResearchList(@RequestBody @Valid CBYGetResearchRequest researchRequest) {
        InternalResponse<PageResponse<PNECClassInfoDto>> internalResponse = InternalResponse.success();
        internalResponse.withBody(pnecClassService.listResearch(researchRequest.adaptToSearchCriteria()));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/sync", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "同步活动课程数据", notes = "同步活动课程数据", response = InternalResponse.class)
    @ApiLog(storage =true)
    public InternalResponse sync(@RequestBody ActivitySyncRequest activitySyncRequest) {
        return InternalResponse.success().withBody(cbyService.syncActivityData(activitySyncRequest));
    }

    @ApiLog
    @ApiOperation(value = "课程详情查询", notes = "课程详情查询")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<PNECClassInfoDetailDto> getDetail(@RequestParam("pclassId") String pclassId) throws ParseException {
        InternalResponse internalResponse = null;
        PNECClassInfoDetailDto detail = pnecClassService.getDetail(pclassId);
        internalResponse = success().withBody(detail);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "获取cs课程npc签到数据", notes = "获取cs课程npc签到数据")
    @RequestMapping(value = "/getNcpSignListFromNcp", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<List> getNcpSignListFromNcp(@RequestParam("classCode") String classCode) throws ParseException {
        InternalResponse internalResponse = null;
        internalResponse = success().withBody(pnecClassService.getNcpSignFromNcp(classCode, 100));
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "个人码生成", notes = "个人码生成")
    @RequestMapping(value = "/getPersonQrCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> getPersonQrCode(@RequestParam("userId") String userId) {
        InternalResponse internalResponse = null;
        String data = pnecClassService.getPersonQrCode(userId);
        internalResponse = success().withBody(data);
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "批量更新二维码", notes = "批量更新二维码")
    @RequestMapping(value = "/batchUpdateQrcode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> batchUpdateQrcode() {
        InternalResponse internalResponse = null;
        cbyService.batchUpdateQrcode();
        internalResponse = success();
        return internalResponse;
    }
}
