package loyalty.activity.service.external.controller;

import com.cstools.data.internal.utils.loyalty.EncodeUtils;
import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/ncp")
@Api
public class NcpPclassController {

        @PostMapping("/generateToken")
        public Map generateToken(@RequestParam("classCode") String classCode){
            Map params=new HashMap();
            String s = EncodeUtils.encodeToMD5(("code=" + classCode + "8.142.106.193" + "MPC@pclass")).toLowerCase();
            params.put("注册token",s);
            return params;
        }
}
