package loyalty.activity.service.external.controller;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.CRMOtherException;
import loyalty.activity.service.error.SignInValidateException;
import loyalty.activity.service.external.db.model.CeRegisterLog;
import loyalty.activity.service.external.domain.request.InsidePClassRegisterRequest;
import loyalty.activity.service.external.domain.request.InsidePClassSignInRequest;
import loyalty.activity.service.external.domain.request.InsidePClassUpdateMemberInfoRequest;
import loyalty.activity.service.external.domain.response.InsidePClassInternalResponse;
import loyalty.activity.service.external.service.InsidePclassService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.errors.InternalException;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@Slf4j
@RestController
@RequestMapping("/external/inside/pclass")
@Api
public class InsidePclassController {

    @Autowired
    private InsidePclassService insidePclassService;

    @ResponseBody
    @RequestMapping(value = "/register", method = RequestMethod.POST,consumes ="application/x-www-form-urlencoded;charset=UTF-8", produces = "application/json")
    @ApiOperation(value = "/register", notes = "注册会员", response = InternalResponse.class)
    @ApiLog(storage = true)
    public InsidePClassInternalResponse register(InsidePClassRegisterRequest request) throws InterruptedException {
        InsidePClassInternalResponse response = InsidePClassInternalResponse.success().withMessage("注册成功！");
        CeRegisterLog ceRegisterLog = new CeRegisterLog();
        try {
            ceRegisterLog.setCeRequest(new Gson().toJson(request));
            insidePclassService.register(request,ceRegisterLog);
            ceRegisterLog.setIsSuc(true);
        } catch (CRMOtherException ce) {
            ceRegisterLog.setIsSuc(false);
            ceRegisterLog.setExceptionInfo(ExceptionUtil.getStackTrace(ce));
            String errorMessage = ce.getParams() == null ?
                    (InternalResponse.fail(ce.getErrorCode()).getMessage() == null ? ce.getErrMsg() :
                            InternalResponse.fail(ce.getErrorCode()).getMessage()) : Arrays.toString(ce.getParams());
            log.error("Internal error execute /external/inside/pclass/register with errorCode={}, message=[{}]", ce.getErrorCode(), errorMessage);
            ceRegisterLog.setExceptionInfo("errorCode="+ce.getErrorCode()+",message="+errorMessage+",stackTrace:"+ExceptionUtil.getStackTrace(ce));
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        }catch (InternalException ie) {
            ceRegisterLog.setIsSuc(false);
            String errorMessage = ie.getParams() == null ?
                    (InternalResponse.fail(ie.getErrorCode()).getMessage() == null ? ie.getMessage() :
                            InternalResponse.fail(ie.getErrorCode()).getMessage()) : Arrays.toString(ie.getParams());
            log.error("Internal error execute /external/inside/pclass/register with errorCode={}, message=[{}]", ie.getErrorCode(), errorMessage);
            ceRegisterLog.setExceptionInfo("errorCode="+ie.getErrorCode()+",message="+errorMessage+",stackTrace:"+ExceptionUtil.getStackTrace(ie));
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (Exception e) {
            ceRegisterLog.setIsSuc(false);
            ceRegisterLog.setExceptionInfo(ExceptionUtil.getStackTrace(e));
            log.error("execute /external/inside/pclass/register with error ", e);
            response = InsidePClassInternalResponse.fail().withMessage("注册失败,请联系管理员处理");
        }finally {
            insidePclassService.insertCeRegisterLogEntity(ceRegisterLog);
        }
        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/signIn", method = RequestMethod.POST,  produces = "application/json")
    @ApiOperation(value = "/signIn", notes = "签到", response = InternalResponse.class)
    @ApiLog(storage = true)
    public InsidePClassInternalResponse signIn(InsidePClassSignInRequest request) {
        InsidePClassInternalResponse response = InsidePClassInternalResponse.success().withMessage("签到成功！");
        try {
            insidePclassService.signIn(request);
        }catch (SignInValidateException se){
            String errorMessage = se.getParams() == null ? InternalResponse.fail(se.getErrorCode()).getMessage() : Arrays.toString(se.getParams());
            log.error("Internal error execute /external/inside/pclass/signIn with errorCode={}, message=[{}]", se.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (CRMOtherException ce) {
            String errorMessage = ce.getParams() == null ?
                    (InternalResponse.fail(ce.getErrorCode()).getMessage() == null ? ce.getErrMsg() :
                            InternalResponse.fail(ce.getErrorCode()).getMessage()) : Arrays.toString(ce.getParams());
            log.error("Internal error execute /external/inside/pclass/signIn with errorCode={}, message=[{}]", ce.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (InternalException ie) {
            String errorMessage = ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()).getMessage() : Arrays.toString(ie.getParams());
            log.error("Internal error execute /external/inside/pclass/signIn with errorCode={}, message=[{}]", ie.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (Exception e) {
            log.error("execute /external/inside/pclass/signIn with error ", e);
            response = InsidePClassInternalResponse.fail().withMessage("签到失败,请联系管理员处理");
        }
        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/bindWeChat", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/bindWeChat", notes = "会员微信绑定接口", response = InternalResponse.class)
    @ApiLog(storage = true)
    public InsidePClassInternalResponse bindWeChat(@RequestBody InsidePClassUpdateMemberInfoRequest request) {
        InsidePClassInternalResponse response = InsidePClassInternalResponse.success().withMessage("绑定成功！");
        try {
            insidePclassService.bindWeChat(request);
        } catch (CRMOtherException ce) {
            String errorMessage = ce.getParams() == null ?
                    (InternalResponse.fail(ce.getErrorCode()).getMessage() == null ? ce.getErrMsg() :
                            InternalResponse.fail(ce.getErrorCode()).getMessage()) : Arrays.toString(ce.getParams());
            log.error("Internal error execute /external/inside/pclass/bindWeChat with errorCode={}, message=[{}]", ce.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (InternalException ie) {
            String errorMessage = ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()).getMessage() : Arrays.toString(ie.getParams());
            log.error("Internal error execute /external/inside/pclass/bindWeChat with errorCode={}, message=[{}]", ie.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (Exception e) {
            log.error("execute /external/inside/pclass/bindWeChat with error ", e);
            response = InsidePClassInternalResponse.fail().withMessage("绑定失败,请联系管理员处理");
        }
        return response;
    }

    @ResponseBody
    @RequestMapping(value = "/getRegisterRecruitCode", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getRegisterRecruitCode", notes = "获取创建人角色", response = InternalResponse.class)
    public InsidePClassInternalResponse getRegisterRecruitCode(@RequestParam String classCode) {
        InsidePClassInternalResponse response = InsidePClassInternalResponse.success();
        try {
            insidePclassService.getRegisterRecruitCode(classCode);
        } catch (CRMOtherException ce) {
            String errorMessage = ce.getParams() == null ?
                    (InternalResponse.fail(ce.getErrorCode()).getMessage() == null ? ce.getErrMsg() :
                            InternalResponse.fail(ce.getErrorCode()).getMessage()) : Arrays.toString(ce.getParams());
            log.error("Internal error execute /external/inside/pclass/getRegisterRecruitCode with errorCode={}, message=[{}]", ce.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (InternalException ie) {
            String errorMessage = ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()).getMessage() : Arrays.toString(ie.getParams());
            log.error("Internal error execute /external/inside/pclass/getRegisterRecruitCode with errorCode={}, message=[{}]", ie.getErrorCode(), errorMessage);
            response = InsidePClassInternalResponse.fail().withMessage(errorMessage);
        } catch (Exception e) {
            log.error("execute /external/inside/pclass/getRegisterRecruitCode with error ", e);
            response = InsidePClassInternalResponse.fail().withMessage("绑定失败,请联系管理员处理");
        }
        return response;
    }
}
