package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("活动信息请求")
public class JmgActivityRequest {

    @ApiModelProperty(value = "活动编码", required = true)
    @NotBlank(message = "活动编码不能为空")
    private String code;
    
    @ApiModelProperty(value = "开始报名时间", required = true)
    @NotBlank(message = "开始报名时间不能为空")
    private String registrationBeginDate;
    
    @ApiModelProperty(value = "报名结束时间", required = true)
    @NotBlank(message = "报名结束时间不能为空")
    private String registrationEndDate;
    
    @ApiModelProperty(value = "活动状态(0已取消 1执行中)", required = true)
    @NotBlank(message = "活动状态不能为空")
    private String status;
    
    @ApiModelProperty(value = "页面标题", required = true)
    @NotBlank(message = "页面标题不能为空")
    private String pageTitle;

    @ApiModelProperty(value = "logo图", required = false)
    //@NotBlank(message = "logo图不能为空")
    private String logoImg;
    
    @ApiModelProperty(value = "列表缩略图", required = false)
    //@NotBlank(message = "列表缩略图不能为空")
    private String coverImg;
    
    @ApiModelProperty(value = "详情头部背景图", required = false)
    //@NotBlank(message = "详情头部背景图不能为空")
    private String topImg;
    
    @ApiModelProperty(value = "活动详情背景图", required = false)
    //@NotBlank(message = "活动详情背景图不能为空")
    private String detailImg;
    
    @ApiModelProperty("限制报名人数")
    private Integer restrictAttendance;
    
    @ApiModelProperty("海报图")
    private String posterImg;
    
    @ApiModelProperty("吊起海报图")
    private String posterQrImg;
} 