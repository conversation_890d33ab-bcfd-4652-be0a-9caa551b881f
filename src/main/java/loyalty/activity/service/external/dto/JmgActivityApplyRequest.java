package loyalty.activity.service.external.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;


@Data
@ApiModel("cs活动报名请求")
public class JmgActivityApplyRequest {
    @ApiModelProperty(value = "活动编码", required = true)
    @NotBlank(message = "活动编码不能为空")
    private String code;
    
    @ApiModelProperty(value = "openid", required = true)
    private String openid;
    
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String cellphone;
    
    @ApiModelProperty("小程序unionid")
    private String unionid;
    
    @ApiModelProperty("客户名称")
    private String userName;
    
    @ApiModelProperty("宝宝生日或预产期")
    private String babyBirthday;
    
    @ApiModelProperty("宝宝状态(0备孕 1已有宝宝)")
    private Integer babyStatus;
    
    @ApiModelProperty("出席人数")
    private Integer attendance;
    
    @ApiModelProperty(value = "报名时间", required = true)
    @NotBlank(message = "报名时间不能为空")
    @JsonProperty("apply_time")
    private String applyTime;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("当前经度")
    @JsonProperty("current_longitude")
    private String currentLongitude;
    
    @ApiModelProperty("当前纬度")
    @JsonProperty("current_latitude")
    private String currentLatitude;
} 