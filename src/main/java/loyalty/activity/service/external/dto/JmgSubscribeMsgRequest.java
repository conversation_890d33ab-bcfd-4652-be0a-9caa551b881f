package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;



@Data
@ApiModel("jmg订阅消息请求")
public class JmgSubscribeMsgRequest {
    @ApiModelProperty(value = "活动编码", required = true)
    @NotBlank(message = "活动编码不能为空")
    private String code;
    
    @ApiModelProperty(value = "openid", required = true)
    @NotBlank(message = "openid不能为空")
    private String openid;
    
    @ApiModelProperty(value = "手机号", required = true)
    private String cellphone;
    
    @ApiModelProperty("小程序unionid")
    private String unionid;
    
    @ApiModelProperty(value = "消息类型(1.报名成功通知 2.活动状态更新)", required = true)
    private Integer subscribeType;

    @ApiModelProperty("活动状态更新内容")
    private String updateContent;

    @ApiModelProperty("跳转url")
    private String redirectUrl;



} 