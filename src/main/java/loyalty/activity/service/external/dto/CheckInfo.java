package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("签到信息")
@NoArgsConstructor
@AllArgsConstructor
public class CheckInfo {
    @ApiModelProperty("用户openid")
    private String openid;
    
    @ApiModelProperty("用户unionid")
    private String unionid;
    
    @ApiModelProperty("宝妈昵称")
    private String nickname;
    
    @ApiModelProperty("联系手机号(AES加密)")
    private String contactphoneencrypt;
    
    @ApiModelProperty("签到时间(yyyy-MM-dd HH:mm:ss)")
    private String checkindate;
} 