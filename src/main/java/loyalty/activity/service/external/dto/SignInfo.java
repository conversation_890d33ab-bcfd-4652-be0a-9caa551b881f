package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("报名信息")
public class SignInfo {
    @ApiModelProperty(value = "用户openid", required = true)
    private String openid;
    
    @ApiModelProperty("用户unionid")
    private String unionid;
    
    @ApiModelProperty(value = "会员手机号AES加密", required = true)
    private String userphoneencrypt;
    
    @ApiModelProperty("宝妈昵称")
    private String nickname;
    
    @ApiModelProperty("宝宝生日或预产期,采用yyyy-MM-dd格式")
    private String babybirthday;
    
    @ApiModelProperty("联系手机号AES加密")
    private String contactphoneencrypt;
    
    @ApiModelProperty("出席人数")
    private String attendance;
    
    @ApiModelProperty("用户经纬度-当前经度")
    private String currentlongitude;
    
    @ApiModelProperty("用户经纬度-当前纬度")
    private String currentlatitude;
    
    @ApiModelProperty(value = "报名时间，采用yyyy-MM-dd HH:mm:ss格式", required = true)
    private String applytime;

    @ApiModelProperty(value = "报名状态，1 正常  0取消", required = true)
    private Integer signstatus;
} 