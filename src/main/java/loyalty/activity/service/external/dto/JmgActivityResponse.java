package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("活动信息响应")
public class JmgActivityResponse {
    @ApiModelProperty(value = "活动编码", required = true)
    private String code;
    
    @ApiModelProperty(value = "开始报名时间", required = true)
    private String registrationBeginDate;
    
    @ApiModelProperty(value = "报名结束时间", required = true)
    private String registrationEndDate;
    
    @ApiModelProperty(value = "活动状态(0已取消 1执行中)", required = true)
    private String status;
    
    @ApiModelProperty(value = "页面标题", required = true)
    private String pageTitle;
    
    @ApiModelProperty(value = "列表缩略图", required = true)
    private String coverImg;
    
    @ApiModelProperty(value = "详情头部背景图", required = true)
    private String topImg;
    
    @ApiModelProperty(value = "活动详情背景图", required = true)
    private String detailImg;
    
    @ApiModelProperty("限制报名人数")
    private Integer restrictAttendance;
    
    @ApiModelProperty("海报图")
    private String posterImg;
    
    @ApiModelProperty("二维码海报图")
    private String posterQrImg;
} 