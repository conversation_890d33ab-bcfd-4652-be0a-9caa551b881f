package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("活动同步请求")
public class JmgActivitySyncRequest {
    @ApiModelProperty(value = "分配的appid", required = true)
    private String appid;
    
    @ApiModelProperty(value = "UTC+8时间，与当前时间差不得超过10分钟", required = true)
    private String timestamp;
    
    @ApiModelProperty(value = "请参考app接求说明详述说明", required = true)
    private String encoded;
    
    @ApiModelProperty(value = "活动信息", required = true)
    private JmgActivityResponse body;
} 