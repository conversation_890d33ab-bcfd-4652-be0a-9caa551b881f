package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("金马股发送订阅消息请求")
public class JmgSendSubscribeMsgRequest {
    @ApiModelProperty(value = "活动编码", required = true)
    private String code;
    
    @ApiModelProperty(value = "openid", required = true)
    private String openid;
    
    @ApiModelProperty(value = "手机号", required = true)
    private String cellphone;
    
    @ApiModelProperty("小程序unionid")
    private String unionid;
    
    @ApiModelProperty(value = "消息类型(1.报名成功通知 2.签到提醒)", required = true)
    private Integer subscribeType;
} 