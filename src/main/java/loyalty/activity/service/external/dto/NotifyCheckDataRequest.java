package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
@ApiModel("签到数据推送请求")
public class NotifyCheckDataRequest {
    @ApiModelProperty(value = "活动编码", required = true)
    private String activitycode;
    
    @ApiModelProperty(value = "签到记录数", required = true)
    private Integer recordcnt;
    
    @ApiModelProperty(value = "签到信息列表", required = true)
    private List<CheckInfo> recordlist;
    
    @ApiModelProperty(value = "当前时间(yyyy-MM-dd HH:mm:ss)", required = true)
    private String timestamp;
} 