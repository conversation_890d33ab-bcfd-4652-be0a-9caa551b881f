package loyalty.activity.service.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

/**
 * 报名数据推送请求
 */
@Data
@ApiModel(description = "报名数据推送请求")
public class JmgSignDataRequest {
    
    @NotBlank(message = "活动编码不能为空")
    @ApiModelProperty(value = "活动编码", required = true)
    private String activityCode;
    
    @NotEmpty(message = "报名信息列表不能为空")
    @Valid
    @ApiModelProperty(value = "报名信息列表", required = true)
    private List<SignInfo> signInfoList;
} 