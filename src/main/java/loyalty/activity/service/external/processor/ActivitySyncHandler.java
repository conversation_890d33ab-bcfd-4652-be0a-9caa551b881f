package loyalty.activity.service.external.processor;

import loyalty.activity.service.error.DataIsNullException;
import loyalty.activity.service.external.db.model.ActivityPO;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.response.ActivitySyncResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.OperateType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityOwnerRel;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class ActivitySyncHandler {

    @Value("${spring.application.name}")
    public String applicationName;

    protected final PclassInfoMapper pclassInfoMapper;

    protected ActivitySyncHandler(PclassInfoMapper pclassInfoMapper) {
        this.pclassInfoMapper = pclassInfoMapper;
    }

    public List<ActivitySyncResponse.ErrorData> validate(List<ActivitySyncRequest.ActivityInfo> activityInfoList){
        if(activityInfoList==null||activityInfoList.isEmpty()){
            throw new DataIsNullException();
        }
        List<ActivitySyncResponse.ErrorData> errorDataList = new ArrayList<>();
        for (int i = 0; i < activityInfoList.size(); i++) {
            ActivitySyncRequest.ActivityInfo activityInfo = activityInfoList.get(i);
            ActivitySyncResponse.ErrorData errorData = doValidate(activityInfo, i);
            Optional.ofNullable(errorData).ifPresent(errorDataList::add);
        }
        return errorDataList;
    }

    public ActivitySyncResponse.ErrorData doValidate(ActivitySyncRequest.ActivityInfo activityInfo,int index){
        List<String> errorList = new ArrayList<>();
        if(StringUtils.isBlank(activityInfo.getActivityCode())) {
            errorList.add("第" + (index + 1) + "条数据 【activity_code】不能为空");
        }
        if (StringUtils.isBlank(activityInfo.getActivityType())){
            errorList.add("字段【activity_type】不能为空");
            activityInfo.setIsCorrectData(false);
        }
        if (StringUtils.isBlank(activityInfo.getConsumerActivityType())){
            errorList.add("字段【consumer_activity_type】不能为空");
            activityInfo.setIsCorrectData(false);
        }
        if (StringUtils.isBlank(activityInfo.getOwnerId())){
            errorList.add("字段【owner_id】不能为空");
            activityInfo.setIsCorrectData(false);
        }
        if (StringUtils.isBlank(activityInfo.getEventStartDate())){
            errorList.add("字段【event_start_date】不能为空");
            activityInfo.setIsCorrectData(false);
        }
        if (StringUtils.isBlank(activityInfo.getEventEndDate())){
            errorList.add("字段【event_end_date】不能为空");
            activityInfo.setIsCorrectData(false);
        }
       /* if(activityInfo.getProvince()==null){
            errorList.add("字段【province】不能为null");
            activityInfo.setIsCorrectData(false);
        }
        if(activityInfo.getCity()==null){
            errorList.add("字段【city】不能为null");
            activityInfo.setIsCorrectData(false);
        }*/
        if(!errorList.isEmpty()){
            ActivitySyncResponse.ErrorData errorData = new ActivitySyncResponse.ErrorData();
            errorData.setActivityCode(activityInfo.getActivityCode());
            errorData.setErrorDescList(errorList);
            return errorData;
        }

        return null;
    }

    public List<ActivityPO> buildActivityPO(List<ActivitySyncRequest.ActivityInfo> activityInfoList, List<String> errorCodeList){
        List<ActivityPO> activityPOList = new ArrayList<>();
        for (ActivitySyncRequest.ActivityInfo activityInfo : activityInfoList) {
            if(errorCodeList.contains(activityInfo.getActivityCode())){
                continue;
            }
            ActivityPO activityPO = new ActivityPO();
            activityPO.setActivity(activityInfo.adaptToActivity(applicationName));
            ActivityOwnerRel activityOwnerRel = new ActivityOwnerRel(activityInfo.getOwnerId(),Boolean.TRUE,applicationName);
            activityPO.setActivityOwnerRel(activityOwnerRel);
            activityPO.setPclassInfo(activityInfo.adaptToPclassInfo(applicationName));
            activityPO.setActivityAddress(activityInfo.adaptToActivityAddress(applicationName));
            activityPOList.add(activityPO);
            PclassInfo oldPclassInfo = pclassInfoMapper.getByClassesCode(activityInfo.getActivityCode());
            if (oldPclassInfo==null){
                activityPO.setOperateType(OperateType.INSERT.name());
            }else {
                activityPO.setOperateType(OperateType.UPDATE.name());
                Long activityId = oldPclassInfo.getActivityId();
                activityPO.getActivity().setId(activityId);
                activityPO.getActivityOwnerRel().setActivityId(activityId);
                activityPO.getPclassInfo().setActivityId(activityId);
                activityPO.getPclassInfo().setActionType(OperateType.UPDATE.name());
                activityPO.getActivityAddress().setActivityId(activityId);
            }
            activityPO.setIsCorrectData(activityInfo.getIsCorrectData());
        }
        return activityPOList;
    }
}
