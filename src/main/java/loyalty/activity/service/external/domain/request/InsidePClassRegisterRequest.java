package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InsidePClassRegisterRequest{

    private String mobile;
    private Integer type;
    @JsonProperty("pne_mobile")
    private String pneMobile;
    private String code;
    private String pnecid;
    private String pnaid;
    private String pnrid;
    private String babybirth;
    private String secbabybirth;
    private String name;
    @JsonProperty("is_agree")
    private Integer isAgree;
}
