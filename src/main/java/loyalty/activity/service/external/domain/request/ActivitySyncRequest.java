package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import loyalty.activity.service.common.enums.ActivityStatus;
import loyalty.activity.service.common.enums.CSActivityType;
import loyalty.activity.service.common.enums.ConsumerActivityBigType;
import loyalty.activity.service.common.utils.DateUtils;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityAddress;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

import static loyalty.activity.service.common.constants.AppConstants.ADMIN_USER;
import static loyalty.activity.service.common.constants.AppConstants.MJT_SUPPLIER;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActivitySyncRequest {

    @ApiModelProperty(value = "课程/活动列表", required = true)
    private List<ActivityInfo> activityList;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    public static class ActivityInfo {
        @ApiModelProperty(value = "课程/活动编码", required = true)
        @JsonProperty("activity_code")
        @JsonAlias(value = {"activityCode","activity_code"})
        private String activityCode;
        @ApiModelProperty(value = "主题", required = true)
        @JsonProperty("topic")
        private String topic;
        @ApiModelProperty(value = "活动类型", required = true)
        @JsonProperty("activity_type")
        @JsonAlias(value = {"activityType","activity_type"})
        private String activityType;

        @ApiModelProperty(value = "消费者活动大类", required = true)
        @JsonProperty("consumer_activity_type")
        @JsonAlias(value = {"consumerActivityType","consumer_activity_type"})
        private String consumerActivityType;

        @ApiModelProperty(value = "NC门店编码，多个用分号（;）隔开", required = true)
        @JsonAlias(value = {"store_code","storeCode"})
        @JsonProperty("store_code")
        private String storeCode;
        @JsonAlias(value = {"event_date","eventDate"})
        @JsonProperty("event_date")
        @JsonIgnore
        private String eventDate;
       @ApiModelProperty(value = "活动开始时间", required = true)
       @JsonProperty("event_start_date")
       @JsonAlias(value = {"event_start_date","eventStartDate"})
        //private String eventEndDate;
        private String eventStartDate;
        @ApiModelProperty(value = "活动结束时间", required = true)
        @JsonProperty("event_end_date")
        @JsonAlias(value = {"event_end_date","eventEndDate"})
        private String eventEndDate;
        @ApiModelProperty(value = "活动状态,[Active(有效)/Inactive(无效)]", required = true)
        @JsonProperty("activity_status")
        @JsonAlias(value = {"activity_status","activityStatus"})
        private String activityStatus;
        @ApiModelProperty(value = "活动申请人员工编号", required = true)
        @JsonProperty("owner_id")
        @JsonAlias(value = {"ownerId","owner_id"})
        private String ownerId;
        @ApiModelProperty(value = "活动申请人员名字", required = true)
        @JsonAlias(value = {"owner_name","ownerName"})
        @JsonProperty("owner_name")
        private String ownerName;
        @ApiModelProperty(value = "省份", required = true)
        @JsonProperty("province")
        private String province;
        @ApiModelProperty(value = "城市", required = true)
        @JsonProperty("city")
        private String city;
        @ApiModelProperty(value = "活动地址（详细）", required = true)
        @JsonProperty("address")
        private String address;
        @ApiModelProperty(value = "活动地点", required = true)
        private String place;
        @ApiModelProperty(value = "活动地点经度（计划）", required = true)
        @JsonProperty("longitude")
        private String longitude;
        @ApiModelProperty(value = "活动地点纬度（计划）", required = true)
        @JsonProperty("latitude")
        private String latitude;
        @ApiModelProperty(value = "微信报名人数限制")
        @JsonProperty("limit_count")
        @JsonAlias(value = {"limitCount","limit_count"})
        @JsonIgnore
        private Integer limitCount;
        @JsonIgnore
        private Boolean isCorrectData = true;

        @ApiModelProperty(value = "课程名称")
        @JsonProperty("course_name")
        @JsonAlias(value = {"course_name","courseName"})
        private String courseName;

        @ApiModelProperty(value = "咨询热线")
        @JsonProperty("hotline")
        @JsonAlias(value = {"hotline"})
        private String hotline;

        @ApiModelProperty(value = "专家姓名")
        @JsonProperty("expert_name")
        @JsonAlias(value = {"expert_name","expertName"})
        private String expertName;

        @ApiModelProperty(value = "活动执行负责人")
        @JsonProperty("applicant_name")
        @JsonAlias(value = {"applicant_name","applicantName"})
        private String applicantName;

        @ApiModelProperty(value = "活动执行负责人手机号")
        @JsonProperty("applicant_mobile")
        @JsonAlias(value = {"applicant_mobile","applicantMobile"})
        private String applicantMobile;

        @ApiModelProperty(value = "直播平台")
        private String livestreamPlatform;


        public Activity adaptToActivity(String operator){
            Activity activity = new Activity();
            activity.setTopic(this.getTopic()==null?this.getActivityType():this.getTopic());
            activity.setChannel(ConsumerActivityBigType.get(this.consumerActivityType,this.activityType).name());
            activity.setOwnerId(this.getOwnerId());
            activity.setStartTime(StringUtils.isNotBlank(eventStartDate)?DateUtils.parseDate(eventStartDate):null);
            activity.setEndTime(StringUtils.isNotBlank(eventEndDate)?DateUtils.parseDate(eventEndDate):null);
            activity.setIsEnabled(ActivityStatus.getByStatus(this.activityStatus));
            if(StringUtils.isNotBlank(this.eventDate)){
                activity.setStartTime(DateUtils.parseDate(eventDate));
                eventDate +=" 23:59:59";
                if (CSActivityType.LARGE_CS.getValue().equals(activityType) || CSActivityType.MIDDLE_CS.getValue().equals(activityType)){
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(DateUtils.parseDate(eventDate));
                    calendar.add(Calendar.DATE,1);
                    activity.setEndTime(calendar.getTime());
                }else {
                    activity.setEndTime(DateUtils.parseDate(eventDate));
                }
            }
            activity.setCreatedByUser(ADMIN_USER);
            activity.setUpdatedByUser(ADMIN_USER);
            activity.setCreator(operator);
            activity.setUpdater(operator);
            return activity;
        }

        public ActivityAddress adaptToActivityAddress(String operator){
            ActivityAddress activityAddress=new ActivityAddress();
            activityAddress.setProvince(this.getProvince());
            activityAddress.setCity(this.getCity());
            activityAddress.setAddress(this.getAddress());
            activityAddress.setLatitude(StringUtils.isNotBlank(this.getLatitude())?new BigDecimal(this.getLatitude()):null);
            activityAddress.setLongitude(StringUtils.isNotBlank(this.getLongitude())?new BigDecimal(this.getLongitude()):null);
            activityAddress.setPlace(this.getPlace());
            activityAddress.setCreatedByUser(ADMIN_USER);
            activityAddress.setUpdatedByUser(ADMIN_USER);
            activityAddress.setCreator(operator);
            activityAddress.setUpdater(operator);
            return activityAddress;
        }

        public PclassInfo adaptToPclassInfo(String operator) {
            PclassInfo pclassInfo = new PclassInfo();
            pclassInfo.setId(this.getActivityCode());
            pclassInfo.setPclassId(this.getActivityCode());
            pclassInfo.setClassesCode(activityCode);
            pclassInfo.setActivityType(this.getActivityType());
            pclassInfo.setNcCode(this.getStoreCode());
            pclassInfo.setDirectorExmployeeNum(this.getOwnerId());
            pclassInfo.setDirector(this.getApplicantName());
            pclassInfo.setDirectorExmployeeNum(this.getOwnerId());
            pclassInfo.setDirectorMobile(this.getApplicantMobile());
            pclassInfo.setLimitCount(this.getLimitCount());
            pclassInfo.setIsEnabled(ActivityStatus.getByStatus(this.activityStatus));
            pclassInfo.setStatus(this.activityStatus);
            pclassInfo.setIsDeleted(false);
            pclassInfo.setOwnerName(this.getOwnerName());
            pclassInfo.setOwnerEmployeeNum(this.getOwnerId());
            pclassInfo.setOwnerMobile(this.getApplicantMobile());
            pclassInfo.setCourseName(StringUtils.isBlank(this.getCourseName())?this.getTopic():this.getCourseName());
            pclassInfo.setExpertName(this.getExpertName());
            pclassInfo.setHotline(this.getHotline());
               // pclassInfo.setExpertIntroduce(this.getPclassOtherProperties().getExpertIntroduce());
               // pclassInfo.setHospitalName(this.getPclassOtherProperties().getHospitalName());
               // pclassInfo.setHospitalCode(this.getPclassOtherProperties().getHospitalCode());
              //  pclassInfo.setInvitedFamilyCount(this.getPclassOtherProperties().getInvitedFamilyCount());
               // pclassInfo.setIsGodeepCity(this.getPclassOtherProperties().isGodeepCity());
              //  pclassInfo.setIsOnlineActivity(this.getPclassOtherProperties().isOnlineActivity());
               // pclassInfo.setPnaEmployeeNumber(this.getPclassOtherProperties().getPnaEmployeeNumber());
            pclassInfo.setPclassType(this.getConsumerActivityType());
            pclassInfo.setPclassType2(this.getActivityType());
            pclassInfo.setPclassProperty(MJT_SUPPLIER);
            pclassInfo.setLivestreamPlatform(livestreamPlatform);
            pclassInfo.setCreatedByUser(ADMIN_USER);
            pclassInfo.setUpdatedByUser(ADMIN_USER);
            pclassInfo.setCreator(operator);
            pclassInfo.setUpdater(operator);
               // pclassInfo.setPclassProperty(this.getPclassOtherProperties().getPclassProperty());
               // pclassInfo.setBusinessReason(this.getPclassOtherProperties().getBusinessReason());
               // pclassInfo.setOtherCourseName(this.getPclassOtherProperties().getOtherCourseName());
               // pclassInfo.setOnlineChannel(this.getPclassOtherProperties().getOnlineChannel());
                //pclassInfo.setLastModifyTime(StringUtils.isBlank(this.getPclassOtherProperties().getLastModifyTime())?null:DateUtils.parseDate(this.getPclassOtherProperties().getLastModifyTime()));
              //  pclassInfo.setUploadTime(StringUtils.isBlank(this.getPclassOtherProperties().getUploadTime())?null:DateUtils.parseDate(this.getPclassOtherProperties().getUploadTime()));
            return pclassInfo;
        }
    }

    @Data
    @NoArgsConstructor
    @ToString
    public static class PclassProperties {
        @ApiModelProperty(value = "妈妈班ID", required = true)
        private String pclassId;
        @ApiModelProperty(value = "活动属性")
        private String pclassProperty;
        @ApiModelProperty(value = "活动负责人员工名称", required = true)
        private String director;
        @ApiModelProperty(value = "活动负责人员工号码", required = true)
        private String directorMobile;
        @ApiModelProperty(value = "活动负责人员工编号", required = true)
        private String directorEmployeeNum;
        @ApiModelProperty(value = "专家姓名")
        private String expertName;
        @ApiModelProperty(value = "专家介绍")
        private String expertIntroduce;
        @ApiModelProperty(value = "咨询热线")
        private String hotline;
        @ApiModelProperty(value = "妈妈班类型", required = true)
        private String pclassType;
        @ApiModelProperty(value = "活动形式")
        private String pclassType2;
        @ApiModelProperty(value = "课程名称", required = true)
        private String courseName;
        @ApiModelProperty(value = "其他课程名称")
        private String otherCourseName;
        @ApiModelProperty(value = "总计划家庭数")
        private Integer invitedFamilyCount;
        @ApiModelProperty(value = "线上渠道")
        private String onlineChannel;
        @ApiModelProperty(value = "医院编码")
        private String hospitalCode;
        @ApiModelProperty(value = "医院名称")
        private String hospitalName;
        @ApiModelProperty(value = "是否为Go deep城市")
        @JsonProperty("isGodeepCity")
        private boolean isGodeepCity;
        @ApiModelProperty(value = "业务理由")
        private String businessReason;
        @ApiModelProperty(value = "是否线上活动")
        @JsonProperty("isOnlineActivity")
        private boolean isOnlineActivity;
        @ApiModelProperty(value = "操作类型")
        private String actionType;
        @ApiModelProperty(value = "PNA员工编码")
        private String pnaEmployeeNumber;
        @ApiModelProperty(value = "上次修改日期,时间格式:yyyy-MM-dd HH:mm:ss")
        private String lastModifyTime;
        @ApiModelProperty(value = "数据上传时间,时间格式:yyyy-MM-dd HH:mm:ss")
        private String uploadTime;

    }
}
