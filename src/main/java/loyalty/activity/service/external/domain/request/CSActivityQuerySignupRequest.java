package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CSActivityQuerySignupRequest {
    @JsonProperty("activity_code")
    private String activityCode;
    @JsonProperty("query_startdate")
    private String queryStartDate;
    @JsonProperty("query_enddate")
    private String queryEndDate;
    @JsonProperty("next_activity_id")
    private String nextActivityId;
    @JsonProperty("page_num")
    private Integer pageNum;
}
