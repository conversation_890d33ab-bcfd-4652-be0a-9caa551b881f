package loyalty.activity.service.external.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import loyalty.activity.service.external.db.model.SignupDataPO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class CSActivityQuerySignupResponse {
    private Integer total;
    private Integer count;
    @JsonProperty("next_activity_id")
    private String nextActivityId;
    @JsonProperty("data")
    private List<SignupDataResponse> signupData;

    public CSActivityQuerySignupResponse(Integer total, Integer count, String nextActivityId, List<SignupDataPO> signupData) {
        this.total = total;
        this.count = count;
        this.nextActivityId = nextActivityId;
        if (CollectionUtils.isNotEmpty(signupData)) {
            List<SignupDataResponse> signupDataResponse = new ArrayList<>();
            for (SignupDataPO signupDatum : signupData) {
                SignupDataResponse response = new SignupDataResponse();
                response.setActivityId(signupDatum.getActivityId());
                response.setSignupNum(signupDatum.getSignupNum());
                response.setActivityCode(signupDatum.getActivityCode());
                signupDataResponse.add(response);
            }
            this.signupData = signupDataResponse;
        }
    }

    @Data
    public static class SignupDataResponse {
        @JsonProperty("activity_code")
        private String activityCode;
        @JsonProperty("signup_num")
        private Integer signupNum;
        @JsonIgnore
        private String activityId;
    }
}
