package loyalty.activity.service.external.domain.response;

import lombok.Data;

@Data
public class InsidePClassInternalResponse {
    private final static String SUCCESS = "true";
    private final static String SUCCESS_MESSAGE = "操作成功";
    private final static String FAILED = "false";
    private final static String FAILED_MESSAGE = "操作失败";
    private static String source;

    private String status;
    private String message;

    public InsidePClassInternalResponse(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public static InsidePClassInternalResponse success() {
        return new InsidePClassInternalResponse(SUCCESS, SUCCESS_MESSAGE);
    }

    public static InsidePClassInternalResponse fail() {
        return new InsidePClassInternalResponse(FAILED, FAILED_MESSAGE);
    }

    public InsidePClassInternalResponse withMessage(String message) {
        this.message = message;
        return this;
    }
}
