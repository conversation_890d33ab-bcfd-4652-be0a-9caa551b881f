package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CBYQrcodeRequest {

    @ApiModelProperty(value = "查询code【课程码/调研码】", required = true)
    @NotNull(message = "查询code不能为空【课程码/调研码】")
    @JsonProperty("queryCode")
    private String queryCode;


    @ApiModelProperty(value = "查询类型【院内：INSIDE_PCLASS，调研：RESEARCH】", required = true)
    @NotNull(message = "查询类型不能为空")
    @JsonProperty("queryType")
    private String queryType;
}
