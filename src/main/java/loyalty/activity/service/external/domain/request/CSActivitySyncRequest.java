package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CSActivitySyncRequest {

    private List<ActivityInfo> activityList;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ActivityInfo {
        @JsonProperty("activity_code")
        private String activityCode;
        @JsonProperty("topic")
        private String topic;
        @JsonProperty("activity_type")
        private String activityType;
        @JsonProperty("store_code")
        private String storeCode;
        @JsonProperty("event_date")
        private String eventDate;
        @JsonProperty("owner_id")
        private String ownerId;
        @JsonProperty("owner_name")
        private String ownerName;
        @JsonProperty("province")
        private String province;
        @JsonProperty("city")
        private String city;
        @JsonProperty("address")
        private String address;
        @JsonProperty("longitude")
        private String longitude;
        @JsonProperty("latitude")
        private String latitude;
        @JsonProperty("limit_count")
        private Integer limitCount;
        @JsonIgnore
        private Boolean isCorrectData = true;
    }
}
