package loyalty.activity.service.external.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ToString
public class PraiseMomQuickRedeemQueryRequest {
    //活动参与信息
    @NotNull(message = "手机号不能为空")
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("驰骛活动id")
    @NotNull(message = "手机号不能为空")
    private String campaignId;
    @ApiModelProperty("课程码")
    @NotNull(message = "课程码不能为空")
    private String activityCode;

}
