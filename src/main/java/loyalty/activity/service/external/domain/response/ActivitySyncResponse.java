package loyalty.activity.service.external.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class ActivitySyncResponse {
    @ApiModelProperty("失败记录清单")
    private List<ErrorData> errorDataList;
    @ApiModelProperty("失败数")
    private Integer failSize;
    @JsonProperty("success_activity_codes")
    @ApiModelProperty("成功课程编码清单")
    private List<String> successActivityCodes;

    @Data
    public static class ErrorData{
        @ApiModelProperty("课程编码")
        private String activityCode;
        @JsonIgnore
        private List<String> errorDescList;
        @ApiModelProperty("失败信息")
        private String errorDesc;
    }

    public ActivitySyncResponse() {
        this.errorDataList = new ArrayList<>();
        this.successActivityCodes = new ArrayList<>();
    }
}
