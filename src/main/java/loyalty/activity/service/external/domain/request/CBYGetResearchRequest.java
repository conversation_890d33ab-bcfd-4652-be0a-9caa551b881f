package loyalty.activity.service.external.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import loyalty.activity.service.common.dto.PageRequest;
import loyalty.activity.service.common.dto.SearchCriteria;
import org.apache.commons.lang3.StringUtils;

import static loyalty.activity.service.common.constants.AppConstants.DEFAULT_PAGE_INDEX;
import static loyalty.activity.service.common.constants.AppConstants.DEFAULT_PAGE_SIZE;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class CBYGetResearchRequest extends PageRequest {
    @ApiModelProperty(value = "查询类型：RESEARCH", required = true)
    private String queryType;

    @ApiModelProperty(value = "开始时间：2023-04-06")
    private String startTime;

    @ApiModelProperty(value = "结束时间：2023-04-07")
    private String endTime;

    public SearchCriteria adaptToSearchCriteria(){
        SearchCriteria searchCriteria=new SearchCriteria();
        searchCriteria.setLimit(this.getLimit()==null?DEFAULT_PAGE_SIZE:this.getLimit());
        searchCriteria.setPage(this.getPage()==null?DEFAULT_PAGE_INDEX:this.getPage());
        SearchCriteria.Criteria criteria=null;
        if(StringUtils.isNotBlank(startTime)||StringUtils.isNotBlank(endTime)){
            criteria =new SearchCriteria.Criteria();
            criteria.setKey("classTime");
        }
        if(StringUtils.isNotBlank(startTime)){
            criteria.setMinValue(startTime);
        }
        if(StringUtils.isNotBlank(endTime)){
            criteria.setMaxValue(endTime);
        }
        if(criteria!=null) {
            searchCriteria.getCriterias().add(criteria);
        }
        return searchCriteria;
    }
}
