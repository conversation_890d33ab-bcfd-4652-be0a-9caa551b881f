package loyalty.activity.service.external.db.mapper;

import loyalty.activity.service.external.db.model.SignupDataPO;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivitySignInRemindDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CSActivityMapper {

    @Select("<script>" +
            "SELECT classes_code activityCode,COUNT(t3.id) signupNum,t2.id activityId " +
            "FROM pclass_info t1 " +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id " +
            "LEFT JOIN activity_user_signin t3 ON t2.id = t3.activity_id " +
            "<where>" +
            "t2.channel = #{channel} " +
            "<if test = \"activityCode != null and activityCode !='' \"> " +
            "  AND t1.classes_code = #{activityCode} " +
            "</if>" +
            "<if test = \"queryStartDate != null and queryStartDate != ''\"> " +
            "  AND DATE_FORMAT(t2.start_time,'%Y-%m-%d') &gt;= #{queryStartDate} " +
            "</if>" +
            "<if test = \"queryEndDate != null and queryEndDate != ''\"> " +
            "  AND DATE_FORMAT(t2.end_time,'%Y-%m-%d') &lt;= #{queryEndDate} " +
            "</if>" +
            "<if test = \"nextActivityId != null and nextActivityId != ''\"> " +
            "  AND t2.id &gt;= #{nextActivityId} " +
            "</if>" +
            "</where>" +
            "GROUP BY t2.id " +
            "ORDER BY t2.id " +
            "limit #{pageNum} " +
            "</script>")
    List<SignupDataPO> queryCSActivitySignupData(@Param("activityCode") String activityCode,
                                                 @Param("queryStartDate") String queryStartDate,
                                                 @Param("queryEndDate") String queryEndDate,
                                                 @Param("nextActivityId") String nextActivityId,
                                                 @Param("pageNum") int pageNum,
                                                 @Param("channel") String channel);

    @Select("SELECT COUNT(1) FROM activity WHERE channel = 'CS'")
    Integer queryCSActivityTotal();


    @Select({"<script>SELECT t1.id,\n    t2.classes_code,   t2.course_name,\n       t1.openid,\n       t3.start_time,\n       t4.address,\n       t4.place,\n       t6.template_data\nFROM pclass_subscribe_msg_info t1\n       INNER JOIN pclass_info t2 ON t1.pclass_code = t2.classes_code\n       INNER JOIN activity t3 ON t2.activity_id = t3.id\n       LEFT JOIN activity_address t4 ON t3.id = t4.activity_id\n       INNER JOIN wxmpp_subscribe_msg_info t5 ON t1.wxmpp_subscribe_msg_info_id = t5.id\n       INNER JOIN wxmpp_template_info t6 ON t6.pri_tmpl_id = t5.template_id\n  AND t6.is_enabled = TRUE\nWHERE t1.pclass_code = #{pclassCode}\n  AND t1.cellphone = #{cellphone}\n  AND t1.subscribe_type = #{subscribeType}\n  AND t1.is_send = FALSE  AND t5.subscribe_status = 'accept' order by t1.create_time desc limit 1</script>"})
    @Results({@Result(
            column = "id",
            property = "id"
    ), @Result(
            column = "course_name",
            property = "courseName"
    ), @Result(
            column = "openid",
            property = "openid"
    ), @Result(
            column = "start_time",
            property = "startTime"
    ), @Result(
            column = "address",
            property = "address"
    ), @Result(
            column = "place",
            property = "place"
    ), @Result(
            column = "template_data",
            property = "templateData"
    ), @Result(
            column = "classes_code",
            property = "classesCode"
    )})
    ActivitySignInRemindDto getRemindInfoByCellphoneAndCode(@Param("cellphone") String var1, @Param("pclassCode") String var2,@Param("subscribeType") String subscribeType);
}
