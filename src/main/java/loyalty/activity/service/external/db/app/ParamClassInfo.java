package loyalty.activity.service.external.db.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/31 上午 10:20
 * @describe
 */
@Data
public class ParamClassInfo {
    @ApiModelProperty(value = "操作类型")
    private String actionType;
    @ApiModelProperty(value = "活动形式1")
    private String activityType;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "业务理由")
    private String businessReason;
    @ApiModelProperty(value = "城市")
    private String city;
    //eventNumber
    @ApiModelProperty(value = "课程编码")
    private String classesCode;
    //courseTopics
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "活动负责人")
    private String director;
    @ApiModelProperty(value = "活动负责人员工编号")
    private String directorEmployeeNum;
    @ApiModelProperty(value = "活动负责人号码")
    private String directorMobile;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "讲师介绍")
    private String expertIntroduce;
    @ApiModelProperty(value = "课程讲师")
    private String expertName;
    //CouponAddress1
    @ApiModelProperty(value = "优惠券兑换地址1")
    private String firstCouponAddress;
    //CouponContents1
    @ApiModelProperty(value = "优惠券内容1")
    private String firstCouponContent;
    //CouponValidity1
    @ApiModelProperty(value = "优惠券有效期1")
    private String firstCouponExpirationDate;
    //CouponNumber1
    @ApiModelProperty(value = "优惠券数量1")
    private Integer firstCouponNum;
    //CouponType1
    @ApiModelProperty(value = "优惠券类型1")
    private String firstCouponType;
    @ApiModelProperty(value = "医院编码")
    private String hospitalCode;
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;
    @ApiModelProperty(value = "咨询热线")
    private String hotline;
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "总家庭计划数")
    private Integer invitedFamilyCount;
    @ApiModelProperty(value = "删除标识")
    private Boolean isDeleted;
    @ApiModelProperty(value = "可用标识")
    private Boolean isEnabled;
    @ApiModelProperty(value = "是否为Godeep城市")
    private Boolean isGodeepCity;
    @ApiModelProperty(value = "是否线上活动")
    private Boolean isOnlineActivity;
    @ApiModelProperty(value = "是否发放优惠券")
    private Boolean isSendCoupon;
    @ApiModelProperty(value = "上次修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "所在纬度")
    private BigDecimal latitude;
    //limitNumber
    @ApiModelProperty(value = "限制人数")
    private Integer limitCount;
    @ApiModelProperty(value = "所在经度")
    private BigDecimal longitude;
    @ApiModelProperty(value = "nc门店编码")
    private String ncCode;
    @ApiModelProperty(value = "线上渠道")
    private String onlineChannel;
    @ApiModelProperty(value = "其它课程名称")
    private String otherCourseName;
    //employeeNum
    @ApiModelProperty(value = "活动申请人员工编号")
    private String ownerEmployeeNum;
    //applicantMobile
    @ApiModelProperty(value = "活动申请人号码")
    private String ownerMobile;
    @ApiModelProperty(value = "活动申请人")
    private String ownerName;
    @ApiModelProperty(value = "pclass_id")
    private String pclassId;
    //property
    @ApiModelProperty(value = "活动属性")
    private String pclassProperty;
    @ApiModelProperty(value = "妈妈班类型")
    private String pclassType;
    @ApiModelProperty(value = "活动形式")
    private String pclassType2;
    @ApiModelProperty(value = "举办地点")
    private String place;
    @ApiModelProperty(value = "pna员工编号")
    private String pnaEmployeeNumber;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "课程二维码链接")
    private String qrcodeUrl;
    @ApiModelProperty(value = "备注")
    private String remark;
    //CouponAddress2
    @ApiModelProperty(value = "优惠券兑换地址2")
    private String secCouponAddress;
    //CouponContents2
    @ApiModelProperty(value = "优惠券内容2")
    private String secCouponContent;
    //CouponValidity2
    @ApiModelProperty(value = "优惠券有效期2")
    private String secCouponExpirationDate;
    //CouponNumber2
    @ApiModelProperty(value = "优惠券数量2")
    private Integer secCouponNum;
    //CouponType2
    @ApiModelProperty(value = "优惠券类型2")
    private String secCouponType;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    //EventStatus
    @ApiModelProperty(value = "活动状态")
    private String status;
    @ApiModelProperty(value = "问卷模板")
    private String surveyTemplate;
    @ApiModelProperty(value = "主题")
    private String topic;
    @ApiModelProperty(value = "上传状态")
    private String uploadStatus;
    @ApiModelProperty(value = "上传时间")
    private String uploadTime;
    private String courseTopics;

}
