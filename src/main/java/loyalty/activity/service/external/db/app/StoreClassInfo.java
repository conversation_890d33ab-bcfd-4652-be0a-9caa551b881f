package loyalty.activity.service.external.db.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StoreClassInfo {
    private String eventNumber;

    private String eventAddress;

    private String courseTopics;

    private String storeId;

    private String pClassType1;

    private String pClassType2;

    private String startTime;

    private String endTime;
}
