package loyalty.activity.service.external.db.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StoreClassResponse {
    @JsonProperty("err_code")
    private String errCode;

    @JsonProperty("err_msg")
    private String errMsg;

    private List<StoreClassInfo> body;

}
