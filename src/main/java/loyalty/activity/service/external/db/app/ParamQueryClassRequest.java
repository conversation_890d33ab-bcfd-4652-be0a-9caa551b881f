package loyalty.activity.service.external.db.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import loyalty.activity.service.common.dto.PageRequest;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/31 上午 10:16
 * @describe
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ParamQueryClassRequest extends PageRequest {
    private String classesCode;
    private Date endTime;
    private Date startTime;
    private String status;
}
