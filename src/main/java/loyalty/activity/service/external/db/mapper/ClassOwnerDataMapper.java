package loyalty.activity.service.external.db.mapper;

import loyalty.activity.service.external.db.model.ClassOwnerDataInfo;
import loyalty.activity.service.external.db.model.PnStaffDataInfo;
import org.apache.ibatis.annotations.*;


@Mapper
public interface ClassOwnerDataMapper {

    @Select("SELECT " +
            " class.classes_code, " +
            " class.owner_employee_num, " +
            " CASE " +
            " WHEN pna.pnec_id IS NOT NULL  " +
            " AND REPLACE ( pna.pnec_id, 'P', '' )= REPLACE ( class.owner_employee_num, 'P', '' ) THEN " +
            " REPLACE ( class.owner_employee_num, 'P', '' ) ELSE ''  " +
            " END pnec_id, " +
            " CASE " +
            " WHEN pna.pna_id IS NOT NULL  " +
            " AND REPLACE ( pna.pna_id, 'P', '' )= REPLACE ( class.owner_employee_num, 'P', '' ) THEN " +
            " REPLACE ( class.owner_employee_num, 'P', '' ) ELSE ''  " +
            " END pna_id  " +
            " FROM " +
            " pclass_info class " +
            " LEFT JOIN sync_pna_info pna ON ( " +
            " REPLACE ( pna.pna_id, 'P', '' )= REPLACE ( class.owner_employee_num, 'P', '' )  " +
            " OR REPLACE ( pna.pnec_id, 'P', '' )= REPLACE ( class.owner_employee_num, 'P', '' ))  " +
            " WHERE " +
            " class.classes_code = #{classesCode} " +
            " LIMIT 1")
    @Results(value = {
            @Result(property = "classesCode", column = "classes_code"),
            @Result(property = "ownerEmployeeNum", column = "owner_employee_num"),
            @Result(property = "pnecId", column = "pnec_id"),
            @Result(property = "pnaId", column = "pna_id")
    })
    ClassOwnerDataInfo queryClassRecruitByClassCode(@Param("classesCode") String classesCode);


    @Select("select       " +
            " class.classes_code,  " +
            " class.owner_employee_num,  " +
            " pnstaff.staff_type, " +
            " pnstaff.staff_code " +
            " from pclass_info class  " +
            " inner join sync_pnstaff_info pnstaff on REPLACE ( class.owner_employee_num, 'P', '' ) =  REPLACE ( pnstaff.staff_code , 'P', '' ) " +
            " where class.classes_code = #{classesCode} " +
            " limit 1 ")
    @Results(value = {
            @Result(property = "classesCode", column = "classes_code"),
            @Result(property = "ownerEmployeeNum", column = "owner_employee_num"),
            @Result(property = "staffType", column = "staff_type"),
            @Result(property = "staffCode", column = "staff_code")
    })
    PnStaffDataInfo queryClassRecruitV2ByClassCode(@Param("classesCode") String classesCode);
}
