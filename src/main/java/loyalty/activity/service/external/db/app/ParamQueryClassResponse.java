package loyalty.activity.service.external.db.app;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import loyalty.activity.service.common.dto.PageResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/31 上午 10:19
 * @describe
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ParamQueryClassResponse {
    private PageResponse<ParamClassInfo> body;
    @JsonProperty("err_code")
    private String errCode;
    @JsonProperty("err_msg")
    private String errMsg;

}
