package loyalty.activity.service.external.db.mapper;

import loyalty.activity.service.external.entity.PClassExt;
import org.apache.ibatis.annotations.*;

@Mapper
public interface PClassExtMapper {
    
    /**
     * 根据活动编码查询活动扩展信息
     *
     * @param classesCode 活动编码
     * @return 活动扩展信息
     */
    @Select("SELECT classes_code, registration_begin_date, registration_end_date, status, page_title, " +
            "logo_img, cover_img, top_img, detail_img, restrict_attendance, poster_img, poster_qr_img, " +
            "creator, updater, create_time, update_time " +
            "FROM pclass_ext WHERE classes_code = #{classesCode}")
    @Results({
        @Result(property = "classesCode", column = "classes_code"),
        @Result(property = "registrationBeginDate", column = "registration_begin_date"),
        @Result(property = "registrationEndDate", column = "registration_end_date"),
        @Result(property = "pageTitle", column = "page_title"),
        @Result(property = "logoImg", column = "logo_img"),
        @Result(property = "coverImg", column = "cover_img"),
        @Result(property = "topImg", column = "top_img"),
        @Result(property = "detailImg", column = "detail_img"),
        @Result(property = "restrictAttendance", column = "restrict_attendance"),
        @Result(property = "posterImg", column = "poster_img"),
        @Result(property = "posterQrImg", column = "poster_qr_img"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    PClassExt selectByClassesCode(@Param("classesCode") String classesCode);

    /**
     * 插入或更新活动扩展信息
     * 如果记录存在则更新，不存在则插入
     *
     * @param record 活动扩展信息
     * @return 影响行数
     */
    @Insert("INSERT INTO pclass_ext (classes_code, registration_begin_date, registration_end_date, " +
            "status, page_title, logo_img, cover_img, top_img, detail_img, restrict_attendance, poster_img, " +
            "poster_qr_img, creator, updater) " +
            "VALUES (#{classesCode}, #{registrationBeginDate}, #{registrationEndDate}, " +
            "#{status}, #{pageTitle}, #{logoImg}, #{coverImg}, #{topImg}, #{detailImg}, #{restrictAttendance}, " +
            "#{posterImg}, #{posterQrImg}, #{creator}, #{updater}) " +
            "ON DUPLICATE KEY UPDATE " +
            "registration_begin_date = VALUES(registration_begin_date), " +
            "registration_end_date = VALUES(registration_end_date), " +
            "status = VALUES(status), " +
            "page_title = VALUES(page_title), " +
            "logo_img = VALUES(logo_img), " +
            "cover_img = VALUES(cover_img), " +
            "top_img = VALUES(top_img), " +
            "detail_img = VALUES(detail_img), " +
            "restrict_attendance = VALUES(restrict_attendance), " +
            "poster_img = VALUES(poster_img), " +
            "poster_qr_img = VALUES(poster_qr_img), " +
            "updater = VALUES(updater)")
    int insertOrUpdate(PClassExt record);
} 