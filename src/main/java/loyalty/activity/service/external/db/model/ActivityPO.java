package loyalty.activity.service.external.db.model;

import lombok.Data;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityAddress;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityOwnerRel;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;

@Data
public class ActivityPO {
    private Activity activity;
    private ActivityAddress activityAddress;
    private PclassInfo pclassInfo;
    private ActivityOwnerRel activityOwnerRel;
    private String operateType;
    private Boolean isCorrectData;
}
