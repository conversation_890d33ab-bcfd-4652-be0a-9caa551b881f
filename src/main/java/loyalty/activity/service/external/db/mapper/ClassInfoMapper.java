package loyalty.activity.service.external.db.mapper;

import loyalty.activity.service.external.db.app.ParamClassInfo;
import loyalty.activity.service.external.db.app.ParamQueryClassRequest;
import loyalty.activity.service.external.db.app.StoreClassInfo;
import loyalty.activity.service.external.db.model.ActivityClassInfo;
import loyalty.activity.service.external.db.model.CeRegisterLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ClassInfoMapper {

    /*todo:to howard team: ，此处的根本问题在于表里的start_time/end_time的格式是字符串才会产生前面的comment
      1. The query uses LIKE operator on the nc_code column with wildcard on both sides, which can be inefficient. If possible, use a more specific operator to optimize the query.
      2. The query fetches all columns from the pclass_info table, even though only a few of them are needed. Fetching only the required columns can improve the performance.
      3. The query is using date functions on the start_time and end_time columns which can prevent full use of indexes. If possible, modify the query to avoid using functions, or create indexes that consider the use of these functions.
      4. The select statement concatenates empty strings to date columns, which is unnecessary.
      5. Instead of using a CASE statement to determine the value for a column based on a condition, use separate SQL statements.
      6. The property in the @Result annotation for the event_address column is incorrect, which can cause a NullPointerException.

      To-do list:
      1. Use a more specific operator than LIKE to query the nc_code column.
      2. Fetch only the necessary columns from the pclass_info table.
      3. Modify the query to avoid using date functions or create indexes that can optimize date-related queries.
      4. Remove the unnecessary string concatenation in the select statement.
      5. Use separate SQL statements instead of CASE statements to populate columns with different values based on conditions.
      6. Update the property in the @Result annotation for the event_address column to match the column name in the query. */
    @Select(" select CONCAT(start_time,'' )start_time, CONCAT(end_time,'') end_time,  classes_code event_number, address, " +
            " case when channel='CS' then activity.topic else  course_name end course_topics," +
            " case when channel='CS' then 'CS' else pclass_type end pclass_type1, " +
            " case when channel='CS' then activity_type else pclass_type end pclass_type2  " +
            " from pclass_info " +
            " inner join activity on pclass_info.activity_id = activity.id " +
            " left join activity_address on activity_address.activity_id = activity.id " +
            " where nc_code like '%${storeId}%' " +
            " and DATE_FORMAT(start_time,'%Y-%m-%d')  <= CURDATE() and  DATE_FORMAT(end_time,'%Y-%m-%d') >= CURDATE()  " +
            " and ( pclass_info.status is null or pclass_info.status != '已取消') ")
    @Results({
            @Result(column = "event_number", property = "eventNumber"),
            @Result(column = "course_topics", property = "courseTopics"),
            @Result(column = "event_address", property = "eventAddress"),
            @Result(column = "pclass_type1", property = "pClassType1"),
            @Result(column = "pclass_type2", property = "pClassType2"),
        @Result(column = "start_time", property = "startTime"),
        @Result(column = "end_time", property = "endTime")
    }
    )
    List<StoreClassInfo> getClassesByStoreId(@Param("storeId") String storeId);

    @Insert("INSERT INTO ce_register_log(id,mobile,`type`,code,pnecid,pnaid,pnrid,ce_request,request_crm," +
        "crm_response,is_suc,regsub_channel,exception_info)" +
        " VALUES(#{id},#{mobile},#{type},#{code},#{pnecid},#{pnaid},#{pnrid},#{ceRequest},#{requestCrm}," +
        "#{crmResponse},#{isSuc},#{regsubChannel},#{exceptionInfo})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertCeRegisterLogEntity(CeRegisterLog ceRegisterLog);

    /*todo:to howard team:
2. The use of `LEFT JOIN`s and `INNER JOIN`s could potentially lead to null values if not handled properly.
3. The use of `CONCAT` in the SQL query is not recommended and could potentially lead to SQL injection if `classesCode` contains malicious values.
4. The usage of `<script>` tag should be avoided wherever possible, as it can pose a security risk.
5. There is no check to validate that the `id` fields of tables `activity` and `pclass_info` have indices, which could impact query performance.
6. `startTime` and `endTime` variables could be validated to ensure that `endTime` is greater than `startTime`.
8. Instead of `date(now())`, it is better to use `CURDATE()` SQL function, which can improve performance.

To-Do List:
2. Use `LEFT OUTER JOIN` instead of `LEFT JOIN` to avoid null values.
3. Use parameterized queries to prevent SQL injection.
4. Avoid <script> tag where possible and replace with @org.apache.ibatis.annotations.Param.
5. Check the indices of id fields in the activity and pclass_info tables.
6. Add validation checks for `startTime` and `endTime`.
8. Replace `date(now())` with `CURDATE()`. */
    @Select("<script>" +
        "SELECT t1.topic,\n" +
        "       t1.remark,\n" +
        "       t1.start_time,\n" +
        "       t1.end_time,\n" +
        "       t1.is_enabled,\n" +
        "       t2.province,\n" +
        "       t2.city,\n" +
        "       t2.address,\n" +
        "       t2.place,\n" +
        "       t2.longitude,\n" +
        "       t2.latitude,\n" +
        "       t3.id,\n" +
        "       t3.pclass_id,\n" +
        "       t3.classes_code,\n" +
        "       t3.owner_name,\n" +
        "       t3.owner_mobile,\n" +
        "       t3.owner_employee_num,\n" +
        "       t3.director,\n" +
        "       t3.director_mobile,\n" +
        "       t3.director_exmployee_num,\n" +
        "       t3.expert_name,\n" +
        "       t3.expert_introduce,\n" +
        "       t3.hotline,\n" +
        "       t3.qrcode_url,\n" +
        "       t3.limit_count,\n" +
        "       t3.survey_template,\n" +
        "       case when t1.channel='CS' then 'CS' else t3.pclass_type end pclass_type,\n" +
        "       case when t1.channel='CS' then t1.topic else t3.course_name end course_topics,\n" +
        "       t3.pclass_property,\n" +
        "       case when t1.channel='CS' then t3.activity_type else t3.pclass_type end pclass_type2,\n" +
        "       t3.course_name,\n" +
        "       t3.other_course_name,\n" +
        "       t3.invited_family_count,\n" +
        "       t3.nc_code,\n" +
        "       t3.last_modify_time,\n" +
        "       t3.upload_time,\n" +
        "       t3.upload_status,\n" +
        "       t3.status,\n" +
        "       t3.online_channel,\n" +
        "       t3.hospital_code,\n" +
        "       t3.hospital_name,\n" +
        "       t3.is_godeep_city,\n" +
        "       t3.is_send_coupon,\n" +
        "       t3.business_reason,\n" +
        "       t3.is_online_activity,\n" +
        "       t3.activity_type,\n" +
        "       t3.action_type,\n" +
        "       t3.pna_employee_number,\n" +
        "       t3.is_deleted,\n" +
        "       first_coupon_type,\n" +
        "       first_coupon_content,\n" +
        "       first_coupon_address,\n" +
        "       first_coupon_num,\n" +
        "       first_coupon_expiration_date,\n" +
        "       sec_coupon_type,\n" +
        "       sec_coupon_content,\n" +
        "       sec_coupon_address,\n" +
        "       sec_coupon_num,\n" +
        "       sec_coupon_expiration_date\n" +
        "FROM activity t1\n" +
        "       LEFT JOIN activity_address t2 ON t2.activity_id = t1.id\n" +
        "       INNER JOIN pclass_info t3 ON t3.activity_id = t1.id\n" +
        "       LEFT JOIN sync_pclass_coupon_info t4 ON t3.classes_code = t4.pclass_code\n" +
        "<where>\n" +
        "   <if test=\"classesCode != null and classesCode != ''\">\n" +
        "       AND t3.classes_code LIKE CONCAT('%', #{classesCode}, '%')\n" +
        "   </if>\n" +
        "   <if test=\"status != null and status != ''\">\n" +
        "       AND t3.status = #{status}\n" +
        "   </if>\n" +
        "   <choose>\n" +
        "       <when test=\"endTime != null or startTime != null\">\n" +
        "           <if test=\"endTime != null\">\n" +
        "               AND t1.start_time &lt;= #{endTime}\n" +
        "           </if>\n" +
        "           <if test=\"startTime != null\">\n" +
        "               AND t1.start_time &gt;= #{startTime}\n" +
        "           </if>\n" +
        "       </when>\n" +
        "       <otherwise>\n" +
        "           AND date(t1.start_time) = date(now())\n" +
        "       </otherwise>\n" +
        "   </choose>\n" +
        "</where>\n" +
        "ORDER BY t3.id" +
        "</script>")
    @Results(value = {
        @Result(property = "topic", column = "topic"),
        @Result(property = "remark", column = "remark"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "endTime", column = "end_time"),
        @Result(property = "province", column = "province"),
        @Result(property = "city", column = "city"),
        @Result(property = "address", column = "address"),
        @Result(property = "place", column = "place"),
        @Result(property = "longitude", column = "longitude"),
        @Result(property = "latitude", column = "latitude"),
        @Result(property = "id", column = "id"),
        @Result(property = "pclassId", column = "pclass_id"),
        @Result(property = "classesCode", column = "classes_code"),
        @Result(property = "ownerName", column = "owner_name"),
        @Result(property = "ownerMobile", column = "owner_mobile"),
        @Result(property = "ownerEmployeeNum", column = "owner_employee_num"),
        @Result(property = "director", column = "director"),
        @Result(property = "directorMobile", column = "director_mobile"),
        @Result(property = "directorEmployeeNum", column = "director_exmployee_num"),
        @Result(property = "isEnabled", column = "is_enabled"),
        @Result(property = "expertName", column = "expert_name"),
        @Result(property = "expertIntroduce", column = "expert_introduce"),
        @Result(property = "hotline", column = "hotline"),
        @Result(property = "qrcodeUrl", column = "qrcode_url"),
        @Result(property = "limitCount", column = "limit_count"),
        @Result(property = "surveyTemplate", column = "survey_template"),
        @Result(property = "pclassType", column = "pclass_type"),
        @Result(property = "pclassProperty", column = "pclass_property"),
        @Result(property = "pclassType2", column = "pclass_type2"),
        @Result(property = "courseName", column = "course_name"),
        @Result(property = "otherCourseName", column = "other_course_name"),
        @Result(property = "invitedFamilyCount", column = "invited_family_count"),
        @Result(property = "ncCode", column = "nc_code"),
        @Result(property = "lastModifyTime", column = "last_modify_time"),
        @Result(property = "uploadTime", column = "upload_time"),
        @Result(property = "uploadStatus", column = "upload_status"),
        @Result(property = "status", column = "status"),
        @Result(property = "onlineChannel", column = "online_channel"),
        @Result(property = "hospitalCode", column = "hospital_code"),
        @Result(property = "hospitalName", column = "hospital_name"),
        @Result(property = "isGodeepCity", column = "is_godeep_city"),
        @Result(property = "isSendCoupon", column = "is_send_coupon"),
        @Result(property = "businessReason", column = "business_reason"),
        @Result(property = "isOnlineActivity", column = "is_online_activity"),
        @Result(property = "activityType", column = "activity_type"),
        @Result(property = "actionType", column = "action_type"),
        @Result(property = "pnaEmployeeNumber", column = "pna_employee_number"),
        @Result(property = "isDeleted", column = "is_deleted"),
        @Result(property = "firstCouponType", column = "first_coupon_type"),
        @Result(property = "firstCouponContent", column = "first_coupon_content"),
        @Result(property = "firstCouponAddress", column = "first_coupon_address"),
        @Result(property = "firstCouponNum", column = "first_coupon_num"),
            @Result(property = "firstCouponExpirationDate", column = "first_coupon_expiration_date"),
            @Result(property = "secCouponType", column = "sec_coupon_type"),
            @Result(property = "courseTopics", column = "course_topics"),
            @Result(property = "secCouponContent", column = "sec_coupon_content"),
            @Result(property = "secCouponAddress", column = "sec_coupon_address"),
            @Result(property = "secCouponNum", column = "sec_coupon_num"),
            @Result(property = "secCouponExpirationDate", column = "sec_coupon_expiration_date")
    })
    List<ParamClassInfo> getClassesByParam(ParamQueryClassRequest request);

    @Select("select classes_code, start_time, end_time, qrcode_url,activity_type" +
            " from activity  " +
            " inner join pclass_info on activity.id=pclass_info.activity_id\n" +
            " where activity.channel=#{channel} and classes_code=#{classesCode}")
    @Results(value = {
            @Result(property = "classesCode", column = "classes_code"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "qrcodeUrl", column = "qrcode_url"),
            @Result(property = "activityType", column = "activity_type")
    })
    ActivityClassInfo getClassesByCode(@Param("classesCode") String classesCode, @Param("channel") String channel);

    @Select("select activity_type" +
            " from activity  " +
            " inner join pclass_info on activity.id=pclass_info.activity_id\n" +
            " where classes_code=#{classesCode}")
    String getActivityTypeByCode(@Param("classesCode") String classesCode);

    @Select("select p.classes_code from pclass_info p inner join activity a on p.activity_id=a.id where a.channel='INSIDE_PCLASS' and a.start_time>=now() and qrcode_url is not null order by a.start_time")
    List<String> getInsideClassAfterToday();
}
