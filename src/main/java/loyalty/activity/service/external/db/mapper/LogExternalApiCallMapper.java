package loyalty.activity.service.external.db.mapper;

import loyalty.activity.service.external.entity.LogExternalApiCall;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface LogExternalApiCallMapper {

    @Insert("insert into log_external_api_call (activity_code, req_succeed, req_path, req_body, req_params, result_body, caller) " +
            "values (#{activityCode}, #{reqSucceed}, #{reqPath}, #{reqBody}, #{reqParams}, #{resultBody}, #{caller})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(LogExternalApiCall record);

    @Select("select id, activity_code, req_succeed, req_path, req_body, req_params, result_body, caller, create_time " +
            "from log_external_api_call where id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "activityCode", column = "activity_code"),
            @Result(property = "reqSucceed", column = "req_succeed"),
            @Result(property = "reqPath", column = "req_path"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "reqParams", column = "req_params"),
            @Result(property = "resultBody", column = "result_body"),
            @Result(property = "caller", column = "caller"),
            @Result(property = "createTime", column = "create_time")
    })
    LogExternalApiCall selectByPrimaryKey(Long id);

    @Select("select id, activity_code, req_succeed, req_path, req_body, req_params, result_body, caller, create_time " +
            "from log_external_api_call where activity_code = #{activityCode}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "activityCode", column = "activity_code"),
            @Result(property = "reqSucceed", column = "req_succeed"),
            @Result(property = "reqPath", column = "req_path"),
            @Result(property = "reqBody", column = "req_body"),
            @Result(property = "reqParams", column = "req_params"),
            @Result(property = "resultBody", column = "result_body"),
            @Result(property = "caller", column = "caller"),
            @Result(property = "createTime", column = "create_time")
    })
    List<LogExternalApiCall> selectByActivityCode(@Param("activityCode") String activityCode);

    @Update("<script>" +
            "update log_external_api_call" +
            "<set>" +
            "<if test='activityCode != null'>activity_code = #{activityCode},</if>" +
            "<if test='reqSucceed != null'>req_succeed = #{reqSucceed},</if>" +
            "<if test='reqPath != null'>req_path = #{reqPath},</if>" +
            "<if test='reqBody != null'>req_body = #{reqBody},</if>" +
            "<if test='reqParams != null'>req_params = #{reqParams},</if>" +
            "<if test='resultBody != null'>result_body = #{resultBody},</if>" +
            "<if test='caller != null'>caller = #{caller},</if>" +
            "</set>" +
            "where id = #{id}" +
            "</script>")
    int updateByPrimaryKeySelective(LogExternalApiCall record);

    @Delete("delete from log_external_api_call where id = #{id}")
    int deleteByPrimaryKey(Long id);
} 