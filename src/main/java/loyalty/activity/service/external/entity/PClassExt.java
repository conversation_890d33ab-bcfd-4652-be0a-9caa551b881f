package loyalty.activity.service.external.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class PClassExt {
    /**
     * 活动编码
     */
    private String classesCode;
    
    /**
     * 开始报名时间
     */
    private Date registrationBeginDate;
    
    /**
     * 报名结束时间
     */
    private Date registrationEndDate;
    
    /**
     * 活动状态(0已取消 1执行中)
     */
    private Integer status;
    
    /**
     * 页面标题
     */
    private String pageTitle;

    private String logoImg;
    
    /**
     * 列表缩略图
     */
    private String coverImg;
    
    /**
     * 详情头部背景图
     */
    private String topImg;
    
    /**
     * 活动详情背景图
     */
    private String detailImg;
    
    /**
     * 限制报名人数
     */
    private Integer restrictAttendance;
    
    /**
     * 海报图
     */
    private String posterImg;
    
    /**
     * 吊起海报图
     */
    private String posterQrImg;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 更新者
     */
    private String updater;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 