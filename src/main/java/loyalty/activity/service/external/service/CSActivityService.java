package loyalty.activity.service.external.service;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.enums.CSActivityType;
import loyalty.activity.service.common.utils.DateUtils;
import loyalty.activity.service.error.DataIsNullException;
import loyalty.activity.service.external.domain.request.CSActivitySyncRequest;
import loyalty.activity.service.external.domain.response.ActivitySyncResponse;
import loyalty.activity.service.sharelib.common.MainBaseAdminEntity;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.bo.OperateType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityOwnerRel;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;
import loyalty.activity.service.external.db.mapper.CSActivityMapper;
import loyalty.activity.service.external.db.model.ActivityPO;
import loyalty.activity.service.external.db.model.SignupDataPO;
import loyalty.activity.service.external.domain.request.CSActivityQuerySignupRequest;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.response.CSActivityQuerySignupResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CSActivityService {

    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private ActivityMapper activityMapper;
    @Autowired
    private PclassInfoMapper pclassInfoMapper;
    @Autowired
    private ActivityAddressMapper activityAddressMapper;
    @Autowired
    private ActivityOwnerRelMapper activityOwnerRelMapper;
    @Autowired
    private CSActivityMapper csActivityMapper;

    private static final int pageNum = 20;


    public ActivitySyncResponse sync(CSActivitySyncRequest syncRequest) {
        List<CSActivitySyncRequest.ActivityInfo> activityInfoList = syncRequest.getActivityList();
        ActivitySyncResponse errorResponse = verifyData(activityInfoList);
        if (CollectionUtils.isNotEmpty(activityInfoList)){
            List<ActivityPO> activityPOList = convertToActivityPOList(activityInfoList,errorResponse);
            List<ActivityPO> insertList = activityPOList.stream().filter(s -> OperateType.INSERT.name().equals(s.getOperateType()) && s.getIsCorrectData()).collect(Collectors.toList());
            List<ActivityPO> updateList = activityPOList.stream().filter(s -> OperateType.UPDATE.name().equals(s.getOperateType()) && s.getIsCorrectData()).collect(Collectors.toList());
            insertUpdateCSData(insertList,OperateType.INSERT.name());
            insertUpdateCSData(updateList,OperateType.UPDATE.name());
            if (errorResponse.getErrorDataList()!=null&&errorResponse.getErrorDataList().size()>0){
                errorResponse.setFailSize(activityInfoList.size()-insertList.size()-updateList.size());
                errorResponse.getErrorDataList().forEach(s->s.setErrorDesc(StringUtils.join(s.getErrorDescList(),",")));
                return errorResponse;
            }
        }
        return null;
    }


    public CSActivityQuerySignupResponse querySignup(CSActivityQuerySignupRequest querySignupRequest) {
        if (querySignupRequest.getPageNum()==null || querySignupRequest.getPageNum() == 0){
            querySignupRequest.setPageNum(pageNum);
        }
        List<SignupDataPO> signupData = csActivityMapper.queryCSActivitySignupData(querySignupRequest.getActivityCode(),querySignupRequest.getQueryStartDate(),
                querySignupRequest.getQueryEndDate(),querySignupRequest.getNextActivityId(),querySignupRequest.getPageNum(),ActivityChannel.CS.name());
        int count = 0;
        String nextActivityId = null;
        if (CollectionUtils.isNotEmpty(signupData)){
            count = signupData.size();
            nextActivityId = signupData.get(count-1).getActivityId();
        }
        Integer total = csActivityMapper.queryCSActivityTotal();
        return new CSActivityQuerySignupResponse(total,count,nextActivityId,signupData);
    }


    private void insertUpdateCSData(List<ActivityPO> activityPOList, String operateType){
        if (CollectionUtils.isNotEmpty(activityPOList)) {
            List<Activity> activityList = activityPOList.stream().map(ActivityPO::getActivity).collect(Collectors.toList());
            List<ActivityOwnerRel> activityOwnerRelList = activityPOList.stream().map(ActivityPO::getActivityOwnerRel).collect(Collectors.toList());
            if (OperateType.INSERT.name().equals(operateType)){
                setOperatorInfo(1,activityList);
                activityMapper.insertBatch(activityList);
                activityPOList.forEach(s->{
                    Long activityId = s.getActivity().getId();
//                    s.getActivityAddress().setActivityId(activityId);
                    s.getPclassInfo().setActivityId(activityId);
                    s.getActivityOwnerRel().setActivityId(activityId);
                });
                setOperatorInfo(1,activityOwnerRelList);
                activityOwnerRelMapper.insertBatch(activityOwnerRelList);
            }else if (OperateType.UPDATE.name().equals(operateType)){
                activityMapper.updateBatchByActivityId(activityList);
                activityOwnerRelMapper.updateBatchByActivityId(activityOwnerRelList);
            }
//            List<ActivityAddress> activityAddressList = activityPOList.stream().map(ActivityPO::getActivityAddress).collect(Collectors.toList());
//            setOperatorInfo(1,activityAddressList);
//            activityAddressMapper.insertUpdateBatch(activityAddressList);
            List<PclassInfo> pclassInfoList = activityPOList.stream().map(ActivityPO::getPclassInfo).collect(Collectors.toList());
            setOperatorInfo(1,pclassInfoList);
            pclassInfoMapper.insertUpdateBatch(pclassInfoList);
        }
        log.info((OperateType.INSERT.name().equals(operateType)?"Insert":"Update")+" {} line CS data ： {}",activityPOList==null ? 0 : activityPOList.size(), new Gson().toJson(activityPOList));
    }

    /*todo: to howard team:
     1. Inconsistent code formatting (spacing, brackets, etc.) should be fixed to improve readability and maintainability.
     2. Non-thread safety issue: `ArrayList` is not thread-safe, so the use of `ArrayList` for `activityPOList` should be changed to a thread-safe collection.
     3. Ensure thread safety when accessing the `activityInfoList` list.
     6. The use of `ne.printStackTrace()` in the catch block should be logged properly.
     7. The `oldPclassInfo` should be checked for not `null` before attempting to access its properties such as `oldPclassInfo.getActivityId()`.
     10. It would be better to catch more specific exceptions instead of `NumberFormatException` to avoid catching too many exceptions. This can be done by surrounding the code block with specific try-catch blocks for each operation that might throw an exception.
     11. The code could also use some refactoring to improve its structure and reduce complexity. */
    private List<ActivityPO> convertToActivityPOList(List<CSActivitySyncRequest.ActivityInfo> activityInfoList, ActivitySyncResponse errorResponse) {
        List<ActivityPO> activityPOList = new ArrayList<>();
        for (CSActivitySyncRequest.ActivityInfo activityInfo : activityInfoList) {
            String activityCode = activityInfo.getActivityCode();
            String activityType = activityInfo.getActivityType();
            ActivityPO activityPO = new ActivityPO();
            try {
                Activity activity = new Activity();
                activity.setTopic(activityType);
                activity.setChannel(ActivityChannel.CS.name());
                activity.setOwnerId(activityInfo.getOwnerId());
                String eventDate = activityInfo.getEventDate();
                if (StringUtils.isNotBlank(eventDate)){
                    activity.setStartTime(DateUtils.parseDate(eventDate));
                    eventDate +=" 23:59:59";
                    if (CSActivityType.LARGE_CS.getValue().equals(activityType) || CSActivityType.MIDDLE_CS.getValue().equals(activityType)){
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(DateUtils.parseDate(eventDate));
                        calendar.add(Calendar.DATE,1);
                        activity.setEndTime(calendar.getTime());
                    }else {
                        activity.setEndTime(DateUtils.parseDate(eventDate));
                    }
                }
                activity.setIsEnabled(true);
                activityPO.setActivity(activity);
                ActivityOwnerRel activityOwnerRel = new ActivityOwnerRel();
                activityOwnerRel.setIsOwner(true);
                activityOwnerRel.setUserId(activityInfo.getOwnerId());
                activityPO.setActivityOwnerRel(activityOwnerRel);

                PclassInfo pclassInfo = new PclassInfo();
                pclassInfo.setId(activityCode);
                pclassInfo.setClassesCode(activityCode);
                pclassInfo.setActivityType(activityInfo.getActivityType());
                pclassInfo.setNcCode(activityInfo.getStoreCode());
                pclassInfo.setDirectorExmployeeNum(activityInfo.getOwnerId());
                pclassInfo.setDirector(activityInfo.getOwnerName());
                pclassInfo.setLimitCount(activityInfo.getLimitCount());
                pclassInfo.setIsEnabled(true);
                pclassInfo.setIsDeleted(false);
                activityPO.setPclassInfo(pclassInfo);

                activityPOList.add(activityPO);

                String pclassInfoId = pclassInfo.getId();
                PclassInfo oldPclassInfo = pclassInfoMapper.getByIdEX(pclassInfoId);
                if (oldPclassInfo==null){
                    activityPO.setOperateType(OperateType.INSERT.name());
                }else {
                    activityPO.setOperateType(OperateType.UPDATE.name());
                    Long activityId = oldPclassInfo.getActivityId();
                    activityPO.getActivity().setId(activityId);
//                    activityPO.getActivityAddress().setActivityId(activityId);
                    activityPO.getActivityOwnerRel().setActivityId(activityId);
                    activityPO.getPclassInfo().setActivityId(activityId);
                }
                activityPO.setIsCorrectData(activityInfo.getIsCorrectData());
            } catch (NumberFormatException ne){
                ne.printStackTrace();
                putErrorResponse(errorResponse,activityCode, "字段 longitude或latitude 格式错误");
                activityPO.setIsCorrectData(false);
            }
        }
        return activityPOList;
    }

       /*todo: to howard team:
      1. Improve the method signature by including the return type in the signature, which makes it clearer what data the method may produce.      
      2. Remove redundant null-check for the activity code since it is unnecessary, as the equals method returns false whenever one of its parameters is null.      
      3. Instead of using if-else statements to initialize the errorDataList, it is clearer, easier to understand and more concise to use the ternary operator.      
      4. Use the diamond operator to avoid the need to repeat the generic type in the initialization of the ArrayList.      
      5. Use a for-each loop, where possible, instead of a traditional for loop to make the code more readable and less error-prone, provided that the order of the elements is not important.      
      6. Replace the loop, which searching for duplicates with a stream to improve the code's readability.      
      7. Refactor the code to include a method, private static void addNewErrorDataToList() to handle the creation and addition of a new errorData object to the errorDataList.
      */
     private void putErrorResponse(ActivitySyncResponse errorResponse, String activityCode, String errorDesc) {
        List<ActivitySyncResponse.ErrorData> errorDataList;
        if (CollectionUtils.isNotEmpty(errorResponse.getErrorDataList())){
            errorDataList = errorResponse.getErrorDataList();
        }else {
            errorDataList = new ArrayList<>();
            errorResponse.setErrorDataList(errorDataList);
        }
        boolean isExist = false;
        for (ActivitySyncResponse.ErrorData errorData : errorDataList) {
            if (errorData.getActivityCode()!=null&&errorData.getActivityCode().equals(activityCode)){
                errorData.getErrorDescList().add(errorDesc);
                isExist = true;
            }
        }
        if (!isExist){
            ActivitySyncResponse.ErrorData errorDataNew = new ActivitySyncResponse.ErrorData();
            List<String> errorDescList = new ArrayList<>();
            errorDescList.add(errorDesc);
            errorDataNew.setActivityCode(activityCode);
            errorDataNew.setErrorDescList(errorDescList);
            errorDataList.add(errorDataNew);
        }
    }

    private void setOperatorInfo(Integer identifier, List<? extends MainBaseAdminEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (MainBaseAdminEntity entity : entities) {
            entity.setCreator(serviceName);
            entity.setUpdater(serviceName);
            entity.setCreatedByUser(identifier);
            entity.setUpdatedByUser(identifier);
        }
    }

        /*todo: to howard team:
      Optimization items:
      4. Instead of performing a check for an empty list and throwing an exception, it would be better to catch any exception that may occur in the function and throw a more meaningful exception message that describes the nature of the exception.
      5. The string concatenation on line 18 should be improved by using the String.format method.
      6. The if else block on lines 23-44 could be refactored. The code can be better optimized by creating an ArrayList of empty Strings and adding each error message to the array if it contains null or empty strings.
      7. The variables defined inside the for loop must be used only inside the loop, as their scope is not required outside the loop.
      8. It would be better to initialize the errorFlag variable with the inverse operator rather than false. This would eliminate the need to check whether the variable is false or true.
      9. Instead of collectionUtils.isEmpty(errorResponse.getErrorDataList()), errorResponse.getErrorDataList().isEmpty() would be shorter and clearer. 
      
      To-Do List:
      4- Change the exception thrown in case of an empty list.
      5- Improve the string concatenation on line 18.
      6- Refactor the if-else block on lines 23-44.
      7- Restrict the scope of variables defined inside the for loop.
      8- Initialize the errorFlag variable with the inverse operator rather than false.
      9- Modify the lines of code using errorResponse.getErrorDataList() to errorResponse.getErrorDataList().isEmpty(). */
     private ActivitySyncResponse verifyData(List<CSActivitySyncRequest.ActivityInfo> activityInfoList){
        if (CollectionUtils.isNotEmpty(activityInfoList)){
            ActivitySyncResponse errorResponse = new ActivitySyncResponse();
            for (int i = 0; i < activityInfoList.size(); i++) {
                CSActivitySyncRequest.ActivityInfo activityInfo = activityInfoList.get(i);
                String activityCode = activityInfo.getActivityCode();
                ActivitySyncResponse.ErrorData errorData = new ActivitySyncResponse.ErrorData();
                if (StringUtils.isBlank(activityCode)){
                    List<String> errorList = new ArrayList<>();
                    errorList.add("第" + (i + 1) + "条数据 activity_code不能为空");
                    errorData.setActivityCode(activityCode);
                    errorData.setErrorDescList(errorList);
                    if (CollectionUtils.isEmpty(errorResponse.getErrorDataList())){
                        List<ActivitySyncResponse.ErrorData> errorDataList = new ArrayList<>();
                        errorDataList.add(errorData);
                        errorResponse.setErrorDataList(errorDataList);
                    }else {
                        errorResponse.getErrorDataList().add(errorData);
                    }
                    activityInfo.setIsCorrectData(false);
                }else {
                    boolean errorFlag = false;
                    List<String> errorList = new ArrayList<>();
                    if (StringUtils.isBlank(activityInfo.getActivityType())){
                        errorList.add("字段 activity_type 不能为空");
                        activityInfo.setIsCorrectData(false);
                        errorFlag = true;
                    }
                    if (StringUtils.isBlank(activityInfo.getEventDate())){
                        errorList.add("字段 event_date 不能为空");
                        activityInfo.setIsCorrectData(false);
                        errorFlag = true;
                    }
                    if (StringUtils.isBlank(activityInfo.getOwnerId())){
                        errorList.add("字段 owner_id 不能为空");
                        activityInfo.setIsCorrectData(false);
                        errorFlag = true;
                    }
                    if (errorFlag){
                        errorData.setActivityCode(activityCode);
                        errorData.setErrorDescList(errorList);
                        if (CollectionUtils.isEmpty(errorResponse.getErrorDataList())){
                            List<ActivitySyncResponse.ErrorData> errorDataList = new ArrayList<>();
                            errorDataList.add(errorData);
                            errorResponse.setErrorDataList(errorDataList);
                        }else {
                            errorResponse.getErrorDataList().add(errorData);
                        }
                    }
                }
            }
            return errorResponse;
        }else {
            throw new DataIsNullException();
        }
    }

}
