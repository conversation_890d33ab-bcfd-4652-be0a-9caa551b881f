package loyalty.activity.service.external.service;

import com.cstools.data.internal.client.CRMClient;
import com.cstools.data.internal.domain.crm.request.CRMBindMemberWechatRequest;
import com.cstools.data.internal.domain.crm.request.CRMRegisterMemberRequest;
import com.cstools.data.internal.domain.crm.response.InternalCRMRestResponse;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.enums.PersonType;
import loyalty.activity.service.error.*;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.external.db.mapper.ClassOwnerDataMapper;
import loyalty.activity.service.external.db.model.CeRegisterLog;
import loyalty.activity.service.external.db.model.PnStaffDataInfo;
import loyalty.activity.service.external.domain.request.InsidePClassRegisterRequest;
import loyalty.activity.service.external.domain.request.InsidePClassSignInRequest;
import loyalty.activity.service.external.domain.request.InsidePClassUpdateMemberInfoRequest;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import static loyalty.activity.service.error.handler.CRMResponseChecker.checkCRMResponse;

@Service
@Slf4j
@RefreshScope
public class InsidePclassService {
    @Autowired
    private CRMClient crmClient;
    @Autowired
    private PclassSignInService pclassSignInService;
    @Autowired
    private PclassInfoMapper pclassInfoMapper;
    @Autowired
    private ClassInfoMapper classInfoMapper;
    @Resource
    private ClassOwnerDataMapper classOwnerDataMapper;
    @Autowired
    private JdbcTemplate ncpMasterJdbcTemplate;

    @Value("${thirdparty.ncp-master.datasource.person-query}")
    private String ncpPersonQuerySql;


    public void register(InsidePClassRegisterRequest request, CeRegisterLog ceRegisterLog) throws Exception {
        CRMRegisterMemberRequest crmRegisterMemberRequest = convertToMemberRegisterRequest(request, ceRegisterLog);
        ceRegisterLog.setRequestCrm(new Gson().toJson(crmRegisterMemberRequest));
        InternalCRMRestResponse internalCRMRestResponse = crmClient.registerMember(crmRegisterMemberRequest);
        ceRegisterLog.setCrmResponse(new Gson().toJson(internalCRMRestResponse));
        handleRegisterResponse(internalCRMRestResponse);
    }

    public void signIn(InsidePClassSignInRequest request) {
        UserSignInRequest userSignInRequest = new UserSignInRequest();
        userSignInRequest.setCellphone(request.getMobile());
        userSignInRequest.setActivityId(pclassInfoMapper.getActivityIdByClassesCode(request.getCode()));
        userSignInRequest.setSignInType(ActivityChannel.INSIDE_PCLASS.name());
        userSignInRequest.setLatitude(request.getLatitude());
        userSignInRequest.setLongitude(request.getLongitude());
        userSignInRequest.setYousheng(true);
        pclassSignInService.userSignIn(userSignInRequest);
    }

    public void bindWeChat(InsidePClassUpdateMemberInfoRequest request) {
        CRMBindMemberWechatRequest crmUpdateMemberInfoRequest = convertToCRMRegisterMemberRequest(request);
        crmUpdateMemberInfoRequest.setOpenId(request.getOpenid());
        InternalCRMRestResponse crmMemberResponse = crmClient.bindWechat(crmUpdateMemberInfoRequest);
        handleBindResponse(crmMemberResponse);
    }

    @Async
    public void insertCeRegisterLogEntity(CeRegisterLog ceRegisterLog) {
        try {
            classInfoMapper.insertCeRegisterLogEntity(ceRegisterLog);
        } catch (Exception e) {
            log.error("insert ce register log failure with error " + e);
        }
    }

    private CRMRegisterMemberRequest convertToMemberRegisterRequest(InsidePClassRegisterRequest insidePclassRegisterRequest, CeRegisterLog ceRegisterLog) throws ParseException {
        CRMRegisterMemberRequest request = new CRMRegisterMemberRequest();
        request.setIsverifycode("1");
        request.setName(insidePclassRegisterRequest.getName());
//        request.setCode(insidePclassRegisterRequest.getCode()==null?"123456":insidePclassRegisterRequest.getCode());
        request.setCellPhone(insidePclassRegisterRequest.getMobile());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isNotBlank(insidePclassRegisterRequest.getBabybirth())){
            request.setBabyBirth(format.parse(insidePclassRegisterRequest.getBabybirth()));
        }
        if (StringUtils.isNotBlank(insidePclassRegisterRequest.getSecbabybirth())){
            request.setSecbabyBirth(format.parse(insidePclassRegisterRequest.getSecbabybirth()));
        }
        if (StringUtils.isNotBlank(insidePclassRegisterRequest.getPnaid())){
            request.setPnaId(insidePclassRegisterRequest.getPnaid());
        }
        if(StringUtils.isNotBlank(insidePclassRegisterRequest.getPnecid())){
            request.setPnecId(insidePclassRegisterRequest.getPnecid());
        }
        if (insidePclassRegisterRequest.getType()==3) {
            request.setRegsubChannel(661);
            //调研id
            request.setSurveyId(insidePclassRegisterRequest.getCode());
            request.setPnrId(insidePclassRegisterRequest.getPnrid());
            //PNRID = PNEID  但是CRM一定要PNEID(测试环境验证过数据没问题)
            request.setPnecId(insidePclassRegisterRequest.getPnrid());
        }else {
            request.setRegsubChannel(511);
            if (insidePclassRegisterRequest.getType() == 1) {
                //院内妈妈班课程 ，type=1 的注册时，根据课程的创建人是pne还是pna,补全crm接口招募信息
                getRegisterRecruitCode(request, insidePclassRegisterRequest.getCode());
            }
        }
        request.setSourceType("中国优生");
        request.setAcceptlastedinfo("1");
        ceRegisterLog.setCode(insidePclassRegisterRequest.getCode());
        ceRegisterLog.setRegsubChannel(request.getRegsubChannel());
        ceRegisterLog.setMobile(request.getCellPhone());
        ceRegisterLog.setPnaid(request.getPnaId());
        ceRegisterLog.setPnecid(request.getPnecId());
        ceRegisterLog.setPnrid(insidePclassRegisterRequest.getPnrid());
        ceRegisterLog.setType(insidePclassRegisterRequest.getType());
        return request;
    }

    private void handleRegisterResponse(InternalCRMRestResponse crmMemberResponse) {
        if (crmMemberResponse == null) {
            return;
        }
        //检查CRM接口错误码
        checkCRMResponse(crmMemberResponse.getErrCode(), crmMemberResponse.getErrMsg());
//        //检查注册状态码
        String status = crmMemberResponse.getStatus();
        switch (status) {
            case "104":
                throw new CRMRegisterFailException();
                //else if("wechat_has_bind".equals(crmMemberResponse.getWechatBindStatus())||"phone_has_bind".equals(crmMemberResponse.getWechatBindStatus())) {
                //   throw new CampaignQueryException();
                //}
//                throw new CRMIsMemberException();
        }
//        String status = crmMemberResponse.getStatus();
//        switch (status) {
//            case "101":
//            case "102":
//                //NMA已转NUA（注册成功，当前月龄在-280~540，且曾经购买过1-3段产品）
//                //NMA可转NUA（注册成功，当前月龄在-280~540，未购买过1-3段产品）
//                break;
//            case "103":
//                break;
//            case "104":

//                throw new CRMRegisterFailException();
//            case "105":
//                if ("bind_successful".equals(crmMemberResponse.getWechatBindStatus())) {
//                    return;
//                }
//                //else if("wechat_has_bind".equals(crmMemberResponse.getWechatBindStatus())||"phone_has_bind".equals(crmMemberResponse.getWechatBindStatus())) {
//                //   throw new CampaignQueryException();
//                //}
////                throw new CRMIsMemberException();
//        }
    }

    private CRMBindMemberWechatRequest convertToCRMRegisterMemberRequest(InsidePClassUpdateMemberInfoRequest memberInfo) {
        CRMBindMemberWechatRequest request = new CRMBindMemberWechatRequest();
        request.setCellphone(memberInfo.getCellphone());
        request.setOpenId(memberInfo.getOpenid());
        request.setWeChatPlatform("1");
        return request;
    }

    private void handleBindResponse(InternalCRMRestResponse crmMemberResponse) {
        if (crmMemberResponse == null) {
            return;
        }
        //检查绑定状态码
        String status = crmMemberResponse.getStatus();
        switch (status) {
            case "104":
                //手机号非会员
                throw new CRMNoSuchMemberException();
            case "105":
                //手机号码已经绑定其他微信号
                throw new CRMAlreadyBindOtherWeChatException();
            case "106":
                //手机号码已经是会员且绑定微信成功
                break;
            case "110":
                //openId已经被注册
                throw new CRMOpenIdAlreadyRegisteredWeChatException();
            case "111":
                //unionId已经被注册
                throw new CRMUnionIdAlreadyRegisteredWeChatException();
        }
    }

    private void getRegisterRecruitCode(CRMRegisterMemberRequest request, String code) {
        if (StringUtils.isNotBlank(code)) {
           PclassInfo pclassInfo = pclassInfoMapper.getByClassesCode(code);
           try {
               String personType = ncpMasterJdbcTemplate.queryForObject(ncpPersonQuerySql, String.class, pclassInfo.getOwnerEmployeeNum());
               if(StringUtils.isNotBlank(personType)){
                   if(PersonType.PNE.code.equals(personType)){
                       request.setPnecId(pclassInfo.getOwnerEmployeeNum());
                   }else if(PersonType.PNA.code.equals(personType)){
                       request.setPnaId(pclassInfo.getOwnerEmployeeNum());
                   }else{
                       log.info("classcode={} with owner_id={},person_type={} is not pne or pna",code,pclassInfo.getOwnerEmployeeNum(),personType);
                   }
               }

           }catch (Exception e){
               log.error("query personType with employeeNo={} from ncp error={}",pclassInfo.getOwnerEmployeeNum(),e);
           }
           /* PnStaffDataInfo pnStaffDataInfo = classOwnerDataMapper.queryClassRecruitV2ByClassCode(code);
            if (pnStaffDataInfo != null && StringUtils.isNotBlank(pnStaffDataInfo.getStaffCode())) {
                if ("PNE".equals(pnStaffDataInfo.getStaffType())) {
                    request.setPnecId(pnStaffDataInfo.getStaffCode());
                } else if ("PNA".equals(pnStaffDataInfo.getStaffType())) {
                    request.setPnaId(pnStaffDataInfo.getStaffCode());
                }
            }*/
        }
    }

    public void getRegisterRecruitCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            PclassInfo pclassInfo = pclassInfoMapper.getByClassesCode(code);
            String personType = ncpMasterJdbcTemplate.queryForObject(ncpPersonQuerySql,String.class,pclassInfo.getOwnerEmployeeNum());
            if(StringUtils.isNotBlank(personType)){
                if(PersonType.PNE.code.equals(personType)){
                   log.info("pne===========");
                }else if(PersonType.PNA.code.equals(personType)){
                    log.info("pna===========");
                }
            }
        }
    }
}
