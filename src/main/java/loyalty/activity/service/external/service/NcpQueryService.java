package loyalty.activity.service.external.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.external.db.app.ParamClassInfo;
import loyalty.activity.service.external.db.app.ParamQueryClassRequest;
import loyalty.activity.service.external.db.app.StoreClassInfo;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.pclass.db.model.ActivityDetail;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class NcpQueryService {

    @Resource
    private ClassInfoMapper classInfoMapper;

    public PageResponse<ParamClassInfo> queryClassInfoForQueryParam(ParamQueryClassRequest request) {
        PageResponse<ParamClassInfo> pageResponse = new PageResponse<>();
        Page<List<ActivityDetail>> page =
            PageHelper.startPage(request.getPage(), request.getLimit(), true);
        List<ParamClassInfo> classesByParam = classInfoMapper.getClassesByParam(request);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(request.getPage());
        pageResponse.setResults(classesByParam);
        return pageResponse;
    }

    public List<StoreClassInfo> queryClassInfoForStore(String storeId) {
        List<StoreClassInfo> result = classInfoMapper.getClassesByStoreId(storeId);
        result.forEach(p -> p.setStoreId(storeId));
        return result;
    }


}
