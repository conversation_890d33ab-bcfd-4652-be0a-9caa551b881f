package loyalty.activity.service.external.service;

import com.cstools.data.internal.domain.cmp.client2.response.CMPGetCampaignDetailResponse;
import com.cstools.data.internal.error.CMPSystemException;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.external.domain.request.PraiseMomQuickRedeemQueryRequest;
import loyalty.activity.service.external.domain.response.PraiseMomQuickRedeemQueryResponse;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.PclassQrcodeInfo;
import loyalty.activity.service.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

@Service
@RefreshScope
public class QuickRedeemCampaignService {

    @Autowired
    private PclassInfoQueryMapper pclassInfoQueryMapper;

    @Value("${quick.redeem.codeurl}")
    private String quickQrCodeTemplate;

    public PraiseMomQuickRedeemQueryResponse getQuickRedeemInfo(@Valid PraiseMomQuickRedeemQueryRequest request) {
        PclassQrcodeInfo pclassQrcodeInfo= pclassInfoQueryMapper.getPclassQrcodeInfoByClassCode(request.getActivityCode());
        if(pclassQrcodeInfo==null){
            throw new PclassNotFoundException();
        }
        String qrCodeUrl=quickQrCodeTemplate.replace("{mobile}",request.getMobile()).replace("{activityCode}",request.getActivityCode()).replace("{ccCampaignId}",request.getCampaignId());
        return new PraiseMomQuickRedeemQueryResponse(qrCodeUrl, DateUtils.getNextDayEnd(pclassQrcodeInfo.getEndTime()));
    }
}
