package loyalty.activity.service.external.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.enums.CsSubscribeTypeEnums;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.external.client.JmgActivityClient;
import loyalty.activity.service.external.db.mapper.PClassExtMapper;
import loyalty.activity.service.external.dto.*;
import loyalty.activity.service.external.entity.PClassExt;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.InsertSignUpBaseInfo;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.service.PclassSignUpService;
import loyalty.activity.service.pclass.service.ClassesWxRemindService;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.SubscribeStatus;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.WxmppTemplateInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

@Slf4j
@Service
public class JmgActivityService {

    @Resource
    private PclassSignUpService pclassSignUpService;
    
    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;

    @Resource
    private ClassesWxRemindService classesWxRemindService;

    @Autowired
    private JmgActivityClient jmgActivityClient;

    @Resource
    private WxmppTemplateInfoMapper wxmppTemplateInfoMapper;

    @Resource
    private PClassExtMapper pClassExtMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("${mp.appid}")
    private String appid;

    @Transactional(rollbackFor = Exception.class)
    public void userApply(JmgActivityApplyRequest request) {
        // 获取活动信息
        PclassInfoApplyCount pclassInfo = pclassInfoQueryMapper.getPclassInfoApplyCountByCode(
            request.getCode(), request.getCellphone());
        if(pclassInfo==null){
            throw new PclassNotFoundException();
        }
        
        // 构建报名基础信息
        InsertSignUpBaseInfo baseInfo = new InsertSignUpBaseInfo();
        baseInfo.setCellphone(request.getCellphone());
        baseInfo.setUnionId(request.getUnionid());
        baseInfo.setOpenId(request.getOpenid());
        
        // 经纬度转换
        if (StringUtils.isNotBlank(request.getCurrentLatitude())) {
            baseInfo.setLatitude(new BigDecimal(request.getCurrentLatitude()));
        }
        if (StringUtils.isNotBlank(request.getCurrentLongitude())) {
            baseInfo.setLongitude(new BigDecimal(request.getCurrentLongitude()));
        }
        
        baseInfo.setBabyStatus(request.getBabyStatus());
        baseInfo.setAttendance(request.getAttendance());
        baseInfo.setUserName(request.getUserName());
        baseInfo.setTriggerTime(DateUtil.parseDateTime(request.getApplyTime()));

        if (StringUtils.isNotBlank(request.getBabyBirthday())) {
            baseInfo.setBabyBirthday(DateUtil.parseDate(request.getBabyBirthday()));
        }
        baseInfo.setRemark(request.getRemark());
        baseInfo.setInputCellphone(request.getCellphone());
        baseInfo.setCreator("jmgActivityClient");
        
        // 调用报名服务
        pclassSignUpService.doInsertPclassUserApplyDetail(pclassInfo, baseInfo);
    }


    public void sendSubscribeMsg(JmgSubscribeMsgRequest request) {
        SaveRemindSubscriptionRequest subscriptionRequest = new SaveRemindSubscriptionRequest();
        subscriptionRequest.setAppid(appid);
        subscriptionRequest.setPclassCode(request.getCode());
        subscriptionRequest.setCellphone(request.getCellphone());
        subscriptionRequest.setUnionid(request.getUnionid());
        subscriptionRequest.setOpenid(request.getOpenid());
        subscriptionRequest.setEvent("subscribe_msg_popup_event");
        subscriptionRequest.setSubscribeTime(new Date());
        subscriptionRequest.setContent(request.getUpdateContent());
        subscriptionRequest.setExternalUrl(request.getRedirectUrl());

        List<SaveRemindSubscriptionRequest.Template> templates = new ArrayList<>();
        SaveRemindSubscriptionRequest.Template template = new SaveRemindSubscriptionRequest.Template();
        CsSubscribeTypeEnums subcribeTypeEnum = CsSubscribeTypeEnums.getByCode(request.getSubscribeType());
        WxmppTemplateInfo wxmppTemplateInfo = wxmppTemplateInfoMapper.selectOne(
                new LambdaQueryWrapper<WxmppTemplateInfo>().eq(WxmppTemplateInfo::getSubscribeType,subcribeTypeEnum.name()));
        template.setTemplateId(wxmppTemplateInfo.getPriTmplId());
        template.setType(subcribeTypeEnum.name());
        template.setSubscribeStatus(SubscribeStatus.accept.name());
        templates.add(template);
        subscriptionRequest.setTemplate(templates);
        classesWxRemindService.sendRemindSubscription(subscriptionRequest);
    }

    public void notifyCheckData(NotifyCheckDataRequest request) {
       jmgActivityClient.notifyCheckData(request.getActivitycode(), request.getRecordlist());
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncActivityInfo(JmgActivityRequest request) {
        log.info("开始同步活动信息, request={}", request);

        // 构建活动扩展信息
        PClassExt record = new PClassExt();
        record.setClassesCode(request.getCode());
        record.setRegistrationBeginDate(DateUtil.parse(request.getRegistrationBeginDate()));
        record.setRegistrationEndDate(DateUtil.parse(request.getRegistrationEndDate()));
        record.setStatus(Integer.parseInt(request.getStatus()));
        record.setPageTitle(request.getPageTitle());
        record.setCoverImg(request.getCoverImg());
        record.setTopImg(request.getTopImg());
        record.setDetailImg(request.getDetailImg());
        record.setRestrictAttendance(request.getRestrictAttendance());
        record.setPosterImg(request.getPosterImg());
        record.setPosterQrImg(request.getPosterQrImg());
        record.setLogoImg(request.getLogoImg());
        // 设置创建者和更新者
        record.setCreator("SYSTEM");
        record.setUpdater("SYSTEM");

        // 插入或更新数据
        pClassExtMapper.insertOrUpdate(record);
        log.info("同步活动扩展信息成功, classesCode={}", request.getCode());
    }

    public void notifySignData(String activityCode, List<SignInfo> signInfoList) {
        jmgActivityClient.notifySignData(activityCode, signInfoList);
    }
}