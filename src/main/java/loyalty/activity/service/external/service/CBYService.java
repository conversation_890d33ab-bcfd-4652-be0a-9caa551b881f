package loyalty.activity.service.external.service;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.enums.QrcodeOperateType;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.error.CBYClassDateException;
import loyalty.activity.service.error.CBYNoSuchDataException;
import loyalty.activity.service.error.CBYQueryTypeException;
import loyalty.activity.service.error.handler.PNCinsideClassException;
import loyalty.activity.service.external.db.mapper.ClassInfoMapper;
import loyalty.activity.service.external.db.model.ActivityClassInfo;
import loyalty.activity.service.external.db.model.ActivityPO;
import loyalty.activity.service.external.domain.request.ActivitySyncRequest;
import loyalty.activity.service.external.domain.request.CBYQrcodeRequest;
import loyalty.activity.service.external.domain.response.ActivitySyncResponse;
import loyalty.activity.service.external.domain.response.CBYQrcodeResponse;
import loyalty.activity.service.external.processor.ActivitySyncHandler;
import loyalty.activity.service.pclass.service.InternalInsidePClassService;
import loyalty.activity.service.pnec.db.mapper.PclassResearchInfoMapper;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;
import loyalty.activity.service.sharelib.rest.client.YoushengClient;
import loyalty.activity.service.sharelib.rest.request.YouShengSignInQrcodeRequest;
import loyalty.activity.service.sharelib.rest.response.ResearchResponse;
import loyalty.activity.service.sharelib.rest.response.YouShengSignInQrcodeResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;
import loyalty.activity.service.sharelib.sharelib.domain.bo.OperateType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityAddressMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityOwnerRelMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityAddress;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityOwnerRel;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static loyalty.activity.service.config.AppConstant.*;

@Slf4j
@Service
public class CBYService {

    @Resource
    private ClassInfoMapper classInfoMapper;
    @Resource
    private PclassResearchInfoMapper pclassResearchInfoMapper;

    private final ActivitySyncHandler activitySyncHandler;

    private final ActivityMapper activityMapper;

    private final ActivityOwnerRelMapper activityOwnerRelMapper;

    private final ActivityAddressMapper activityAddressMapper;

    private final PclassInfoMapper pclassInfoMapper;

    @Autowired
    private InternalInsidePClassService internalInsidePClassService;

    @Autowired
    private YoushengClient youshengClient;

    public CBYService(ActivitySyncHandler activitySyncHandler, ActivityMapper activityMapper, ActivityOwnerRelMapper activityOwnerRelMapper, ActivityAddressMapper activityAddressMapper, PclassInfoMapper pclassInfoMapper) {
        this.activitySyncHandler = activitySyncHandler;
        this.activityMapper = activityMapper;
        this.activityOwnerRelMapper = activityOwnerRelMapper;
        this.activityAddressMapper = activityAddressMapper;
        this.pclassInfoMapper = pclassInfoMapper;
    }


    public CBYQrcodeResponse getQrcode(CBYQrcodeRequest request) {
        if (!CBY_GETQRCODE_TYPE_LIST.contains(request.getQueryType())) {
            throw new CBYQueryTypeException();
        }
        CBYQrcodeResponse cbyQrcodeResponse = new CBYQrcodeResponse(getSearchData(request));
        return cbyQrcodeResponse;
    }

    private String getSearchData(CBYQrcodeRequest request) {
        switch (request.getQueryType()) {
            case CBY_GETQRCODE_TYPE_INSIDE_PCLASS:
                ActivityClassInfo classesByCode = classInfoMapper.getClassesByCode(request.getQueryCode(), ActivityChannel.INSIDE_PCLASS.name());
                if (classesByCode != null) {
                    //判断当前 活动类型是否是 院内-PNE链路 等于的话就抛异常
                    if (StringUtils.isNotBlank(classesByCode.getActivityType()) && INSIDE_PCLASS_PNE_TYPE.equals(classesByCode.getActivityType())) {
                        //抛出异常
                        throw new PNCinsideClassException();
                    }
                    if (StringUtils.isBlank(classesByCode.getQrcodeUrl()) && classesByCode.getStartTime().getTime() - System.currentTimeMillis() > DAY_14) {
                        throw new CBYClassDateException();
                    }
                    return internalInsidePClassService.youShengSignInQrcodeGenerate(request.getQueryCode());
                    //return classesByCode.getQrcodeUrl();
                }
                throw new CBYNoSuchDataException();
            case CBY_GETQRCODE_TYPE_INSIDE_RESEARCH:
                PclassResearchInfo dataByResearchId = pclassResearchInfoMapper.getDataByResearchId(request.getQueryCode());
                if (dataByResearchId == null) {
                    throw new CBYNoSuchDataException();
                }
                return dataByResearchId.getQrcodeUrl();
        }
        throw new CBYClassDateException();
    }

    @Transactional(rollbackFor = Exception.class)
    public ActivitySyncResponse syncActivityData(ActivitySyncRequest activitySyncRequest) {
        ActivitySyncResponse.ActivitySyncResponseBuilder responseBuilder = ActivitySyncResponse.builder();
        List<ActivitySyncResponse.ErrorData> errorDataList = activitySyncHandler.validate(activitySyncRequest.getActivityList());
        responseBuilder.errorDataList(errorDataList);
        List<String> errorCodeList = errorDataList.stream().map(ActivitySyncResponse.ErrorData::getActivityCode).collect(Collectors.toList());
        List<ActivityPO> activityPOList = activitySyncHandler.buildActivityPO(activitySyncRequest.getActivityList(), errorCodeList);
        List<ActivityPO> insertList = activityPOList.stream().filter(s -> OperateType.INSERT.name().equals(s.getOperateType()) && s.getIsCorrectData()).collect(Collectors.toList());
        List<ActivityPO> updateList = activityPOList.stream().filter(s -> OperateType.UPDATE.name().equals(s.getOperateType()) && s.getIsCorrectData()).collect(Collectors.toList());
        insertUpdateCSData(insertList, OperateType.INSERT.name());
        insertUpdateCSData(updateList, OperateType.UPDATE.name());
        responseBuilder.failSize(errorDataList.size());
        errorDataList.forEach(s -> s.setErrorDesc(StringUtils.join(s.getErrorDescList(), ",")));
        List<String> successCodes = new ArrayList<>();
        activityPOList.stream().forEach(activityPO -> successCodes.add(activityPO.getPclassInfo().getClassesCode()));
        responseBuilder.successActivityCodes(successCodes);
        return responseBuilder.build();
    }

    private void insertUpdateCSData(List<ActivityPO> activityPOList, String operateType) {
        if (CollectionUtils.isNotEmpty(activityPOList)) {
            List<Activity> activityList = activityPOList.stream().map(ActivityPO::getActivity).collect(Collectors.toList());
            List<ActivityOwnerRel> activityOwnerRelList = activityPOList.stream().map(ActivityPO::getActivityOwnerRel).collect(Collectors.toList());
            if (OperateType.INSERT.name().equals(operateType)) {
                activityMapper.insertBatch(activityList);
                activityPOList.forEach(s -> {
                    Long activityId = s.getActivity().getId();
                    s.getActivityAddress().setActivityId(activityId);
                    s.getPclassInfo().setActivityId(activityId);
                    s.getActivityOwnerRel().setActivityId(activityId);
                });
                activityOwnerRelMapper.insertBatch(activityOwnerRelList);
            } else if (OperateType.UPDATE.name().equals(operateType)) {
                activityMapper.updateBatchByActivityId(activityList);
                activityOwnerRelMapper.updateBatchByActivityId(activityOwnerRelList);
                //院内的妈妈班更新二维码
                try {
                    updateQrcode(activityPOList);
                } catch (Exception e) {
                    log.error("update yousheng qrcode error={}", e.getMessage());
                }
            }
            List<PclassInfo> pclassInfoList = activityPOList.stream().map(ActivityPO::getPclassInfo).collect(Collectors.toList());
            List<ActivityAddress> activityAddressList = activityPOList.stream().map(ActivityPO::getActivityAddress).collect(Collectors.toList());
            pclassInfoMapper.insertUpdateBatch(pclassInfoList);
            activityAddressMapper.insertUpdateBatch(activityAddressList);
        }
        log.info((OperateType.INSERT.name().equals(operateType) ? "Insert" : "Update") + " {} line pclass data ： {}", activityPOList == null ? 0 : activityPOList.size(), new Gson().toJson(activityPOList));
    }

    private void updateQrcode(List<ActivityPO> activityList) {
        for (ActivityPO activity : activityList) {
            if (ActivityChannel.INSIDE_PCLASS.name().equals(activity.getActivity().getChannel())) {
                generateInsideClassQrcode(activity, QrcodeOperateType.UPDATE);
            }
        }
    }

    private void generateInsideClassQrcode(ActivityPO activityPO, QrcodeOperateType qrcodeOperateType) {
        Activity activity = activityPO.getActivity();
        PclassInfo pclassInfo = activityPO.getPclassInfo();
        String classesCode = pclassInfo.getClassesCode();
        YouShengSignInQrcodeRequest request = new YouShengSignInQrcodeRequest();
        request.setName(pclassInfo.getCourseName());
        request.setStime(String.valueOf(activity.getStartTime().getTime() / 1000));
        request.setEtime(String.valueOf(activity.getEndTime().getTime() / 1000));
        //todo 定义变量
        //1:新增 2：更新
        request.setType(qrcodeOperateType.value);
        request.setStatus("1");
        request.setCode(classesCode);
        request.setChannel("92");
        request.setPneid(pclassInfo.getOwnerEmployeeNum());
        request.setUsername(pclassInfo.getOwnerName());
        request.setMobilePhone(pclassInfo.getOwnerMobile());
        youshengClient.generateQrCode(request);
        ResearchResponse<YouShengSignInQrcodeResponse> response = youshengClient.queryActivity(classesCode, "92");
        if (response.getCode() == 0) {
            log.error("insert or update qrCode failure , classesCode = {} , errorMessage = {}", classesCode, response.getMsg());
            return;
        }
        if (response.getData() != null && response.getData().getQrcodeUrl() != null) {
            YouShengSignInQrcodeResponse qrcodeResponse = response.getData();
            activityPO.getPclassInfo().setQrcodeUrl(qrcodeResponse.getQrcodeUrl());
            activityPO.getPclassInfo().setQrcodeExpireTime(new Date(Long.parseLong(qrcodeResponse.getExpireTime()) * 1000));
        }
    }

    public void batchUpdateQrcode() {
        List<String> classCodes= classInfoMapper.getInsideClassAfterToday();
        for(String classCode:classCodes) {
            log.info("start to update insideclass={}",classCode);
            internalInsidePClassService.youShengSignInQrcodeUpate(classCode);
        }
    }
}
