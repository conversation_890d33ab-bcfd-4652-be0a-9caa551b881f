package loyalty.activity.service.external.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.external.dto.CheckInfo;
import loyalty.activity.service.external.dto.JmgBaseResponse;
import loyalty.activity.service.external.dto.SignInfo;
import loyalty.activity.service.external.util.AESUtil;
import loyalty.activity.service.external.util.SignUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import loyalty.activity.service.external.entity.LogExternalApiCall;
import loyalty.activity.service.external.db.mapper.LogExternalApiCallMapper;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
@RefreshScope
public class JmgActivityClient {

    @Value("${jmg.activity.apiKey}")
    private String apiKey;

    @Value("${jmg.activity.apiSecret}")
    private String apiSecret;

    @Value("${jmg.activity.api.url}")
    private String apiUrl;

    @Resource
    private final RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private LogExternalApiCallMapper logExternalApiCallMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public JmgActivityClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 推送签到数据到美赞荟
     *
     * @param activityCode 活动编码
     * @param checkInfoList 签到信息列表
     * @return 是否推送成功
     */
    public boolean notifyCheckData(String activityCode, List<CheckInfo> checkInfoList) {
        String url = apiUrl + "/bkapi/activity/notifycheckdata";
        String recordListStr = convertToJson(checkInfoList);
        return sendRequest(url, activityCode, recordListStr, checkInfoList.size(), "签到数据");
    }

    /**
     * 推送报名数据到美赞荟
     *
     * @param activityCode 活动编码
     * @param signInfoList 报名信息列表
     * @return 是否推送成功
     */
    public boolean notifySignData(String activityCode, List<SignInfo> signInfoList) {
        String url = apiUrl + "/bkapi/activity/notifysigndata";
        String recordListStr = convertToJson(signInfoList);
        return sendRequest(url, activityCode, recordListStr, signInfoList.size(), "报名数据");
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    private String convertToJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("对象转JSON异常", e);
            throw new RuntimeException("对象转JSON异常", e);
        }
    }

    /**
     * 发送请求到美赞荟
     *
     * @param url 请求URL
     * @param activityCode 活动编码
     * @param recordListStr 记录列表JSON字符串
     * @param recordCount 记录数量
     * @param dataType 数据类型描述（用于日志）
     * @return 是否请求成功
     */
    private boolean sendRequest(String url, String activityCode, String recordListStr, int recordCount, String dataType) {
        LogExternalApiCall apiLog = new LogExternalApiCall();
        
        try {
            String timestamp = LocalDateTime.now().format(DATE_FORMATTER);

            // 构建form参数
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            formParams.add("activitycode", activityCode);
            formParams.add("recordcnt", String.valueOf(recordCount));
            formParams.add("recordlist", recordListStr);
            formParams.add("timestamp", timestamp);
            
            // 记录请求参数
            apiLog.setActivityCode(activityCode);
            apiLog.setReqPath(url);
            apiLog.setReqParams(objectMapper.writeValueAsString(formParams));
            apiLog.setCaller("JmgActivityClient");

            // 构建签名参数Map
            Map<String, String> signParams = new HashMap<>();
            signParams.put("activitycode", activityCode);
            signParams.put("recordcnt", String.valueOf(recordCount));
            signParams.put("recordlist", recordListStr);
            signParams.put("timestamp", timestamp);
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("wx3apikey", apiKey);
            headers.add("wx3apisign", SignUtil.generateSign(signParams, apiSecret));

            log.info("开始推送{}到美赞荟, url={}, params={}", dataType, url, formParams);
            HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(formParams, headers);
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                httpEntity,
                String.class
            );
            
            // 记录响应结果
            String responseBody = response.getBody();
            apiLog.setResultBody(responseBody);
            log.info("美赞荟{}推送响应={}", dataType, responseBody);

            if (responseBody != null) {
                JmgBaseResponse jmgBaseResponse = objectMapper.readValue(responseBody, JmgBaseResponse.class);
                Integer errcode = jmgBaseResponse.getErrcode();
                if (errcode != null && errcode == 0) {
                    apiLog.setReqSucceed(true);
                    logExternalApiCallMapper.insert(apiLog);
                    return true;
                }
                log.error("推送{}失败, errcode: {}, errmsg: {}", dataType, errcode, jmgBaseResponse.getErrmsg());
            }
            apiLog.setReqSucceed(false);
            logExternalApiCallMapper.insert(apiLog);
            return false;
        } catch (Exception e) {
            log.error("推送{}异常", dataType, e);
            apiLog.setReqSucceed(false);
            apiLog.setResultBody("Exception: " + e.getMessage());
            logExternalApiCallMapper.insert(apiLog);
            return false;
        }
    }

    public static void main(String[] args) {
        // 创建签到信息列表
        List<CheckInfo> checkInfoList = new ArrayList<>();
        
        // 构建签到信息1
        CheckInfo checkInfo1 = new CheckInfo();
        checkInfo1.setOpenid("oWVvN4mBb6ie_wKqOial_RyX1Z8Q");
        checkInfo1.setUnionid("oqFLxsw2h6xFGMQpuIg0nxB3JC_g");
        checkInfo1.setNickname("张三");
        checkInfo1.setContactphoneencrypt(AESUtil.encrypt("13800138000"));
        checkInfo1.setCheckindate("2024-03-20 10:30:00");
        checkInfoList.add(checkInfo1);
        
        // 构建签到信息2
        CheckInfo checkInfo2 = new CheckInfo();
        checkInfo2.setOpenid("oWVvN4mBb6ie_wKqOial_RyX2Y9R");
        checkInfo2.setUnionid("oqFLxsw2h6xFGMQpuIg0nxB4KD_h");
        checkInfo2.setNickname("李四");
        checkInfo2.setContactphoneencrypt(AESUtil.encrypt("13900139000"));
        checkInfo2.setCheckindate("2024-03-20 10:35:00");
        checkInfoList.add(checkInfo2);

        // 创建客户端实例（实际使用时通过Spring注入）
        JmgActivityClient client = new JmgActivityClient(new RestTemplate());
        
        // 调用推送接口
        boolean success = client.notifyCheckData("ACTIVITY_CODE_001", checkInfoList);
        
        // 打印结果
        System.out.println("推送结果: " + (success ? "成功" : "失败"));
    }
} 