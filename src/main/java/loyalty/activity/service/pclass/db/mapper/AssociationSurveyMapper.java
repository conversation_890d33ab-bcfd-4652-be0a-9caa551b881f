package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.AssociationSurveyInfo;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserSurvey;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserSurveyItem;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AssociationSurveyMapper {

    @Select("SELECT\n" +
            "t1.id associationId,channel,t1.name associationName,summary, \n" +
            "t2.id surveyId,t2.name surveyName,topic, \n" +
            "t3.id topicId,t3.title topicTitle,check_type,t3.sequence topicSequence,score,is_required, \n" +
            "t4.id itemId,t4.title itemTitle,option_type,t4.sequence itemSequence,is_correct_option \n" +
            "FROM\n" +
            "pclass_association t1\n" +
            "INNER JOIN pclass_association_survey t2 ON t1.id = t2.association_id AND t2.is_enabled = 1 \n" +
            "INNER JOIN pclass_survey_topic t3 ON t2.id = t3.survey_id AND t3.is_enabled = 1 \n" +
            "LEFT JOIN pclass_survey_topic_item t4 ON t3.id = t4.topic_id AND t4.is_enabled = 1 \n" +
            "WHERE t1.id = #{associationId}")
    @Results(id = "associationSurvey_mapping",value = {
            @Result(column = "associationId",property = "associationId"),
            @Result(column = "channel",property = "channel"),
            @Result(column = "associationName",property = "associationName"),
            @Result(column = "summary",property = "summary"),
            @Result(column = "surveyName",property = "surveyName"),
            @Result(column = "topic",property = "topic"),
            @Result(column = "topicId",property = "topicId"),
            @Result(column = "topicTitle",property = "topicTitle"),
            @Result(column = "check_type",property = "checkType"),
            @Result(column = "topicSequence",property = "topicSequence"),
            @Result(column = "score",property = "score"),
            @Result(column = "is_required",property = "isRequired"),
            @Result(column = "itemId",property = "itemId"),
            @Result(column = "itemTitle",property = "itemTitle"),
            @Result(column = "option_type",property = "optionType"),
            @Result(column = "itemSequence",property = "itemSequence"),
            @Result(column = "is_correct_option",property = "isCorrectOption")
    })
    List<AssociationSurveyInfo> getSurveyInfoByAssociationId(@Param("associationId")Integer associationId);

    @Select("SELECT\n" +
            "t1.id associationId,channel,t1.name associationName,summary, \n" +
            "t2.id surveyId,t2.name surveyName,topic, \n" +
            "t3.id topicId,t3.title topicTitle,check_type,t3.sequence topicSequence,score,is_required, \n" +
            "t4.id itemId,t4.title itemTitle,option_type,t4.sequence itemSequence,is_correct_option \n" +
            "FROM\n" +
            "pclass_association t1\n" +
            "INNER JOIN pclass_association_survey t2 ON t1.id = t2.association_id AND t2.is_enabled = 1 \n" +
            "INNER JOIN pclass_survey_topic t3 ON t2.id = t3.survey_id AND t3.is_enabled = 1 \n" +
            "LEFT JOIN pclass_survey_topic_item t4 ON t3.id = t4.topic_id AND t4.is_enabled = 1 \n" +
            "WHERE t2.id = #{surveyId}")
    @ResultMap("associationSurvey_mapping")
    List<AssociationSurveyInfo> getSurveyInfoBySurveyId(@Param("surveyId")Integer surveyId);

    @Insert("INSERT INTO pclass_user_survey(id,survey_id,pclass_code,cellphone,unionid,openid,ip,score,creator,updater)" +
            " VALUES(#{id},#{surveyId},#{pclassCode},#{cellphone},#{unionid},#{openid},#{ip},#{score},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE survey_id=VALUES(survey_id),pclass_code=VALUES(pclass_code),cellphone=VALUES(cellphone),unionid=VALUES(unionid),openid=VALUES(openid),ip=VALUES(ip),score=VALUES(score),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdatePclassUserSurveyEntity(PclassUserSurvey pclassUserSurvey);

    @Insert("<script>" +
            "INSERT INTO pclass_user_survey_item(id,user_survey_id,topic_id,question_content,user_answer,correct_answer,is_correct,score,creator,updater)VALUES " +
            "<foreach item='item' collection='userSurveyItemList' separator=','>" +
            " (#{item.id},#{item.userSurveyId},#{item.topicId},#{item.questionContent},#{item.userAnswer},#{item.correctAnswer},#{item.isCorrect},#{item.score},#{item.creator},#{item.updater}) " +
            "</foreach>" +
            " </script>")
    Integer insertPclassUserSurveyItemBatch(@Param("userSurveyItemList")List<PclassUserSurveyItem> userSurveyItemList);

    @Select("SELECT\n" +
            "COUNT( 1 ) \n" +
            "FROM\n" +
            "pclass_user_survey t1\n" +
            "INNER JOIN pclass_user_survey_item t2 ON t1.id = t2.user_survey_id \n" +
            "WHERE\n" +
            "t1.cellphone = #{cellphone} \n" +
            "AND t1.survey_id = #{surveyId}")
    Integer getSubmitCountByCellphoneAndSurveyId(@Param("surveyId")Integer surveyId,@Param("cellphone")String cellphone);

    @Select("SELECT\n" +
            "COUNT( 1 ) \n" +
            "FROM\n" +
            "pclass_user_survey t1\n" +
            "INNER JOIN pclass_user_survey_item t2 ON t1.id = t2.user_survey_id \n" +
            "WHERE\n" +
            "t1.cellphone = #{cellphone} \n" +
            "AND t1.pclass_code = #{classCode}")
    Integer getSubmitCountByCellphoneAndClassCode(@Param("classCode")String classCode,@Param("cellphone")String cellphone);
}
