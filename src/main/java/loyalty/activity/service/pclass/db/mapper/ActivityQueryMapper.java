package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.ActivityDetail;
import loyalty.activity.service.pclass.db.model.UserSignList;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21 下午 02:54
 * @describe
 */
@Mapper
public interface ActivityQueryMapper {
    @Select("SELECT t2.id,\n" +
            "       t2.channel,\n" +
            "       t2.topic,\n" +
            "       t2.content,\n" +
            "       t2.remark,\n" +
            "       t2.start_time,\n" +
            "       t2.end_time,\n" +
            "       t2.owner_id,\n" +
            "       t2.is_enabled,\n" +
            "       t3.province,\n" +
            "       t3.city,\n" +
            "       t3.address,\n" +
            "       t3.place,\n" +
            "       t3.longitude,\n" +
            "       t3.latitude,\n" +
            "       t1.classes_code,\n" +
            "       t1.expert_name,\n" +
            "       t1.expert_introduce,\n" +
            "       t1.course_name,\n" +
            "       t1.hotline,\n" +
            "       t1.is_deleted,\n" +
            "       t1.pclass_id\n" +
            "FROM pclass_info t1\n" +
            "       INNER JOIN activity t2 ON t2.id = t1.activity_id\n" +
            "       INNER JOIN activity_address t3 ON t3.activity_id = t2.id\n" +
            "WHERE t2.id = #{activityId}")
    @Results(id = "activity_detail", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "topic", column = "topic"),
            @Result(property = "content", column = "content"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "ownerId", column = "owner_id"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "courseName", column = "course_name"),
            @Result(property = "province", column = "province"),
            @Result(property = "city", column = "city"),
            @Result(property = "address", column = "address"),
            @Result(property = "place", column = "place"),
            @Result(property = "classesCode", column = "classes_code"),
            @Result(property = "expertName", column = "expert_name"),
            @Result(property = "expertIntroduce", column = "expert_introduce"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "hotline", column = "hotline"),
            @Result(property = "pclassId", column = "pclass_id"),
            @Result(property = "isDeleted", column = "is_deleted")
    })
    ActivityDetail getActivityDetailById(Integer activityId);

    @Select("SELECT t2.id,\n" +
            "       t2.channel,\n" +
            "       t2.topic,\n" +
            "       t2.content,\n" +
            "       t2.remark,\n" +
            "       t2.start_time,\n" +
            "       t2.end_time,\n" +
            "       t2.owner_id,\n" +
            "       t2.is_enabled,\n" +
            "       t3.province,\n" +
            "       t3.city,\n" +
            "       t3.address,\n" +
            "       t3.longitude,\n" +
            "       t3.latitude,\n" +
            "       t3.place,\n" +
            "       t1.classes_code,\n" +
            "       t1.expert_name,\n" +
            "       t1.expert_introduce,\n" +
            "       t1.course_name,\n" +
            "       t1.hotline,\n" +
            "       t1.is_deleted,\n" +
            "       t1.pclass_id,\n" +
            "       t4.registration_begin_date,\n" +
            "       t4.registration_end_date,\n" +
            "       t4.page_title,\n" +
            "       t4.cover_img,\n" +
            "       t4.top_img,\n" +
            "       t4.detail_img,\n" +
            "       t4.restrict_attendance,\n" +
            "       t4.poster_img,\n" +
            "       t4.poster_qr_img,\n" +
            "       t4.logo_img,\n" +
            "       CASE\n" +
            "           WHEN t4.STATUS = 0 THEN 0\n" +
            "           WHEN NOW() <t4.registration_begin_date THEN 1\n" +
            "           WHEN NOW() BETWEEN t4.registration_begin_date AND t4.registration_end_date THEN 2\n" +
            "\t\t WHEN t4.classes_code is null and NOW()<t2.start_time THEN 2 "+
            "           WHEN NOW() BETWEEN t2.start_time AND t2.end_time THEN 3\n" +
            "           WHEN NOW() > t2.end_time THEN 4\n" +
            "           ELSE 5\n" +
            "       END AS status\n" +
            "FROM pclass_info t1\n" +
            "       INNER JOIN activity t2 ON t2.id = t1.activity_id\n" +
            "       LEFT JOIN activity_address t3 ON t3.activity_id = t2.id\n" +
            "       LEFT JOIN pclass_ext t4 ON t1.classes_code = t4.classes_code\n" +
            "WHERE t1.classes_code = #{pclassCode}")
    @ResultMap(value = "pclass_detail")
    ActivityDetail getActivityDetailByPcalssId(String pclassCode);

    @Select("SELECT t2.id,\n" +
            "       t2.channel,\n" +
            "       t2.topic,\n" +
            "       t2.content,\n" +
            "       t2.remark,\n" +
            "       t2.start_time,\n" +
            "       t2.end_time,\n" +
            "       t2.owner_id,\n" +
            "       t2.is_enabled,\n" +
            "       t3.province,\n" +
            "       t3.city,\n" +
            "       t3.address,\n" +
            "       t3.longitude,\n" +
            "       t3.latitude,\n" +
            "       t3.place,\n" +
            "       t1.classes_code,\n" +
            "       t1.expert_name,\n" +
            "       t1.expert_introduce,\n" +
            "       t1.course_name,\n" +
            "       t1.hotline,\n" +
            "       t1.is_deleted,\n" +
            "       t1.pclass_id,\n" +
            "       CASE WHEN t4.id IS NOT NULL THEN true ELSE false END AS isSignIn,\n" +
            "       CASE WHEN t5.id IS NOT NULL THEN true ELSE false END AS isSignUp\n" +
            "FROM pclass_info t1\n" +
            "       INNER JOIN activity t2 ON t2.id = t1.activity_id\n" +
            "       LEFT JOIN activity_address t3 ON t3.activity_id = t2.id\n" +
            "       LEFT JOIN activity_user_signin t4 ON t4.activity_id = t2.id and t4.cellphone = #{cellPhone}\n" +
            "       LEFT JOIN activity_user_signup t5 ON t5.activity_id = t2.id and t5.cellphone = #{cellPhone}\n" +
            "WHERE t1.classes_code = #{pclassCode}")
    @ResultMap(value = "activity_detail")
    ActivityDetail getActivityDetailByPcalssIdAndCellPhone(@Param("pclassCode") String pclassCode ,@Param("cellPhone") String cellPhone);

    @Select("<script>" +
            "SELECT t2.id,\n" +
            "       t2.channel,\n" +
            "       t2.topic,\n" +
            "       t2.content,\n" +
            "       t2.remark,\n" +
            "       t2.start_time,\n" +
            "       t2.end_time,\n" +
            "       t2.owner_id,\n" +
            "       t2.is_enabled,\n" +
            "       t3.province,\n" +
            "       t3.city,\n" +
            "       t3.address,\n" +
            "       t3.place,\n" +
            "       t3.longitude,\n" +
            "       t3.latitude,\n" +
            "       t1.classes_code,\n" +
            "       t1.expert_name,\n" +
            "       t1.expert_introduce,\n" +
        "       t1.course_name,\n" +
        "       t1.hotline,\n" +
        "       t1.is_deleted,\n" +
        "       t1.pclass_id\n" +
        "FROM pclass_info t1\n" +
        "       INNER JOIN activity t2 ON t2.id = t1.activity_id\n" +
        "       INNER JOIN activity_address t3 ON t3.activity_id = t2.id\n" +
        "<where>\n" +
        "   <if test =\"ownerId != null and ownerId != ''\">\n" +
        "       AND t2.owner_id = #{ownerId}\n" +
        "   </if>\n" +
        "   AND t3.city LIKE CONCAT('%', #{city}, '%')\n" +
        "   AND t2.is_enabled = TRUE\n" +
        "   AND t1.is_deleted = FALSE\n" +
            " AND t2.channel = 'OUTSIDE_PCLASS'\n"+
      /*  "   AND (t1.pclass_property='MJT' or t1.pclass_type in (" +
        "       <foreach collection='channel' item='item' separator=','>" +
        "           #{item, jdbcType=VARCHAR}" +
        "       </foreach>) )" + */
         " <choose> " +
        "       <when test =\"listType == 'SIGN_IN'\">\n" +
        //            "           AND date(t2.start_time) = date(#{now}) AND date(t2.end_time) = date(#{now})\n" +
        "           AND date(t2.start_time) &lt;= date(#{now}) AND date(t2.end_time) &gt;= date(#{now})\n" +
        "       </when>" +
        "       <otherwise>" +
        "           AND date(t2.start_time) &gt; date(#{now})" +
        "       </otherwise>" +
        "   </choose>" +
        "</where>" +
        "</script>")
    @ResultMap(value = "activity_detail")
    List<ActivityDetail> getListByCity(@Param("city") String city,
                                       @Param("ownerId") String ownerId,
                                       @Param("channel") List<String> channel,
                                       @Param("listType") String listType,
                                       @Param("now") Date now);

    @Select("<script>" +
            "SELECT t1.id,\n" +
            "       IFNULL(t2.id, FALSE) is_sign_in,\n" +
            "       IFNULL(t3.id, FALSE) is_sign_up\n" +
            "FROM activity t1\n" +
            "       LEFT JOIN activity_user_signin t2 ON t1.id = t2.activity_id\n" +
            "  AND t2.cellphone = #{cellphone} \n" +
            "       LEFT JOIN activity_user_signup t3 ON t1.id = t3.activity_id\n" +
            "  AND t3.cellphone = #{cellphone} and (t3.is_enabled=1 or t3.is_enabled is null) \n" +
            "  where t1.id IN (" +
            "<foreach collection='list' item='item' separator=','>" +
            "#{item}" +
            "</foreach>" +
            ")" +
            "</script>")
    List<UserSignList> getSignList(@Param("list") List<Long> collect, @Param("cellphone") String cellphone);


    @Select("SELECT t2.id,\n" +
            "       t2.channel,\n" +
            "       t2.topic,\n" +
            "       t2.content,\n" +
            "       t2.remark,\n" +
            "       t2.start_time,\n" +
            "       t2.end_time,\n" +
            "       t2.owner_id,\n" +
            "       t2.is_enabled,\n" +
            "       t3.province,\n" +
            "       t3.city,\n" +
            "       t3.address,\n" +
            "       t3.longitude,\n" +
            "       t3.latitude,\n" +
            "       t3.place,\n" +
            "       t1.classes_code,\n" +
            "       t1.expert_name,\n" +
            "       t1.expert_introduce,\n" +
            "       t1.course_name,\n" +
            "       t1.hotline,\n" +
            "       t1.is_deleted,\n" +
            "       t1.pclass_id \n" +
            "FROM pclass_info t1\n" +
            "       INNER JOIN activity t2 ON t2.id = t1.activity_id\n" +
            "       LEFT JOIN activity_address t3 ON t3.activity_id = t2.id\n" +
            "       INNER JOIN activity_user_signin t4 ON t4.activity_id = t2.id \n" +
            "WHERE t4.cellphone = #{cellPhone} and t4.inviter_type!='TRS' order by t4.create_time desc")
    @ResultMap(value = "activity_detail")
    List<ActivityDetail> querySignInListByCellPhone(@Param("cellPhone") String cellPhone);

    @Select({"<script>",
            "SELECT\n" +
                    "\tt2.id,\n" +
                    "\tt2.channel,\n" +
                    "\tt2.topic,\n" +
                    "\tt2.content,\n" +
                    "\tt2.remark,\n" +
                    "\tt2.start_time,\n" +
                    "\tt2.end_time,\n" +
                    "\tt2.owner_id,\n" +
                    "\tt2.is_enabled,\n" +
                    "\tt4.province,\n" +
                    "\tt4.city,\n" +
                    "\tt4.address,\n" +
                    "\tt4.place,\n" +
                    "\tt4.longitude,\n" +
                    "\tt4.latitude,\n" +
                    "\tt1.classes_code,\n" +
                    "\tt1.expert_name,\n" +
                    "\tt1.expert_introduce,\n" +
                    "\tt1.course_name,\n" +
                    "\tt1.hotline,\n" +
                    "\tt1.is_deleted,\n" +
                    "\tt1.pclass_id,\n" +
                    "\tt3.registration_begin_date,\n" +
                    "\tt3.registration_end_date,\n" +
                    "\tt3.page_title,\n" +
                    "\tt3.cover_img,\n" +
                    "\tt3.top_img,\n" +
                    "\tt3.detail_img,\n" +
                    "\tt3.restrict_attendance,\n" +
                    "\tt3.poster_img,\n" +
                    "\tt3.poster_qr_img,\n" +
                    "\tt3.logo_img,\n" +
                    "CASE\n" +
                    "\t\t\n" +
                    "\t\tWHEN t3.STATUS = 0 THEN\n" +
                    "\t\t0 -- 最高优先级判断\n" +
                    "\t\t\n" +
                    "\t\tWHEN NOW() &lt; t3.registration_begin_date THEN\n" +
                    "\t\t1 \n" +
                    "\t\tWHEN NOW() BETWEEN t3.registration_begin_date \n" +
                    "\t\tAND t3.registration_end_date THEN\n" +
                    "\t\t\t2 \n" +
                    "\t\t WHEN t3.classes_code is null and NOW() &lt; t2.start_time THEN 2 "+
                    "\t\t\tWHEN NOW() BETWEEN t2.start_time \n" +
                    "\t\t\tAND t2.end_time THEN\n" +
                    "\t\t\t\t3 \n" +
                    "\t\t\t\tWHEN NOW() &gt; t2.end_time THEN\n" +
                    "\t\t\t\t4 ELSE 5 \n" +
                    "\t\t\tEND AS status \n" +
                    "\t\tFROM\n" +
                    "\t\t\tpclass_info t1\n" +
                    "\t\t\tINNER JOIN activity t2 ON t1.activity_id = t2.id -- 关联活动总表\n" +
                    "\t\t\tLEFT JOIN pclass_ext t3 ON t1.classes_code = t3.classes_code -- 关联扩展表\n" +
                    "\t\t\tINNER JOIN activity_address t4 ON t4.activity_id = t2.id \n" +
                    "\t\tWHERE\n" +
                    "\t\t\tt1.is_deleted = 0 -- 假设需要过滤已删除记录\n" +
                    "<if test=\"city != null and city != ''\">" +
                    "AND t4.city LIKE CONCAT('%', #{city}, '%')" +
                    "</if> " +
                    "<if test=\"status != null\">\n" +
                    "AND CASE\n" +
                    "WHEN t3.STATUS = 0 THEN 0 \n" +
                    "WHEN NOW() &lt; t3.registration_begin_date THEN 1 \n" +
                    "WHEN NOW() BETWEEN t3.registration_begin_date AND t3.registration_end_date THEN 2 \n" +
                    "\t\t WHEN t3.classes_code is null and NOW() &lt; t2.start_time THEN 2 "+
                    "WHEN NOW() BETWEEN t2.start_time AND t2.end_time THEN 3 \n" +
                    "WHEN NOW() &gt; t2.end_time THEN 4 \n" +
                    "ELSE 5 \n" +
                    "END = #{status}\n" +
                    "</if> " +
                    "ORDER BY " +
                    " t2.start_time DESC "
            ,"</script>"})
    @Results(id = "pclass_detail",value={
        @Result(property = "id", column = "id"),
        @Result(property = "channel", column = "channel"),
        @Result(property = "topic", column = "topic"),
        @Result(property = "content", column = "content"),
        @Result(property = "remark", column = "remark"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "endTime", column = "end_time"),
        @Result(property = "ownerId", column = "owner_id"),
        @Result(property = "isEnabled", column = "is_enabled"),
        @Result(property = "province", column = "province"),
        @Result(property = "city", column = "city"),
        @Result(property = "address", column = "address"),
        @Result(property = "place", column = "place"),
        @Result(property = "longitude", column = "longitude"),
        @Result(property = "latitude", column = "latitude"),
        @Result(property = "classesCode", column = "classes_code"),
        @Result(property = "expertName", column = "expert_name"),
        @Result(property = "expertIntroduce", column = "expert_introduce"),
        @Result(property = "courseName", column = "course_name"),
        @Result(property = "hotline", column = "hotline"),
        @Result(property = "isDeleted", column = "is_deleted"),
        @Result(property = "pclassId", column = "pclass_id"),
        @Result(property = "registrationBeginDate", column = "registration_begin_date"),
        @Result(property = "registrationEndDate", column = "registration_end_date"),
        @Result(property = "pageTitle", column = "page_title"),
        @Result(property = "coverImg", column = "cover_img"),
        @Result(property = "topImg", column = "top_img"),
        @Result(property = "detailImg", column = "detail_img"),
        @Result(property = "restrictAttendance", column = "restrict_attendance"),
        @Result(property = "posterImg", column = "poster_img"),
        @Result(property = "posterQrImg", column = "poster_qr_img"),
        @Result(property = "logoImg", column = "logo_img"),
        @Result(property = "status", column = "status")
    })
    List<ActivityDetail> getPclassListForSignUp(@Param("city") String city,@Param("status") Integer status);

    /**
     * 查询用户的报名列表
     * @param cellphone 用户手机号
     * @param signUpStatus 报名状态：0-全部，1-成功报名，2-已取消报名
     * @return 活动详情列表
     */
    @Select({"<script>",
            "SELECT\n" +
                    "\tt2.id,\n" +
                    "\tt2.channel,\n" +
                    "\tt2.topic,\n" +
                    "\tt2.content,\n" +
                    "\tt2.remark,\n" +
                    "\tt2.start_time,\n" +
                    "\tt2.end_time,\n" +
                    "\tt2.owner_id,\n" +
                    "\tt2.is_enabled,\n" +
                    "\tt4.province,\n" +
                    "\tt4.city,\n" +
                    "\tt4.address,\n" +
                    "\tt4.place,\n" +
                    "\tt4.longitude,\n" +
                    "\tt4.latitude,\n" +
                    "\tt1.classes_code,\n" +
                    "\tt1.expert_name,\n" +
                    "\tt1.expert_introduce,\n" +
                    "\tt1.course_name,\n" +
                    "\tt1.hotline,\n" +
                    "\tt1.is_deleted,\n" +
                    "\tt1.pclass_id,\n" +
                    "\tt3.registration_begin_date,\n" +
                    "\tt3.registration_end_date,\n" +
                    "\tt3.page_title,\n" +
                    "\tt3.cover_img,\n" +
                    "\tt3.top_img,\n" +
                    "\tt3.detail_img,\n" +
                    "\tt3.restrict_attendance,\n" +
                    "\tt3.poster_img,\n" +
                    "\tt3.poster_qr_img,\n" +
                    "\tt3.logo_img,\n" +
                    "CASE\n" +
                    "\t\t\n" +
                    "\t\tWHEN t3.STATUS = 0 THEN\n" +
                    "\t\t0 -- 最高优先级判断\n" +
                    "\t\t\n" +
                    "\t\tWHEN NOW() &lt; t3.registration_begin_date THEN\n" +
                    "\t\t1 \n" +
                    "\t\tWHEN NOW() BETWEEN t3.registration_begin_date \n" +
                    "\t\tAND t3.registration_end_date THEN\n" +
                    "\t\t\t2 \n" +
                    "\t\t WHEN t3.classes_code is null and NOW() &lt; t2.start_time THEN 2 "+
                    "\t\t\tWHEN NOW() BETWEEN t2.start_time \n" +
                    "\t\t\tAND t2.end_time THEN\n" +
                    "\t\t\t\t3 \n" +
                    "\t\t\t\tWHEN NOW() &gt; t2.end_time THEN\n" +
                    "\t\t\t\t4 ELSE 5 \n" +
                    "\t\t\tEND AS status \n" +
                    "\t\tFROM\n" +
                    "\t\t\tpclass_info t1\n" +
                    "\t\t\tINNER JOIN activity t2 ON t1.activity_id = t2.id -- 关联活动总表\n" +
                    "\t\t\tLEFT JOIN pclass_ext t3 ON t1.classes_code = t3.classes_code -- 关联扩展表\n" +
                    "\t\t\tINNER JOIN activity_address t4 ON t4.activity_id = t2.id \n" +
                    "\t\t\tINNER JOIN activity_user_signup t5 ON t5.activity_id = t2.id AND t5.cellphone = #{cellphone} \n" +
                    "\t\tWHERE\n" +
                    "\t\t\tt1.is_deleted = 0 -- 假设需要过滤已删除记录\n" +
                    "<if test=\"signUpStatus != null\">" +
                    "AND t5.is_enabled = #{signUpStatus}" +
                    "</if> " +
                    "ORDER BY " +
                    " t2.start_time DESC "
            ,"</script>"})
    @ResultMap(value = "pclass_detail")
    List<ActivityDetail> getMySignUpList(@Param("cellphone") String cellphone, @Param("signUpStatus") Integer signUpStatus);
}
