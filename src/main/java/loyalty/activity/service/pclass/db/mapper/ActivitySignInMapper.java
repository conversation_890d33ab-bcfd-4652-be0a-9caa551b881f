package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.common.dto.SignInHistory;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.Activity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/2 下午 03:04
 * @describe
 */
//todo: to howard team: 没事别用script，自查全部script标签，把非必要的去掉。理由看前面的todo
@Mapper
public interface ActivitySignInMapper {
    @Select("<script>" +
            "SELECT \n" +
            "    t2.id,\n" +
            "    t2.start_time,\n" +
            "    t2.end_time\n" +
            "FROM activity_user_signin t1\n" +
            "       INNER JOIN activity t2 ON t1.activity_id = t2.id\n" +
            "       LEFT JOIN activity_address t3 ON t2.id = t3.activity_id\n" +
            "WHERE t1.cellphone = #{cellphone}\n" +
            "  AND t3.city LIKE CONCAT('%', #{city}, '%')\n" +
            "  AND t2.end_time &gt;= #{date}\n" +
            "  AND t2.channel = #{type} " +
            "LIMIT 1" +
            "</script>")
    @Results(value = {
        @Result(property = "id", column = "id"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "endTime", column = "end_time")
    })
    List<Activity> getStartClassByCellphone(@Param("cellphone") String cellphone,
                                            @Param("date") Date date,
                                            @Param("city") String city,
                                            @Param("type") String type);


    @Select("SELECT\n" +
            "IF\n" +
            "((\n" +
            "SELECT\n" +
            "COUNT( 1 ) \n" +
        "FROM\n" +
        "pclass_info t1\n" +
        "INNER JOIN activity_user_signin t2 ON t1.activity_id = t2.activity_id \n" +
        "WHERE\n" +
        "t1.classes_code = #{classCode} \n" +
        "AND t2.cellphone = #{cellphone} \n" +
        ")> 0,\n" +
        "1,\n" +
        "0)")
    Boolean getSignInCondition(@Param("classCode") String classCode, @Param("cellphone") String cellphone);

    @Select("<script>" +
        "SELECT t1.id\n" +
        "FROM `activity_user_signin` t1\n" +
        "       INNER JOIN activity t2 ON t1.activity_id = t2.id\n" +
        "WHERE cellphone = #{cellphone}\n" +
        "  AND trigger_time &gt;= DATE_SUB(#{date}, INTERVAL 1 HOUR)\n" +
        "  AND t2.channel = #{type}" +
        "LIMIT 1" +
        "</script>")
    Long getStartClassByCellphoneV2(@Param("cellphone") String cellphone,
                                    @Param("date") Date date,
                                    @Param("type") String type);

    @Select("SELECT\n" +
            "\ts.activity_id as activityId,\n" +
            "\tp.classes_code as classesCode,\n" +
            "\ta.channel,\n" +
            "\tp.activity_type as activityType,\n" +
            "\tad.province,\n" +
            "\tad.city,\n" +
            "\tDATE( s.trigger_time ) AS signInTime \n" +
            "FROM\n" +
            "\tactivity_user_signin s\n" +
            "\tLEFT JOIN activity a ON s.activity_id = a.id\n" +
            "\tLEFT JOIN activity_address ad ON ad.activity_id = a.id \n" +
            "\tleft join pclass_info p on p.activity_id=a.id\n" +
            "WHERE\n" +
            "\ts.cellphone = #{cellphone} \n" +
            "\tAND YEAR ( s.trigger_time ) = YEAR (\n" +
            "\tCURDATE()) \n" +
            "\tAND MONTH ( s.trigger_time ) = MONTH (\n" +
            "\tCURDATE());")
    List<SignInHistory> getSignInHistoryForCurrentMonth(@Param("cellphone") String cellphone);

    @Select("SELECT\n" +
            "\ts.activity_id as activityId,\n" +
            "\tp.classes_code as classesCode,\n" +
            "\ta.channel,\n" +
            "\tp.activity_type as activityType,\n" +
            "\tad.province,\n" +
            "\tad.city,\n" +
            "\tDATE( s.trigger_time ) AS signInTime \n" +
            "FROM\n" +
            "\tactivity_user_signin s\n" +
            "\tLEFT JOIN activity a ON s.activity_id = a.id\n" +
            "\tLEFT JOIN activity_address ad ON ad.activity_id = a.id \n" +
            "\tleft join pclass_info p on p.activity_id=a.id\n" +
            "WHERE\n" +
            "\ts.cellphone = #{cellphone} and s.trigger_time>=#{beginTime} \n"
         )
    List<SignInHistory> getSignInHistory(@Param("cellphone") String cellphone,@Param("beginTime") String beginTime);

    /**
     * 取消用户报名活动
     * 更新activity_user_signup表中的is_enabled字段为false
     *
     * @param activityId 活动ID
     * @param cellphone 用户手机号码
     * @return 更新的记录数
     */
    @Update("UPDATE activity_user_signup SET is_enabled = 0, update_time = NOW() " +
            "WHERE activity_id = #{activityId} AND cellphone = #{cellphone}")
    int cancelSignUp(@Param("activityId") Long activityId,
                     @Param("cellphone") String cellphone);
}
