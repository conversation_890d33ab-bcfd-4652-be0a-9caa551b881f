package loyalty.activity.service.pclass.db.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InsertSignUpBaseInfo {
    private Integer activityId;
    private Integer attendance;
    private Date babyBirthday;
    private String cellphone;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String openId;
    private String pnecId;
    private Date triggerTime;
    private String unionId;
    private String userName;
    private String remark;
    private Integer babyStatus;
    private String inputCellphone;
    private String creator;
}
