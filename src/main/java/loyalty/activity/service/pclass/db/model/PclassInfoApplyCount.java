package loyalty.activity.service.pclass.db.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 下午 12:26
 * @describe
 */
@Data
public class PclassInfoApplyCount {
    private Long id;
    private Date startTime;
    private Date endTime;
    private Boolean isEnabled;
    private Integer limitCount;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String province;
    private String city;
    private String status;
    private String ownerId;
    private Boolean isDeleted;
    private Integer applyCount;
    private String pclassCode;
    private String activityType;
    private String pclassType;
    private Map extAttr;



    private Boolean isOnline;
    public PclassInfoApplyCount() {
        //limitCount = 0;
        applyCount = 0;
        latitude = BigDecimal.ZERO;
        longitude = BigDecimal.ZERO;
    }

    public void setExtAttr(String key,String value){
        if(extAttr==null){
            extAttr=new HashMap();
        }
        if(StringUtils.isBlank(value)){
            return;
        }
        extAttr.put(key,value);
    }
}
