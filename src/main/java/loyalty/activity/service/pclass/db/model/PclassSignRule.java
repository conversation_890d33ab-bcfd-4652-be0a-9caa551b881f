package loyalty.activity.service.pclass.db.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PclassSignRule {
    private Integer id;
    private String pclassType;
    private String ruleType;
    private String dimension;
    private Integer limitCount;
    private String province;
    private String city;
    private String whiteList;
    private String ext;
    private String tip;
    private Boolean isEnabled;
    private Date startTime;
    private Date endTime;
    private Date createTime;
    private Date updateTime;

    // 省略getter和setter方法
}
