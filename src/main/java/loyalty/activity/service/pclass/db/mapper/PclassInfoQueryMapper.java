package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.db.model.PclassQrcodeInfo;
import org.apache.ibatis.annotations.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 11:59
 * @describe
 */
@Mapper
public interface PclassInfoQueryMapper {
    @Select("SELECT t1.id,\n" +
            "       t1.start_time,\n" +
            "       t2.classes_code,\n" +
            "       t1.end_time,\n" +
            "       t1.owner_id,\n" +
            "       t1.is_enabled,\n" +
            "       t2.limit_count,\n" +
            "       t2.status,\n" +
            "       t2.is_deleted,\n" +
            "       IFNULL(COUNT(t3.id), 0) apply_count\n" +
            "FROM activity t1\n" +
            "       INNER JOIN pclass_info t2 ON t1.id = t2.activity_id\n" +
            "       LEFT JOIN activity_user_signup t3 ON t3.activity_id = t1.id AND t3.cellphone != #{cellphone}\n" +
            "       LEFT JOIN pclass_user_signup_form t4 ON t3.id = t4.signup_id\n" +
            "  AND t4.`status` != '已取消'\n" +
            "WHERE t1.id = #{id}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "ownerId", column = "owner_id"),
            @Result(property = "status", column = "status"),
            @Result(property = "pclassCode", column = "classes_code"),
            @Result(property = "limitCount", column = "limit_count"),
            @Result(property = "applyCount", column = "apply_count"),
            @Result(property = "isDeleted", column = "is_deleted")
    })
    PclassInfoApplyCount getPclassInfoApplyCountById(@Param("id") Integer id, @Param("cellphone") String cellphone);

    @Select("SELECT t1.id,\n" +
            "       t1.start_time,\n" +
            "       t2.classes_code,\n" +
            "       t2.activity_type,\n" +
            "       t2.pclass_type,\n" +
        "       t1.end_time,\n" +
        "       t1.owner_id,\n" +
        "       t1.is_enabled,\n" +
        "       t1.is_enabled,\n" +
        "       t1.is_enabled,\n" +
        "       t2.limit_count,\n" +
        "       t4.longitude,\n" +
        "       t4.latitude,\n" +
        "       t4.province,\n" +
        "       t4.city,\n" +
        "       t2.status,\n" +
        "       t2.is_deleted,\n" +
        "       t2.is_online_activity,\n" +
        "       IFNULL(COUNT(t3.id), 0) apply_count\n" +
        "FROM activity t1\n" +
        "       INNER JOIN pclass_info t2 ON t1.id = t2.activity_id\n" +
        "       LEFT JOIN activity_user_signin t3 ON t3.activity_id = t1.id\n" +
        "       LEFT JOIN activity_address t4 ON t4.activity_id = t1.id\n" +
        "WHERE t1.id = #{id}")
    @Results(value = {
            @Result(property = "id", column = "id"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "activityType", column = "activity_type"),
        @Result(property = "pclassType", column = "pclass_type"),
        @Result(property = "endTime", column = "end_time"),
        @Result(property = "isEnabled", column = "is_enabled"),
        @Result(property = "ownerId", column = "owner_id"),
        @Result(property = "longitude", column = "longitude"),
        @Result(property = "latitude", column = "latitude"),
        @Result(property = "province", column = "province"),
        @Result(property = "city", column = "city"),
        @Result(property = "status", column = "status"),
        @Result(property = "pclassCode", column = "classes_code"),
        @Result(property = "limitCount", column = "limit_count"),
        @Result(property = "applyCount", column = "apply_count"),
        @Result(property = "isOnline", column = "is_online_activity"),
        @Result(property = "isDeleted", column = "is_deleted")
    })
    PclassInfoApplyCount getPclassInfoSignInCountById(@Param("id") Integer id);

    @Select("SELECT\n" +
            "t1.course_name,\n" +
            "t1.qrcode_url,\n" +
            "t1.qrcode_expire_time,\n" +
            "t2.start_time,\n" +
            "t2.end_time, \n" +
            "t1.owner_employee_num,\n" +
            "t1.owner_name, \n" +
            "t1.owner_mobile \n" +
            "FROM\n" +
            "pclass_info t1\n" +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id \n" +
            "WHERE\n" +
            "t1.classes_code = #{classCode}")
    @Results(value = {
            @Result(property = "courseName", column = "course_name"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "qrcodeUrl", column = "qrcode_url"),
            @Result(property = "pneid", column = "owner_employee_num"),
            @Result(property = "pneName", column = "owner_name"),
            @Result(property = "pneMobile", column = "owner_mobile"),
            @Result(property = "qrcodeExpireTime", column = "qrcode_expire_time")
    })
    PclassQrcodeInfo getPclassQrcodeInfoByClassCode(@Param("classCode") String classCode);


    @Select("SELECT\n" +
            "t1.classes_code,\n" +
            "t1.course_name,\n" +
            "t1.qrcode_url,\n" +
            "t1.qrcode_expire_time,\n" +
            "t2.start_time,\n" +
            "t2.end_time, \n" +
            "t2.channel \n" +
            "FROM\n" +
            "pclass_info t1\n" +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id \n" +
            "WHERE\n" +
            "t2.id = #{id}")
    @Results(value = {
            @Result(property = "courseName", column = "course_name"),
            @Result(property = "classesCode", column = "classes_code"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "qrcodeUrl", column = "qrcode_url"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "qrcodeExpireTime", column = "qrcode_expire_time")
    })
    PclassQrcodeInfo getPclassQrcodeInfoById(@Param("id") Integer id);



    @Update("UPDATE pclass_info \n" +
            "SET qrcode_url = #{qrcodeUrl},\n" +
            "qrcode_expire_time = #{qrcodeExpireTime} \n" +
            "WHERE\n" +
            "classes_code = #{classCode}")
    Integer updatePclassQrcodeInfo(@Param("qrcodeUrl") String qrcodeUrl,
                                   @Param("qrcodeExpireTime") Date qrcodeExpireTime,
                                   @Param("classCode") String classCode);

    @Select("SELECT\n" +
            "t3.user_id \n" +
            "FROM\n" +
            "pclass_info t1\n" +
            "INNER JOIN activity t2 ON t1.activity_id = t2.id\n" +
            "INNER JOIN activity_owner_rel t3 ON t2.id = t3.activity_id\n" +
            "WHERE\n" +
            "t1.classes_code = #{classCode}")
    String getOwnerContactQrcode(@Param("classCode") String classCode);


    @Select("SELECT COUNT(1) FROM activity_user_signin  WHERE activity_id=#{activityId}")
    Integer getSiginInCount(@Param("activityId") String activityId);


    @Select("SELECT t1.id,\n" +
            "       t1.start_time,\n" +
            "       t2.classes_code,\n" +
            "       t1.end_time,\n" +
            "       t1.owner_id,\n" +
            "       t1.is_enabled,\n" +
            "       t2.limit_count,\n" +
            "       t2.status,\n" +
            "       t2.is_deleted,\n" +
            "       IFNULL(COUNT(t3.id), 0) apply_count\n" +
            "FROM activity t1\n" +
            "       INNER JOIN pclass_info t2 ON t1.id = t2.activity_id\n" +
            "       LEFT JOIN activity_user_signup t3 ON t3.activity_id = t1.id AND t3.cellphone != #{cellphone}\n" +
            "       LEFT JOIN pclass_user_signup_form t4 ON t3.id = t4.signup_id\n" +
            "  AND t4.`status` != '已取消'\n" +
            "WHERE t2.classes_code = #{code}")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "ownerId", column = "owner_id"),
            @Result(property = "status", column = "status"),
            @Result(property = "pclassCode", column = "classes_code"),
            @Result(property = "limitCount", column = "limit_count"),
            @Result(property = "applyCount", column = "apply_count"),
            @Result(property = "isDeleted", column = "is_deleted")
    })
    PclassInfoApplyCount getPclassInfoApplyCountByCode(@Param("code") String code, @Param("cellphone") String cellphone);
}
