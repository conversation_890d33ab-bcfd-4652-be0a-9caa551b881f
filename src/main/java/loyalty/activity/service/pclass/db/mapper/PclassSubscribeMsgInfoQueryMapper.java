package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.SubscriptionStatus;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 下午 12:02
 * @describe
 */
@Mapper
public interface PclassSubscribeMsgInfoQueryMapper {

    @Select("SELECT\n" +
            "\tt1.subscribe_type \n" +
            "FROM\n" +
            "\tpclass_subscribe_msg_info t1\n" +
            "\tINNER JOIN wxmpp_subscribe_msg_info t2 ON t1.wxmpp_subscribe_msg_info_id = t2.id \n" +
            "WHERE\n" +
            "\tt1.cellphone = #{cellphone} \n" +
            "\tAND t1.pclass_code = #{pclassCode} \n" +
            "\tAND t2.subscribe_status = 'accept' \n" +
            "GROUP BY\n" +
            "\tt1.subscribe_type")
    List<String> getAcceptSubscriptionCount(@Param("pclassCode") String pclassCode,
                                            @Param("cellphone") String cellphone);

    @Select("SELECT" +
            "       t1.subscribe_type,\n" +
            "       max( t1.is_send ) is_send,\n" +
            "       IF( Max( t2.subscribe_status ) IS NOT NULL, TRUE, FALSE )  subscribe_status\n" +
            "FROM `pclass_subscribe_msg_info` t1\n" +
            "       LEFT JOIN wxmpp_subscribe_msg_info t2 ON t1.wxmpp_subscribe_msg_info_id = t2.id\n" +
            "  AND t2.subscribe_status = 'accept'\n" +
            "WHERE t1.cellphone = #{cellphone}\n" +
            "  AND t1.pclass_code = #{pclassCode}\n" +
            "GROUP BY t1.subscribe_type")
    @Results(value = {
            @Result(column = "subscribe_type", property = "type"),
            @Result(column = "is_send", property = "isSend"),
            @Result(column = "subscribe_status", property = "isAccept")
    })
    List<SubscriptionStatus> getSubscriptionStatus(@Param("cellphone") String cellphone,
                                                   @Param("pclassCode") String pclassCode);
}
