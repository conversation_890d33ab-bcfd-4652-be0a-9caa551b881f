package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.PclassSignRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PclassSignRuleMapper {

    @Select("select * from activity_sign_rule where start_time<=now() and end_time>=now() and is_enabled=1 ORDER BY ext ASC")
    @ResultType(PclassSignRule.class)
    List<PclassSignRule> selectAll();
}
