package loyalty.activity.service.pclass.db.model;

import lombok.Data;

@Data
public class AssociationSurveyInfo {

    //协会信息
    private Integer associationId;
    private String channel;
    private String associationName;
    private String summary;

    //问卷信息
    private Integer surveyId;
    private String surveyName;
    private String topic;

    //问卷题目
    private Integer topicId;
    private String topicTitle;
    private String checkType;
    private Integer topicSequence;
    private Integer score;
    private Boolean isRequired;

    //问卷题目选项
    private Integer itemId;
    private String itemTitle;
    private String optionType;
    private String itemSequence;
    private Boolean isCorrectOption;
}
