package loyalty.activity.service.pclass.db.mapper;

import loyalty.activity.service.pclass.db.model.SignUpHistory;
import loyalty.activity.service.pclass.domain.request.PnecQrCodeRequest;
import loyalty.activity.service.pclass.domain.request.SignUpHistoryRequest;
import loyalty.activity.service.pclass.domain.request.UserApplyRequest;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.domain.response.UserSignUpResponse;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 上午 11:59
 * @describe
 */
@Mapper
public interface PclassApplyQueryMapper {

    //todo: to howard team: 自查，删除没用到的方法，且，\n还是空格，对齐方式统一一下
    @Select("SELECT\n" +
            "  t1.id,\n" +
            "  t3.qr_code\n" +
            "FROM\n" +
            "  activity_user_signup t1\n" +
            "    INNER JOIN pclass_user_signup_form t2 ON t1.id = t2.signup_id\n" +
            "    LEFT JOIN pclass_pnec_contact_way t3 ON t2.pnec_id = t3.user_id" +
            "WHERE t1.cellphone = #{cellphone}\n" +
            "  AND t1.activity_id = #{activityId}\n" +
            "LIMIT 1")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "qrCode", column = "qr_code")
    })
    UserSignUpResponse getApplyHistoryQrCode(UserApplyRequest userApplyRequest);

    @Select("SELECT t1.id,\n" +
            "       t2.qr_code\n" +
            "FROM activity_user_signin t1\n" +
            "       LEFT JOIN pclass_pnec_contact_way t2 ON t1.inviter_id = t2.user_id\n" +
            "WHERE t1.activity_id = #{activityId} AND cellphone = #{cellphone}")
    UserSignInResponse getPnecQrCode(PnecQrCodeRequest request);

    @Select("SELECT qr_code\n" +
            "FROM pclass_pnec_contact_way\n" +
            "WHERE user_id = #{pnecId}")
    String getQrCodeByUserId(String pnecId);

    @Select("SELECT t1.id,\n" +
            "       t2.qr_code\n" +
            "FROM activity_user_signin t1\n" +
            "       LEFT JOIN pclass_pnec_contact_way t2 ON t2.user_id = t1.inviter_id\n" +
            "WHERE t1.cellphone = #{cellphone}\n" +
            "  AND t1.activity_id = #{activityId}\n" +
            "LIMIT 1")
    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "qrCode", column = "qr_code")
    })
    UserSignInResponse getSignInHistoryQrCode(@Param("cellphone") String cellphone,
                                              @Param("activityId") Integer activityId);

    @Select("SELECT t2.id,\n" +
            "       t2.cellphone,\n" +
            "       t2.remark,\n" +
            "       t3.user_name,\n" +
            "       t3.baby_birthday,\n" +
            "       t3.baby_status,\n" +
            "       t3.pnec_id,\n" +
            "       t3.attendance\n" +
            "FROM pclass_info t1\n" +
            "       INNER JOIN activity_user_signup t2 ON t1.activity_id = t2.activity_id\n" +
            "       INNER JOIN pclass_user_signup_form t3 ON t2.id = t3.signup_id\n" +
            "WHERE t2.cellphone = #{cellphone}\n" +
            "  AND t1.classes_code = #{classesCode}")
    @Results(value = {
            @Result(property = "signUpId", column = "id"),
            @Result(property = "attendance", column = "attendance"),
            @Result(property = "babyBirthday", column = "baby_birthday"),
            @Result(property = "babyStatus", column = "baby_status"),
            @Result(property = "cellphone", column = "cellphone"),
            @Result(property = "pnecId", column = "pnec_id"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "userName", column = "user_name")
    })
    SignUpHistory getSignUpHistory(SignUpHistoryRequest signUpHistoryRequest);
}
