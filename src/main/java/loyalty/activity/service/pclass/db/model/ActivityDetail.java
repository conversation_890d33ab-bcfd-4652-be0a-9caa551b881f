package loyalty.activity.service.pclass.db.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21 下午 02:50
 * @describe
 */
@Data
public class ActivityDetail {
    private String address;
    private String city;
    private String classesCode;
    private String content;
    private String channel;
    private Date endTime;
    private String expertIntroduce;
    private String expertName;
    private String hotline;
    private Long id;
    private Boolean isSignIn;
    private Boolean isSignUp;
    private Boolean isDeleted;
    private Boolean isEnabled;
    private String ownerId;
    private String place;
    private String province;
    private String remark;
    private Date startTime;
    private String topic;
    private String courseName;
    //private String otherCourseName;
    private String pclassId;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String classesType;

    @ApiModelProperty(value = "开始报名时间", required = true)
    private String registrationBeginDate;

    @ApiModelProperty(value = "报名结束时间", required = true)
    private String registrationEndDate;

    @ApiModelProperty(value = "活动状态(0已取消 1执行中)", required = true)
    private Integer status;

    @ApiModelProperty(value = "页面标题", required = true)
    private String pageTitle;

    @ApiModelProperty(value = "列表缩略图", required = true)
    private String coverImg;

    @ApiModelProperty(value = "详情头部背景图", required = true)
    private String topImg;

    @ApiModelProperty(value = "活动详情背景图", required = true)
    private String detailImg;

    @ApiModelProperty("限制报名人数")
    private Integer restrictAttendance;

    @ApiModelProperty("海报图")
    private String posterImg;

    @ApiModelProperty("二维码海报图")
    private String posterQrImg;

    @ApiModelProperty("logo图片")
    private String logoImg;

    public ActivityDetail() {
        isSignIn = false;
        isSignUp = false;
    }
}
