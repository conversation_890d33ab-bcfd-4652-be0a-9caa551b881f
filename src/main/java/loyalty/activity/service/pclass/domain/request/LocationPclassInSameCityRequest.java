package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 02:50
 * @describe
 */
@Data
public class LocationPclassInSameCityRequest {
    @ApiModelProperty(value = "城市")
    @NotBlank(message = "城市不能为空")
    private String city;
    @ApiModelProperty(value = "课程id")
    @NotNull(message = "id不能为空")
    private String pclassId;
}
