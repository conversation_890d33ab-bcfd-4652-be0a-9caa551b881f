package loyalty.activity.service.pclass.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 上午 10:02
 * @describe
 */
@Data
public class UserSignInResponse {
    @ApiModelProperty(value = "签到记录id")
    private Integer id;
    @ApiModelProperty(value = "课程码")
    private String activityCode;

    @ApiModelProperty(value = "企业微信二维码")
    private String qrCode;
    @ApiModelProperty(value = "是否是添加nc")
    private Boolean isAdd;
    @ApiModelProperty(value = "是否已签到")
    private Boolean isSigned;
}
