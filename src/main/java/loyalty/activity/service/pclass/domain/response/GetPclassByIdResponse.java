package loyalty.activity.service.pclass.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pclass.db.model.ActivityDetail;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 04:31
 * @describe
 */
@Data
@ApiModel
public class GetPclassByIdResponse {
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "课程编码")
    private String classesCode;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    @ApiModelProperty(value = "讲师介绍")
    private String expertIntroduce;
    @ApiModelProperty(value = "课程讲师")
    private String expertName;
    @ApiModelProperty(value = "咨询热线")
    private String hotline;
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "是否已删除")
    private Boolean isDeleted;
    @ApiModelProperty(value = "是否上线")
    private Boolean isEnabled;
    @ApiModelProperty(value = "负责人id")
    private String ownerId;
    @ApiModelProperty(value = "课程地点")
    private String place;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "备注,支持html格式")
    private String remark;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "课程主题")
    private String topic;

    public GetPclassByIdResponse(ActivityDetail activityDetail) {
        this.address = activityDetail.getAddress();
        this.city = activityDetail.getCity();
        this.classesCode = activityDetail.getClassesCode();
        this.content = activityDetail.getContent();
        this.courseName = activityDetail.getCourseName();
        this.endTime = activityDetail.getEndTime();
        this.expertIntroduce = activityDetail.getExpertIntroduce();
        this.expertName = activityDetail.getExpertName();
        this.hotline = activityDetail.getHotline();
        this.id = activityDetail.getId();
        this.isDeleted = activityDetail.getIsDeleted();
        this.isEnabled = activityDetail.getIsEnabled();
        this.ownerId = activityDetail.getOwnerId();
        this.place = activityDetail.getPlace();
        this.province = activityDetail.getProvince();
        this.remark = activityDetail.getRemark();
        this.startTime = activityDetail.getStartTime();
        this.topic = activityDetail.getTopic();
    }
}
