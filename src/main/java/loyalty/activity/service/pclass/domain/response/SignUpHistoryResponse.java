package loyalty.activity.service.pclass.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pclass.db.model.SignUpHistory;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28 下午 05:15
 * @describe
 */
@Data
public class SignUpHistoryResponse {
    @ApiModelProperty(value = "出席人数")
    private Integer attendance;
    @ApiModelProperty(value = "婴儿生日")
    private Date babyBirthday;
    @ApiModelProperty(value = "0备孕 1已有宝宝")
    private Integer babyStatus;
    @ApiModelProperty(value = "手机号")
    private String cellphone;
    @ApiModelProperty(value = "负责人id")
    private String pnecId;
    @ApiModelProperty(value = "留言")
    private String remark;
    @ApiModelProperty(value = "签到id")
    private Integer signUpId;
    @ApiModelProperty(value = "用户名")
    private String userName;

    public SignUpHistoryResponse() {
    }

    public SignUpHistoryResponse(SignUpHistory signUpHistory) {
        attendance = signUpHistory.getAttendance();
        babyBirthday = signUpHistory.getBabyBirthday();
        babyStatus = signUpHistory.getBabyStatus();
        cellphone = signUpHistory.getCellphone();
        pnecId = signUpHistory.getPnecId();
        remark = signUpHistory.getRemark();
        signUpId = signUpHistory.getSignUpId();
        userName = signUpHistory.getUserName();
    }
}
