package loyalty.activity.service.pclass.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pnec.db.model.PclassResearchInfo;

@Data
public class PclassResearchResponse {
    @ApiModelProperty("ID")
    private Integer id;
    @ApiModelProperty("HCPId")
    private String hcpId;
    @ApiModelProperty("HCP名称")
    private String hcpName;
    @ApiModelProperty("调研ID")
    private String researchId;
    @ApiModelProperty("调研机构名称")
    private String researchInstitutionName;
    @ApiModelProperty("统计月份")
    private String month;
    @ApiModelProperty("调研二维码")
    private String qrcodeUrl;

    public PclassResearchResponse(PclassResearchInfo pclassResearchInfo) {
        if (pclassResearchInfo!=null){
            this.id = pclassResearchInfo.getId();
            this.hcpId = pclassResearchInfo.getHcpId();
            this.hcpName = pclassResearchInfo.getHcpName();
            this.researchId = pclassResearchInfo.getResearchId();
            this.researchInstitutionName = pclassResearchInfo.getResearchInstitutionName();
            this.month = pclassResearchInfo.getMonth();
            this.qrcodeUrl = pclassResearchInfo.getQrcodeUrl();
        }
    }
}
