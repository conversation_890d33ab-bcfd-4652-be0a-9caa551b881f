package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28 下午 05:12
 * @describe
 */
@Data
public class SignInHistoryRequest{
    @ApiModelProperty(value = "电话")
    @NotBlank(message = "手机号不能为空")
    private String cellphone;

    private Integer pageNum=1;

    private Integer pageSize=10;
}
