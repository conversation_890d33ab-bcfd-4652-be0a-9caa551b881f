package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AssociationSurveySubmitRequest {

    @ApiModelProperty("问卷id")
    private Integer surveyId;
    @ApiModelProperty("课程编码")
    private String pclassCode;
    @ApiModelProperty("用户手机号码")
    private String cellphone;
    @ApiModelProperty("unionid")
    private String unionid;
    @ApiModelProperty("openid")
    private String openid;


    @ApiModelProperty("问卷题目列表")
    private List<UserSurveyItem> userSurveyItemList;

    @Data
    public static class UserSurveyItem{
        @ApiModelProperty("题目id")
        private Integer topicId;
        @ApiModelProperty("用户回答内容或者选项")
        private String userAnswer;
    }

}
