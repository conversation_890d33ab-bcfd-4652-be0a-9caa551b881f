package loyalty.activity.service.pclass.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 上午 10:02
 * @describe
 */
@Data
public class UserSignUpResponse {
    @ApiModelProperty(value = "报名记录id")
    private Integer id;
    @ApiModelProperty(value = "企业微信二维码")
    private String qrCode;
    @ApiModelProperty(value = "是否是添加nc")
    private Boolean isAdd;
}
