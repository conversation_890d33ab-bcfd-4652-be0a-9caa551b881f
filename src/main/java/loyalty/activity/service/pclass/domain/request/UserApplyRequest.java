package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 上午 10:56
 * @describe
 */
@Data
public class UserApplyRequest {
    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程id不能为空")
    private Integer activityId;
    @ApiModelProperty(value = "出席人数")
    @Min(value = 1, message = "最少出席一人")
    private Integer attendance;
    @ApiModelProperty(value = "婴儿生日")
//    @NotNull(message = "婴儿生日不能为空")
    private Date babyBirthday;
    @ApiModelProperty(value = "宝宝状态")
    private Integer babyStatus;
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号格式错误")
    private String cellphone;
    @ApiModelProperty(value = "输入框手机号")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号格式错误")
    private String inputCellphone;
    @ApiModelProperty(value = "当前定位-纬度")
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;
    @ApiModelProperty(value = "当前定位-经度")
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;
    @ApiModelProperty(value = "openId")
//    @NotBlank(message = "openId不能为空")
    private String openId;
    @ApiModelProperty(value = "负责人id")
    private String pnecId;
    @ApiModelProperty(value = "unionId")
//    @NotBlank(message = "unionId不能为空")
    private String unionId;
    @ApiModelProperty(value = "用户名")
    @NotNull(message = "用户名")
    private String userName;
    @ApiModelProperty(value = "留言")
    @Size(max = 30, message = "留言最长不得超过30个字")
    private String remark;
}
