package loyalty.activity.service.pclass.domain.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pclass.db.model.ActivityDetail;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 03:11
 * @describe
 */
@Data
@ApiModel
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class PclassInfoResponse {
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "课程编码")
    private String classesCode;
    @ApiModelProperty(value = "课程类型")
    private String classesType;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    //@ApiModelProperty(value = "其他课程名称")
   // private String otherCourseName;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    @ApiModelProperty(value = "讲师介绍")
    private String expertIntroduce;
    @ApiModelProperty(value = "课程讲师")
    private String expertName;
    @ApiModelProperty(value = "咨询热线")
    private String hotline;
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "是否已签到")
    private Boolean isSignIn;
    @ApiModelProperty(value = "是否已报名")
    private Boolean isSignUp;
    @ApiModelProperty(value = "是否已删除")
    private Boolean isDeleted;
    @ApiModelProperty(value = "是否上线")
    private Boolean isEnabled;
    @ApiModelProperty(value = "负责人id")
    private String ownerId;
    @ApiModelProperty(value = "课程地点")
    private String place;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "备注,支持html格式")
    private String remark;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "课程主题")
    private String topic;
    @ApiModelProperty(value = "pclassId")
    private String pclassId;
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;
    @ApiModelProperty(value = "课程NC二维码")
    private String ncQrcode;
    
    @ApiModelProperty(value = "开始报名时间")
    private String registrationBeginDate;
    
    @ApiModelProperty(value = "报名结束时间")
    private String registrationEndDate;
    
    @ApiModelProperty(value = "活动状态 0 已取消\n" +
            "1 未开始【未到报名开始时间】\n" +
            "       2 报名中【可以进行报名】\n" +
            "        3 进行中【活动期间】\n" +
            "        4 已结束【活动已结束】")
    private Integer status;
    
    @ApiModelProperty(value = "页面标题")
    private String pageTitle;
    
    @ApiModelProperty(value = "列表缩略图")
    private String coverImg;
    
    @ApiModelProperty(value = "详情头部背景图")
    private String topImg;
    
    @ApiModelProperty(value = "活动详情背景图")
    private String detailImg;
    
    @ApiModelProperty(value = "限制报名人数")
    private Integer restrictAttendance;
    
    @ApiModelProperty(value = "海报图")
    private String posterImg;
    
    @ApiModelProperty(value = "二维码海报图")
    private String posterQrImg;

    @ApiModelProperty(value = "logo图片")
    private String logoImg;

    public PclassInfoResponse(ActivityDetail activityDetail) {
        this.address = activityDetail.getAddress();
        this.city = activityDetail.getCity();
        this.classesCode = activityDetail.getClassesCode();
        this.content = activityDetail.getContent();
        this.courseName = activityDetail.getCourseName();
        //this.otherCourseName=activityDetail.getOtherCourseName();
        this.endTime = activityDetail.getEndTime();
        this.expertIntroduce = activityDetail.getExpertIntroduce();
        this.expertName = activityDetail.getExpertName();
        this.hotline = activityDetail.getHotline();
        this.id = activityDetail.getId();
        this.isDeleted = activityDetail.getIsDeleted();
        this.isEnabled = activityDetail.getIsEnabled();
        this.ownerId = activityDetail.getOwnerId();
        this.place = activityDetail.getPlace();
        this.province = activityDetail.getProvince();
        this.remark = activityDetail.getRemark();
        this.startTime = activityDetail.getStartTime();
        this.topic = activityDetail.getTopic();
        this.pclassId = activityDetail.getPclassId();
        this.isSignIn = activityDetail.getIsSignIn();
        this.latitude = activityDetail.getLatitude();
        this.longitude = activityDetail.getLongitude();
        this.isSignUp = activityDetail.getIsSignUp();
        this.classesType=activityDetail.getChannel();
        
        this.registrationBeginDate = activityDetail.getRegistrationBeginDate();
        this.registrationEndDate = activityDetail.getRegistrationEndDate();
        this.status = activityDetail.getStatus();
        this.pageTitle = activityDetail.getPageTitle();
        this.coverImg = activityDetail.getCoverImg();
        this.topImg = activityDetail.getTopImg();
        this.detailImg = activityDetail.getDetailImg();
        this.restrictAttendance = activityDetail.getRestrictAttendance();
        this.posterImg = activityDetail.getPosterImg();
        this.posterQrImg = activityDetail.getPosterQrImg();
        this.logoImg = activityDetail.getLogoImg();
    }
}
