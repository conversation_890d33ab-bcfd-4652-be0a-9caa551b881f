package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/28 下午 05:12
 * @describe
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignUpHistoryRequest {
    @ApiModelProperty(value = "电话")
    @NotBlank(message = "手机号不能为空")
    private String cellphone;
    @ApiModelProperty(value = "classesCode")
    @NotNull(message = "classCode不能为空")
    private String classesCode;
}
