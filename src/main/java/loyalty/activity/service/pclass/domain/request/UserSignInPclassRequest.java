package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/25 上午 09:48
 * @describe
 */
@Data
public class UserSignInPclassRequest {
    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程id不能为空")
    private Integer activityId;
    @ApiModelProperty(value = "婴儿生日（报名所需）")
    @NotNull(message = "婴儿生日不能为空")
    private Date babyBirthday;
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$",
            message = "手机号格式错误")
    private String cellphone;
    @ApiModelProperty(value = "当前定位-纬度")
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;
    @ApiModelProperty(value = "当前定位-经度")
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;
    @ApiModelProperty(value = "定位城市")
    @NotBlank(message = "定位城市不能为空")
    private String city;
    //    @ApiModelProperty(value = "课程编码")
//    @NotBlank(message = "课程编码不能为空")
//    private String pclassCode;
    @ApiModelProperty(value = "openId")
    @NotBlank(message = "openId")
    private String openId;
    //    @ApiModelProperty(value = "用户名（报名所需）")
//    @NotNull(message = "用户名")
//    private String userName;
    @ApiModelProperty(value = "负责人id")
    private String pnecId;
    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;
}
