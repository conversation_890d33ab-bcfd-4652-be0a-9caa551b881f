package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/27 上午 10:57
 * @describe
 */
@Data
public class PnecQrCodeRequest {
    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Integer activityId;
    @ApiModelProperty(value = "电话")
    @NotBlank(message = "手机号不能为空")
    private String cellphone;
}
