package loyalty.activity.service.pclass.domain.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 上午 11:57
 * @describe
 */
@Data
public class GetSubscriptionStatusResponse {
    private String pclassCode;
    private List<GetSubscriptionStatusResponse.SubscribeTypeStatus> subscribeTypeStatusList;

    public GetSubscriptionStatusResponse() {
        subscribeTypeStatusList = new ArrayList<>();
    }

    @Data
    public static class SubscribeTypeStatus {
        private Boolean isAccept;
        private Boolean isSend;
        private String type;
    }
}
