package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 04:47
 * @describe
 */
@Data
public class ExpectCitiesRequest {
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$",
            message = "手机号格式错误")
    private String cellphone;
    @ApiModelProperty(value = "期望城市")
    @NotBlank(message = "期望城市不能为空")
    private String expectCity;
    @ApiModelProperty(value = "省份")
    @NotBlank(message = "省份不能为空")
    private String province;
}
