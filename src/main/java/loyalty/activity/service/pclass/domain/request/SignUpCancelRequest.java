package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 02:50
 * @describe
 */
@Data
public class SignUpCancelRequest {
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String cellphone;
    @ApiModelProperty(value = "活动id")
    @NotNull(message = "活动id不能为空")
    private Long activityId;
}
