package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 下午 05:16
 * @describe
 */
@Data
public class SaveRemindSubscriptionRequest {
    @NotEmpty
    private String appid;
    @NotEmpty
    private String cellphone;
    @NotEmpty
    private String event;
    @NotEmpty
    private String openid;
    @NotNull
    @ApiModelProperty(value = "课程编码")
    private String pclassCode;
    private String pnecId;
    @NotNull
    private Date subscribeTime;
    @NotNull
    private List<SaveRemindSubscriptionRequest.Template> template;
    @NotEmpty
    private String unionid;

    private String content;

    private String externalUrl;

    @Data
    @Valid
    public static class Template {
        @NotEmpty
        private String subscribeStatus;
        @NotEmpty
        private String templateId;
        @NotEmpty
        @Pattern(regexp = "signUp|signIn", message = "订阅消息业务类型错误")
        private String type;
    }
}
