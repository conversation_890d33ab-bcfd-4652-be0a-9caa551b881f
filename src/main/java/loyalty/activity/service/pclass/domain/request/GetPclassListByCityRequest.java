package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.common.dto.PageRequest;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 下午 03:10
 * @describe
 */
@Data
public class GetPclassListByCityRequest extends PageRequest {
    @ApiModelProperty(value = "城市")
    @NotBlank(message = "请打开手机定位以及小程序定位允许")
    private String city;
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String cellphone;
    @ApiModelProperty(value = "负责人id")
    private String pnecId;
    @ApiModelProperty(value = "列表类型，SIGN_IN-签到、SIGN_UP-报名")
    @NotEmpty(message = "列表类型不能为空")
    @Pattern(regexp = "SIGN_IN|SIGN_UP", message = "列表类型错误")
    private String listType;
}
