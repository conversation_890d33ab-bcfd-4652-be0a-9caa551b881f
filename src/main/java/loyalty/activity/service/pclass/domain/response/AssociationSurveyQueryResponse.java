package loyalty.activity.service.pclass.domain.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.pclass.db.model.AssociationSurveyInfo;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class AssociationSurveyQueryResponse {

    private List<Association> associationList;

    public AssociationSurveyQueryResponse() {
    }

    public AssociationSurveyQueryResponse(List<Association> associationList) {
        this.associationList = associationList;
    }

    @Data
    public static class Association{
        //协会信息
        @ApiModelProperty("协会ID")
        private Integer associationId;
        @ApiModelProperty("协会渠道")
        private String channel;
        @ApiModelProperty("协会名称")
        private String associationName;
        @ApiModelProperty("协会简介")
        private String summary;
        @ApiModelProperty("问卷信息")
        private List<AssociationSurvey> associationSurveyList;

        public Association() {
        }

        public Association(List<AssociationSurveyInfo> associationSurveyInfoList) {
            if (CollectionUtils.isNotEmpty(associationSurveyInfoList)) {
                //协会信息填充
                AssociationSurveyInfo associationInfo = associationSurveyInfoList.get(0);
                this.setAssociationId(associationInfo.getAssociationId());
                this.setAssociationName(associationInfo.getAssociationName());
                this.setSummary(associationInfo.getSummary());
                this.setChannel(associationInfo.getChannel());
                List<AssociationSurvey> associationSurveyList = new ArrayList<>();
                this.setAssociationSurveyList(associationSurveyList);
                Map<Integer, List<AssociationSurveyInfo>> surveyMap = associationSurveyInfoList.stream().collect(Collectors.groupingBy(AssociationSurveyInfo::getSurveyId));
                for (Map.Entry<Integer, List<AssociationSurveyInfo>> entry : surveyMap.entrySet()) {
                    //单个问卷信息填充
                    AssociationSurvey associationSurvey = new AssociationSurvey();
                    AssociationSurveyInfo surveyInfo = entry.getValue().get(0);
                    associationSurvey.setSurveyId(surveyInfo.getSurveyId());
                    associationSurvey.setSurveyName(surveyInfo.getSurveyName());
                    associationSurvey.setTopic(surveyInfo.getTopic());
                    //问卷题目填充
                    List<SurveyTopic> surveyTopicList = new ArrayList<>();
                    associationSurvey.setSurveyTopicList(surveyTopicList);
                    Map<Integer, List<AssociationSurveyInfo>> topicMap = entry.getValue().stream().collect(Collectors.groupingBy(AssociationSurveyInfo::getTopicId));
                    for (Map.Entry<Integer, List<AssociationSurveyInfo>> topicEntry : topicMap.entrySet()) {
                        //单个问卷题目填充
                        SurveyTopic surveyTopic = new SurveyTopic();
                        AssociationSurveyInfo topicInfo = topicEntry.getValue().get(0);
                        surveyTopic.setTopicId(topicInfo.getTopicId());
                        surveyTopic.setTopicTitle(topicInfo.getTopicTitle());
                        surveyTopic.setTopicSequence(topicInfo.getTopicSequence());
                        surveyTopic.setCheckType(topicInfo.getCheckType());
                        surveyTopic.setScore(topicInfo.getScore());
                        surveyTopic.setIsRequired(topicInfo.getIsRequired());
                        //问卷题目选项填充
                        List<SurveyTopicItem> surveyTopicItemList = new ArrayList<>();
                        surveyTopic.setSurveyTopicItemList(surveyTopicItemList);
                        for (AssociationSurveyInfo itemInfo : topicEntry.getValue()) {
                            SurveyTopicItem surveyTopicItem = new SurveyTopicItem();
                            surveyTopicItem.setItemId(itemInfo.getItemId());
                            surveyTopicItem.setItemTitle(itemInfo.getItemTitle());
                            surveyTopicItem.setItemSequence(itemInfo.getItemSequence());
                            surveyTopicItem.setOptionType(itemInfo.getOptionType());
                            surveyTopicItem.setIsCorrectOption(itemInfo.getIsCorrectOption());
                            surveyTopicItemList.add(surveyTopicItem);
                        }
                        surveyTopicList.add(surveyTopic);
                    }
                    associationSurveyList.add(associationSurvey);
                }
            }
        }
    }

    @Data
    public static class AssociationSurvey{
        //问卷信息
        @ApiModelProperty("问卷ID")
        private Integer surveyId;
        @ApiModelProperty("问卷名称")
        private String surveyName;
        @ApiModelProperty("课程主题")
        private String topic;
        @ApiModelProperty("问卷题目")
        private List<SurveyTopic> surveyTopicList;
    }

    @Data
    public static class SurveyTopic{
        //问卷题目
        @ApiModelProperty("题目ID")
        private Integer topicId;
        @ApiModelProperty("题目标题")
        private String topicTitle;
        @ApiModelProperty("题目类型")
        private String checkType;
        @ApiModelProperty("题目顺序")
        private Integer topicSequence;
        @ApiModelProperty("答对可拿到分数")
        private Integer score;
        @ApiModelProperty("是否必答")
        private Boolean isRequired;
        @ApiModelProperty("题目选项")
        private List<SurveyTopicItem> surveyTopicItemList;
    }

    @Data
    public static class SurveyTopicItem{
        //问卷题目选项
        @ApiModelProperty("选项ID")
        private Integer itemId;
        @ApiModelProperty("选项标题")
        private String itemTitle;
        @ApiModelProperty("选项类型")
        private String optionType;
        @ApiModelProperty("选项顺序")
        private String itemSequence;
        @ApiModelProperty("是否为正确选项")
        @JsonIgnore
        private Boolean isCorrectOption;
    }
}
