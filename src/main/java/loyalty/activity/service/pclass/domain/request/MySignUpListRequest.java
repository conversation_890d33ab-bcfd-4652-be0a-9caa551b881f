package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.common.dto.PageRequest;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 我的报名列表请求
 */
@Data
@ApiModel(description = "我的报名列表请求")
public class MySignUpListRequest extends PageRequest {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String cellphone;

    @ApiModelProperty(value = "报名状态：1-成功报名，0-已取消报名", example = "0")
    private Integer signUpStatus;
} 