package loyalty.activity.service.pclass.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import loyalty.activity.service.common.dto.PageRequest;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class QueryPclassRequest extends PageRequest{

        @ApiModelProperty(value = "城市")
        private String city;
        @ApiModelProperty(value = "活动状态, 0 已取消\n" +
                "1 未开始【未到报名开始时间】\n" +
                "       2 报名中【可以进行报名】\n" +
                "        3 进行中【活动期间】\n" +
                "        4 已结束【活动已结束】")
        private Integer status;

        @ApiModelProperty(value = "手机号")
        @NotBlank(message = "手机号")
        private String cellphone;

}
