package loyalty.activity.service.pclass.controller;

import com.cstools.data.model.error.InternalException;
import com.cstools.data.model.restful.response.InternalResponse;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.external.domain.request.PraiseMomQuickRedeemQueryRequest;
import loyalty.activity.service.external.service.QuickRedeemCampaignService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/pclass/quickRedeem")
@Api
@AllArgsConstructor
public class QuickRedeemCampaignController {

    private final QuickRedeemCampaignService quickRedeemCampaignService;

    @ApiOperation(value = "快速核销信息查询", notes = "快速核销信息查询")
    @PostMapping(value = "/getQuickRedeemInfo")
    public InternalResponse getQuickRedeemInfo(@RequestBody @Valid PraiseMomQuickRedeemQueryRequest request) {
        InternalResponse internalResponse = InternalResponse.success();
        try {
            log.info("Start to execute  /praiseMomGift/getQuickRedeemInfo with request={}",
                    new Gson().toJson(request));
            internalResponse.withBody(quickRedeemCampaignService.getQuickRedeemInfo(request));
        } catch (InternalException ie) {
            log.error("Internal error occurred while execute  /praiseMomGift/getQuickRedeemInfo with errorCode={}, " +
                    "message={}", ie.getErrorCode(), ie.getCustomizeMessage());
            return ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()) :
                    InternalResponse.fail(ie.getErrorCode(), ie.getParams());
        } catch (Exception e) {
            log.error("Error occurred while execute  /praiseMomGift/getQuickRedeemInfo", e);
            return InternalResponse.fail().withBody(e.getMessage());
        } finally {
            log.info("Completed execute  /praiseMomGift/getQuickRedeemInfo with result={} ",
                    new Gson().toJson(internalResponse));
        }
        return internalResponse;
    }
}
