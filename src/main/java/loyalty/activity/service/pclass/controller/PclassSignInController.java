package loyalty.activity.service.pclass.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.pclass.domain.request.PnecQrCodeRequest;
import loyalty.activity.service.pclass.domain.request.SignInHistoryRequest;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pclass.service.PclassSignInService;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/24 下午 05:24
 * @describe
 */
@Slf4j
@RestController
@RequestMapping("/pclass/signIn")
@Api
public class PclassSignInController {
    @Resource
    private PclassSignInService pclassSignInService;

    @ResponseBody
    @RequestMapping(value = "/userSignIn", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/userSignIn", notes = "课程签到", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "课程签到")
    public InternalResponse<UserSignInResponse> userSignIn(@RequestBody @Valid UserSignInRequest request) {
        InternalResponse<UserSignInResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassSignInService.userSignIn(request));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/getPnecQrCode", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getPnecQrCode", notes = "获取pnec的联系我二维码", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "获取pnec的联系我二维码")
    public InternalResponse<UserSignInResponse> getPnecQrCode(@RequestBody @Valid PnecQrCodeRequest request) {
        InternalResponse<UserSignInResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassSignInService.getPnecQrCode(request));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/querySignInHistory", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/querySignInHistory", notes = "查询课程签到历史", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "查询课程签到历史")
    public InternalResponse querySignInHistory(@RequestBody @Valid SignInHistoryRequest request) {
        InternalResponse<PageResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassSignInService.selectSignInList(request));
        return internalResponse;
    }
}
