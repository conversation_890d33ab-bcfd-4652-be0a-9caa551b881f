package loyalty.activity.service.pclass.controller;


import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pclass.domain.request.AssociationSurveySubmitRequest;
import loyalty.activity.service.pclass.domain.request.UserSignInConditionRequest;
import loyalty.activity.service.pclass.domain.request.UserSurveySubConditionRequest;
import loyalty.activity.service.pclass.domain.response.AssociationSurveyQueryResponse;
import loyalty.activity.service.pclass.service.InternalInsidePClassService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/inside/pclass")
public class InternalInsidePClassController {

    @Autowired
    private InternalInsidePClassService insidePClassService;

    @ApiLog
    @ApiOperation(value = "问卷信息查询", notes = "问卷信息查询")
    @RequestMapping(value = "/getSurveyInfo", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<AssociationSurveyQueryResponse> getSurveyInfo(@RequestParam("associationId") Integer associationId) {
        InternalResponse internalResponse = null;
        AssociationSurveyQueryResponse data = insidePClassService.getSurveyInfo(associationId);
        internalResponse = InternalResponse.success().withBody(data);
        return internalResponse;
    }


    @ApiLog
    @ApiOperation(value = "提交问卷", notes = "提交问卷")
    @RequestMapping(value = "/submitSurvey", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse submitSurvey(@RequestBody AssociationSurveySubmitRequest submitData, HttpServletRequest request) {
        InternalResponse internalResponse = null;
        insidePClassService.submitSurvey(submitData,request);
        internalResponse = InternalResponse.success();
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "查询用户问卷提交情况", notes = "查询用户问卷提交情况")
    @RequestMapping(value = "/getUserSurveySubCondition", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse getUserSurveySubCondition(@RequestBody UserSurveySubConditionRequest request) {
        InternalResponse internalResponse = null;

        internalResponse = InternalResponse.success().withBody(insidePClassService.getUserSurveySubCondition(request));
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "优生签到码生成", notes = "优生签到码生成")
    @RequestMapping(value = "/youShengSignInQrcodeGenerate", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> youShengSignInQrcodeGenerate(@RequestParam("classCode") String classCode) throws Exception {
        InternalResponse internalResponse = null;
        internalResponse = InternalResponse.success().withBody(insidePClassService.youShengSignInQrcodeGenerate(classCode));
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "查询用户是否签到", notes = "查询用户是否签到")
    @RequestMapping(value = "/getUserSignInCondition", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse getUserSignInCondition(@RequestBody UserSignInConditionRequest request){
        InternalResponse internalResponse = null;
        internalResponse = InternalResponse.success().withBody(insidePClassService.getUserSignInCondition(request));
        return internalResponse;
    }

    @ApiLog
    @ApiOperation(value = "根据课程编码生成负责人企业微信联系我二维码", notes = "根据课程编码生成负责人企业微信联系我二维码")
    @RequestMapping(value = "/ownerContactQrcodeGenerate", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public InternalResponse<String> ownerContactQrcodeGenerate(@RequestParam("classCode") String classCode){
        InternalResponse internalResponse = null;
        internalResponse = InternalResponse.success().withBody(insidePClassService.ownerContactQrcodeGenerate(classCode));
        return internalResponse;
    }
}
