package loyalty.activity.service.pclass.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pclass.domain.request.ExpectCitiesRequest;
import loyalty.activity.service.pclass.domain.request.SignUpHistoryRequest;
import loyalty.activity.service.pclass.domain.request.UserApplyRequest;
import loyalty.activity.service.pclass.domain.response.SignUpHistoryResponse;
import loyalty.activity.service.pclass.domain.response.UserSignUpResponse;
import loyalty.activity.service.pclass.service.PclassSignUpService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 上午 10:45
 * @describe
 */
@Slf4j
@RestController
@RequestMapping("/pclass/signUp")
@Api
public class PclassSignUpController {
    @Resource
    private PclassSignUpService pclassSignUpService;

    @ResponseBody
    @RequestMapping(value = "/expectCities", method = RequestMethod.POST)
    @ApiOperation(value = "/expectCities", notes = "期望城市", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "提交期望城市")
    public InternalResponse<Void> expectCities(@RequestBody @Valid ExpectCitiesRequest expectCitiesRequest) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        pclassSignUpService.expectCities(expectCitiesRequest);
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/userApply", method = RequestMethod.POST)
    @ApiOperation(value = "/userApply", notes = "用户报名", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "提交课程报名")
    public InternalResponse<UserSignUpResponse> userApply(@RequestBody @Valid UserApplyRequest userApplyRequest) {
        InternalResponse<UserSignUpResponse> internalResponse = InternalResponse.success();
        internalResponse = internalResponse.withBody(pclassSignUpService.userApply(userApplyRequest));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/getSignUpHistory", method = RequestMethod.POST)
    @ApiOperation(value = "/getSignUpHistory", notes = "获取报名历史信息", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "获取报名历史信息")
    public InternalResponse<SignUpHistoryResponse> getSignUpHistory(@RequestBody @Valid SignUpHistoryRequest signUpHistoryRequest) {
        InternalResponse<SignUpHistoryResponse> internalResponse = InternalResponse.success();
        internalResponse = internalResponse.withBody(pclassSignUpService.getSignUpHistory(signUpHistoryRequest));
        return internalResponse;
    }

}
