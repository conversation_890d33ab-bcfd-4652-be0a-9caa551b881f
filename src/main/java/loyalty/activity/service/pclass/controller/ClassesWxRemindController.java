package loyalty.activity.service.pclass.controller;

import common.wechat.serviceapi.service.RestWxMaTokenService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pclass.domain.request.GetSubscriptionStatusRequest;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.pclass.domain.response.GetSubscriptionStatusResponse;
import loyalty.activity.service.pclass.service.ClassesWxRemindService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 下午 04:05
 * @describe
 */
@Slf4j
@RestController
@RequestMapping("/wxRemind")
public class ClassesWxRemindController {
    @Resource
    private ClassesWxRemindService classesWxRemindService;

    @Resource
    private RestWxMaTokenService restWxMaTokenService;

    @ResponseBody
    @RequestMapping(value = "/getSubscriptionStatus", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getSubscriptionStatus", notes = "获取当前活动消息订阅状态", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "获取当前活动消息订阅状态")
    public InternalResponse<GetSubscriptionStatusResponse> getSubscriptionStatus(@RequestBody @Valid GetSubscriptionStatusRequest getSubscriptionStatusRequest) {
        InternalResponse<GetSubscriptionStatusResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(classesWxRemindService.getSubscriptionStatus(getSubscriptionStatusRequest));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/saveRemindSubscription", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/saveRemindSubscription", notes = "保存订阅消息记录，报名如果接受，立即发送", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<Void> saveRemindSubscription(@RequestBody @Valid SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        classesWxRemindService.saveRemindSubscription(saveRemindSubscriptionRequest);
        return InternalResponse.success();
    }

    @ResponseBody
    @RequestMapping(value = "/test", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "/test", notes = "保存订阅消息记录，报名如果接受，立即发送", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<Void> test() {
        String token = restWxMaTokenService.getAccessToken("wx0906e6c98c19783a");
        log.info("token={}", token);
        return InternalResponse.success();
    }
}
