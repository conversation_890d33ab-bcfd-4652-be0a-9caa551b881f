package loyalty.activity.service.pclass.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.pclass.domain.request.GetPclassListByCityRequest;
import loyalty.activity.service.pclass.domain.request.LocationPclassInSameCityRequest;
import loyalty.activity.service.pclass.domain.request.QueryPclassRequest;
import loyalty.activity.service.pclass.domain.request.SignUpCancelRequest;
import loyalty.activity.service.pclass.domain.request.MySignUpListRequest;
import loyalty.activity.service.pclass.domain.response.PclassInfoResponse;
import loyalty.activity.service.pclass.service.PclassInfoService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.utils.log.ApiLog;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 上午 10:43
 * @describe
 */
@Slf4j
@RestController
@RequestMapping("/pclass/info")
@Api
public class PclassInfoController {
    @Resource
    private PclassInfoService pclassInfoService;

    @ResponseBody
    @RequestMapping(value = "/getPclassById", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "/getPclassById", notes = "报名获取课程详情", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<PclassInfoResponse> getPclassById(@RequestParam String pclassCode,@RequestParam(required = false) String cellPhone) {
        InternalResponse<PclassInfoResponse> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassInfoService.getPclassById(pclassCode,cellPhone));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/getPclassListByCity", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getPclassListByCity", notes = "根据城市获取课程列表", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<PageResponse<PclassInfoResponse>> getPclassListByCity(@Valid @RequestBody GetPclassListByCityRequest request) {
        InternalResponse<PageResponse<PclassInfoResponse>> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassInfoService.getPclassListByCity(request));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/getSignUpPclassListByCity", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getSignUpPclassListByCity", notes = "根据城市获取课程列表", response = InternalResponse.class)
    @ApiLog
    public InternalResponse<PageResponse<PclassInfoResponse>> getSignUpPclassListByCity(@Valid @RequestBody QueryPclassRequest request) {
        InternalResponse<PageResponse<PclassInfoResponse>> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassInfoService.getSignUpPclassListByCity(request));
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/locationPclassInSameCity", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/locationPclassInSameCity", notes = "判断课程与定位是否在同一个城市", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "判断课程与定位是否在同一个城市")
    public InternalResponse<Void> locationPclassInSameCity(@RequestBody @Valid LocationPclassInSameCityRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        pclassInfoService.locationPclassInSameCity(request);
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/cancelSignUp", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/cancelSignUp", notes = "取消报名", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "取消报名")
    public InternalResponse<Void> cancelSignUp(@RequestBody @Valid SignUpCancelRequest request) {
        InternalResponse<Void> internalResponse = InternalResponse.success();
        pclassInfoService.cancelSignUp(request);
        return internalResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/getMySignUpList", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "/getMySignUpList", notes = "查询我的报名列表", response = InternalResponse.class)
    @ApiLog(storage = true, storageDescription = "查询我的报名列表")
    public InternalResponse<PageResponse<PclassInfoResponse>> getMySignUpList(@RequestBody @Valid MySignUpListRequest request) {
        InternalResponse<PageResponse<PclassInfoResponse>> internalResponse = InternalResponse.success();
        internalResponse.withBody(pclassInfoService.getMySignUpList(request));
        return internalResponse;
    }
}
