package loyalty.activity.service.pclass.controller;


import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pclass.domain.request.WxTemplateIdRequest;
import loyalty.activity.service.pclass.service.PclassStaticSourceService;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.WxmppTemplateInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 上午 11:33
 * @describe
 */
@Slf4j
@RestController
@RequestMapping("/pclass/staticSource")
public class PclassStaticSourceController {
    @Resource
    private PclassStaticSourceService pclassStaticSourceService;

    //    @ResponseBody
//    @ApiOperation(value = "获取省份下存在课程的城市", notes = "获取省份下存在课程的城市")
//    @GetMapping(value = "/getPclassCityList")
//    public InternalResponse<List<Address.City>> getPclassCityList(@RequestParam String province) {
//        InternalResponse<List<Address.City>> response = InternalResponse.success();
//        response.withBody(pclassStaticSourceService.getPclassCityList(province));
//        return response;
//    }
//
//    @ResponseBody
//    @ApiOperation(value = "获取系统存在课程的省份", notes = "获取系统存在课程的省份")
//    @GetMapping(value = "/getPclassProvinceList")
//    public InternalResponse<List<Address.Province>> getPclassProvinceList() {
//        InternalResponse<List<Address.Province>> response = InternalResponse.success();
//        response.withBody(pclassStaticSourceService.getPclassProvinceList());
//        return response;
//    }
    @ResponseBody
    @ApiOperation(value = "获取省份下的城市", notes = "获取省份下的城市")
    @GetMapping(value = "/getPclassCityList")
    public InternalResponse<List<String>> getPclassCityList(@RequestParam String province) {
        InternalResponse<List<String>> response = InternalResponse.success();
        response.withBody(pclassStaticSourceService.getPclassCityList(province));
        return response;
    }

    @ResponseBody
    @ApiOperation(value = "获取所有省份", notes = "获取所有省份")
    @GetMapping(value = "/getPclassProvinceList")
    public InternalResponse<List<String>> getPclassProvinceList() {
        InternalResponse<List<String>> response = InternalResponse.success();
        response.withBody(pclassStaticSourceService.getPclassProvinceList());
        return response;
    }

    @ResponseBody
    @ApiOperation(value = "获取模板", notes = "获取模板")
    @PostMapping(value = "/getWxTemplateId")
    public InternalResponse<List<WxmppTemplateInfo>> getWxTemplateId(@RequestBody @Valid WxTemplateIdRequest request) {
        InternalResponse<List<WxmppTemplateInfo>> response = InternalResponse.success();
        response.withBody(pclassStaticSourceService.getWxTemplateId(request));
        return response;
    }
}
