package loyalty.activity.service.pclass.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cstools.data.internal.client.WxClient;
import com.cstools.data.internal.domain.wx.request.WxMiniProgramSubscribeMessageSendRequest;
import com.cstools.data.internal.domain.wx.response.AbstractWechatResponse;
import com.google.gson.Gson;
import common.wechat.serviceapi.service.RestWxMaTokenService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.CustomizeException;
import loyalty.activity.service.external.db.mapper.CSActivityMapper;
import loyalty.activity.service.external.util.UrlParseUtils;
import loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper;
import loyalty.activity.service.pclass.db.model.ActivityDetail;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivitySignRemindMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.LogPclassSubscribeSendMsgMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivitySignInRemindDto;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.LogPclassSubscribeSendMsg;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassSubscribeMsgInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

import static loyalty.activity.service.config.AppConstant.APP_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 下午 05:13
 * @describe
 */
@Service
@Slf4j
public class WxMessageService {
    @Resource
    private ActivitySignRemindMapper activitySignRemindMapper;
    @Resource
    private LogPclassSubscribeSendMsgMapper logPclassSubscribeSendMsgMapper;
    @Value("${wx.sass.remind.miniProgramState}")
    private String miniProgramState;
    @Resource
    private PclassSubscribeMsgInfoMapper pclassSubscribeMsgInfoMapper;
    @Resource
    private WxClient wxClient;
    @Resource
    private RestWxMaTokenService restWxMaTokenService;

    @Resource
    private ActivityQueryMapper activityQueryMapper;

    @Resource
    private CSActivityMapper csActivityMapper;

    public void doSendSignUpMessage(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        String pclassCode =
            saveRemindSubscriptionRequest.getPclassCode() == null ? "" : saveRemindSubscriptionRequest.getPclassCode();
        String pnecId =
            saveRemindSubscriptionRequest.getPnecId() == null ? "" : saveRemindSubscriptionRequest.getPnecId();
        ActivitySignInRemindDto remindInfo =
            activitySignRemindMapper.getRemindInfoByCellphoneAndCode(
                saveRemindSubscriptionRequest.getCellphone(), saveRemindSubscriptionRequest.getPclassCode());
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        WxMiniProgramSubscribeMessageSendRequest sendRequest = GetSendSignInRemindMessage(sf, remindInfo,
            pclassCode, pnecId,saveRemindSubscriptionRequest);
        AbstractWechatResponse wechatResponse =
            wxClient.sendSubscribeMessageWithToken(AbstractWechatResponse.class, sendRequest, HttpMethod.POST,
                restWxMaTokenService.getAccessToken(saveRemindSubscriptionRequest.getAppid()));
        doInsertLogPclassSubscribeSendMsg(wechatResponse, remindInfo, sendRequest);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void doInsertLogPclassSubscribeSendMsg(AbstractWechatResponse wechatResponse,
                                                  ActivitySignInRemindDto activitySignInRemindDto,
                                                  WxMiniProgramSubscribeMessageSendRequest sendRequest) {
        LogPclassSubscribeSendMsg logPclassSubscribeSendMsg = new LogPclassSubscribeSendMsg();
        logPclassSubscribeSendMsg.setPclassSubscribeMsgInfoId(activitySignInRemindDto.getId());
        logPclassSubscribeSendMsg.setSendData(JSONObject.toJSONString(sendRequest));
        logPclassSubscribeSendMsg.setIsSuc(wechatResponse.getErrcode() == 0);
        logPclassSubscribeSendMsg.setResponseMsg(JSONObject.toJSONString(wechatResponse));
        logPclassSubscribeSendMsg.setCreator(APP_NAME);
        logPclassSubscribeSendMsg.setUpdater(APP_NAME);
        logPclassSubscribeSendMsgMapper.insert(logPclassSubscribeSendMsg);
        PclassSubscribeMsgInfo pclassSubscribeMsgInfo = new PclassSubscribeMsgInfo();
        pclassSubscribeMsgInfo.setIsSend(true);
        pclassSubscribeMsgInfoMapper.update(pclassSubscribeMsgInfo,
                new LambdaQueryWrapper<PclassSubscribeMsgInfo>()
                        .eq(PclassSubscribeMsgInfo::getId, activitySignInRemindDto.getId()));
    }

    @SneakyThrows
    private WxMiniProgramSubscribeMessageSendRequest GetSendSignInRemindMessage(SimpleDateFormat sf,
                                                                                ActivitySignInRemindDto activitySignInRemindDto,
                                                                                String pclassCode, String pnecId, SaveRemindSubscriptionRequest request) {
        log.info("get sendSignIn class channel={}",new Gson().toJson(activitySignInRemindDto));
        ActivityDetail activityDetail=activityQueryMapper.getActivityDetailByPcalssId(activitySignInRemindDto.getClassesCode());
        String template = activitySignInRemindDto.getTemplateData();
        template = template
            .replace("${openid}", activitySignInRemindDto.getOpenid())
            .replace("${miniprogramState}", miniProgramState)
            .replace("${activityName}", activitySignInRemindDto.getCourseName())
            .replace("${activityStartTime}", sf.format(activitySignInRemindDto.getStartTime()))
            .replace("${activityStore}", " ")
            .replace("${classesCode}", pclassCode)
            .replace("${pnecId}", pnecId)
            .replace("${signInType}",activityDetail.getChannel())
            .replace("${activityAddress}", activityDetail.getCity());
        if(StringUtils.isNotBlank(request.getContent())){
            template=template.replace("${content}",request.getContent());
        }

        WxMiniProgramSubscribeMessageSendRequest sendRequest =
                JSONObject.parseObject(template, WxMiniProgramSubscribeMessageSendRequest.class);
        sendRequest.setMiniProgramState(miniProgramState);
        if(StringUtils.isNotBlank(request.getExternalUrl())){
            //拼接参数
            StringBuilder pageBuilder = new StringBuilder(sendRequest.getPage());
            if (pageBuilder.indexOf("?") == -1) {
                pageBuilder.append("?");
            } else {
                pageBuilder.append("&");
            }
            sendRequest.setPage(pageBuilder.append("h5_url=").append(UrlParseUtils.encrypt(request.getExternalUrl())).toString());
        }
        log.info("get sendSignIn page=[{}],template={}",sendRequest.getPage(),template);
        return sendRequest;
    }

    public void doSendSubscribeMessage(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        String pclassCode =
                saveRemindSubscriptionRequest.getPclassCode() == null ? "" : saveRemindSubscriptionRequest.getPclassCode();
        String pnecId =
                saveRemindSubscriptionRequest.getPnecId() == null ? "" : saveRemindSubscriptionRequest.getPnecId();
        String subscribeType =
                saveRemindSubscriptionRequest.getTemplate().get(0).getType();
        ActivitySignInRemindDto remindInfo =
                csActivityMapper.getRemindInfoByCellphoneAndCode(
                        saveRemindSubscriptionRequest.getCellphone(), saveRemindSubscriptionRequest.getPclassCode(),subscribeType);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        WxMiniProgramSubscribeMessageSendRequest sendRequest = GetSendSignInRemindMessage(sf, remindInfo,
                pclassCode, pnecId,saveRemindSubscriptionRequest);
        AbstractWechatResponse wechatResponse =
                wxClient.sendSubscribeMessageWithToken(AbstractWechatResponse.class, sendRequest, HttpMethod.POST,
                        restWxMaTokenService.getAccessToken(saveRemindSubscriptionRequest.getAppid()));
        doInsertLogPclassSubscribeSendMsg(wechatResponse, remindInfo, sendRequest);
        if(wechatResponse.getErrcode()!=0){
            throw new CustomizeException(wechatResponse.getErrmsg());
        }
    }
}
