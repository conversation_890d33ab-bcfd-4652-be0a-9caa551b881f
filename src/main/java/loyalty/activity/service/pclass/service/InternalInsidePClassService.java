package loyalty.activity.service.pclass.service;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.service.BaseCallServiceImpl;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.error.UserSurveySubmittedException;
import loyalty.activity.service.error.UserSurveyTopicAnswerNumNotMatchException;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.AssociationSurveyMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.AssociationSurveyInfo;
import loyalty.activity.service.pclass.db.model.PclassQrcodeInfo;
import loyalty.activity.service.pclass.domain.request.AssociationSurveySubmitRequest;
import loyalty.activity.service.pclass.domain.request.UserSignInConditionRequest;
import loyalty.activity.service.pclass.domain.request.UserSurveySubConditionRequest;
import loyalty.activity.service.pclass.domain.response.AssociationSurveyQueryResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.common.BaseEntity;
import loyalty.activity.service.sharelib.rest.client.YoushengClient;
import loyalty.activity.service.sharelib.rest.request.YouShengSignInQrcodeRequest;
import loyalty.activity.service.sharelib.rest.response.ResearchResponse;
import loyalty.activity.service.sharelib.rest.response.YouShengSignInQrcodeResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.SurveyTopicCheckType;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserSurvey;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserSurveyItem;
import loyalty.activity.service.sharelib.utils.IPUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

//todo: to howard team: 删除无用代码

@Slf4j
@Service
public class InternalInsidePClassService extends BaseCallServiceImpl {

    @Value("${spring.application.name}")
    private String serviceName;
    @Value("${ncp.client.url}")
    private String ncpUrl;
    @Value("${ncp.access.key.id}")
    private String accessKeyId;

    @Autowired
    private AssociationSurveyMapper associationSurveyMapper;
    @Autowired
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Autowired
    private YoushengClient youShengClient;
    @Autowired
    private ActivitySignInMapper activitySignInMapper;
    @Autowired
    private PNECClassService pnecClassService;

    public AssociationSurveyQueryResponse getSurveyInfo(Integer associationId) {
        List<AssociationSurveyInfo> data = associationSurveyMapper.getSurveyInfoByAssociationId(associationId);
        Map<Integer, List<AssociationSurveyInfo>> surveyInfoMap = data.stream().collect(Collectors.groupingBy(AssociationSurveyInfo::getAssociationId));
        List<AssociationSurveyQueryResponse.Association> associationList = new ArrayList<>();
        for (Map.Entry<Integer, List<AssociationSurveyInfo>> entry : surveyInfoMap.entrySet()) {
            AssociationSurveyQueryResponse.Association association = new AssociationSurveyQueryResponse.Association(entry.getValue());
            associationList.add(association);
        }
        return new AssociationSurveyQueryResponse(associationList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitSurvey(AssociationSurveySubmitRequest submitData, HttpServletRequest request) {
        Integer submitCount = associationSurveyMapper.getSubmitCountByCellphoneAndClassCode(submitData.getPclassCode(), submitData.getCellphone());
        if (submitCount > 0) {
            throw new UserSurveySubmittedException();
        }
        String reqIp = IPUtils.getClinetIpByReq(request);
        PclassUserSurvey pclassUserSurvey = new PclassUserSurvey();
        pclassUserSurvey.setSurveyId(submitData.getSurveyId());
        pclassUserSurvey.setPclassCode(submitData.getPclassCode());
        pclassUserSurvey.setCellphone(submitData.getCellphone());
        pclassUserSurvey.setOpenid(submitData.getOpenid());
        pclassUserSurvey.setUnionid(submitData.getUnionid());
        pclassUserSurvey.setIp(reqIp);
        Map<Integer, List<AssociationSurveySubmitRequest.UserSurveyItem>> userItemMap = submitData.getUserSurveyItemList().stream()
                .collect(Collectors.groupingBy(AssociationSurveySubmitRequest.UserSurveyItem::getTopicId));
        List<AssociationSurveyInfo> data = associationSurveyMapper.getSurveyInfoBySurveyId(submitData.getSurveyId());
        Map<Integer, Map<Boolean, List<AssociationSurveyInfo>>> surveyItemMap = data.stream()
                .collect(Collectors.groupingBy(AssociationSurveyInfo::getTopicId, Collectors.partitioningBy(AssociationSurveyInfo::getIsCorrectOption)));
        List<PclassUserSurveyItem> userSurveyItemList = new ArrayList<>();
        int scoreSum = 0;
        for (Map.Entry<Integer, List<AssociationSurveySubmitRequest.UserSurveyItem>> userItem : userItemMap.entrySet()) {
            Integer topicId = userItem.getKey();
            List<AssociationSurveyInfo> correctItemList = surveyItemMap.get(topicId).get(true);
            String correctItemSequence = StringUtils.join(correctItemList.stream().map(AssociationSurveyInfo::getItemSequence).sorted().toArray(String[]::new), ",");
            String userItemSequence = StringUtils.join(userItem.getValue().stream().map(AssociationSurveySubmitRequest.UserSurveyItem::getUserAnswer).sorted().toArray(String[]::new), ",");
            AssociationSurveyInfo topicInfo = correctItemList.get(0);
            checkUserSurveyAnswerCountIsMatch(topicInfo.getCheckType(), topicInfo.getTopicSequence(), userItemSequence.split(",").length);
            PclassUserSurveyItem userSurveyItem = new PclassUserSurveyItem();
            userSurveyItem.setTopicId(topicId);
            userSurveyItem.setQuestionContent(topicInfo.getTopicTitle());
            userSurveyItem.setUserAnswer(userItemSequence);
            userSurveyItem.setCorrectAnswer(correctItemSequence);
            boolean isCorrect = false;
            int score = 0;
            if (correctItemSequence.equals(userItemSequence)) {
                isCorrect = true;
                score = topicInfo.getScore();
            }
            userSurveyItem.setIsCorrect(isCorrect);
            userSurveyItem.setScore(score);
            userSurveyItemList.add(userSurveyItem);
            scoreSum += score;
        }
        pclassUserSurvey.setScore(scoreSum);
        setOperatorInfo(userSurveyItemList);
        setOperatorInfo(Collections.singletonList(pclassUserSurvey));
        associationSurveyMapper.insertUpdatePclassUserSurveyEntity(pclassUserSurvey);
        userSurveyItemList.forEach(item -> item.setUserSurveyId(pclassUserSurvey.getId()));
        associationSurveyMapper.insertPclassUserSurveyItemBatch(userSurveyItemList);
    }

    private void checkUserSurveyAnswerCountIsMatch(String checkType, Integer topicSequence, Integer answerCount) {
        if (SurveyTopicCheckType.SINGLE.name().equals(checkType) && answerCount != 1) {
            throw new UserSurveyTopicAnswerNumNotMatchException("题号为 " + topicSequence);
        } else if (SurveyTopicCheckType.MULTIPLE.name().equals(checkType) && answerCount < 2) {
            throw new UserSurveyTopicAnswerNumNotMatchException("题号为 " + topicSequence);
        }
    }

    //todo: to howard team: ？？？？
    private void setOperatorInfo(List<? extends BaseEntity> entities) {
        if (CollectionUtils.isNotEmpty(entities)) {
            for (BaseEntity entity : entities) {
                entity.setCreator(serviceName);
                entity.setUpdater(serviceName);
            }
        }
    }

    //todo: to howard team:
        /*todo:1. The method name `youShengSignInQrcodeGenerate` does not follow Java naming conventions which suggests using camelCase for method names. It can be renamed to `generateYouShengSignInQrcode`.
      2. The exception thrown in case `qrcodeInfo` is null, can be a more specific exception, and also can be logged with specific error message. For example, it can be replaced by `throw new PclassNotFoundException("PclassQrcodeInfo not found for classCode: " + classCode);`.
      3. It is best to use the `Instant` class instead of the `Date` class because `Date` is mutable, and using it can cause concurrency issues. But, if it is necessary to use `Date` class, it is better to make itThread safe. So, using the synchronized block can also prevent concurrency issues.
      4. There are two network requests to the `youShengClient` with the same request parameter `classCode` and `channel`. It is better to call the `youShengClient.queryActivity(...)` with the same request parameter `YouShengSignInQrcodeRequest` used to call `youShengClient.generateQrCode(...)`.
      5. The `getQrcodeUrlFromResponse(...)` method is not provided, it can be a helper method that extracts the `qrcodeUrl` field from `ResearchResponse<YouShengSignInQrcodeResponse>`.
      6. It is not ideal to use `String.valueOf(qrcodeInfo.getStartTime().getTime()/1000)` and `String.valueOf(qrcodeInfo.getEndTime().getTime()/1000)` to cast `long` to `String`, instead it can be written like this `Long.toString(qrcodeInfo.getStartTime().getTime()/1000)` and `Long.toString(qrcodeInfo.getEndTime().getTime()/1000)`.
      
      To-do list:
      1. Rename the method `youShengSignInQrcodeGenerate` to `generateYouShengSignInQrcode`.
      2. Change the thrown exception in case `qrcodeInfo` is null to `throw new PclassNotFoundException("PclassQrcodeInfo not found for classCode: " + classCode);`.
      3. Make `Date` object thread-safe by using the synchronized block or use `Instant` class instead.
      4. Call `youShengClient.queryActivity(...)` with the same `YouShengSignInQrcodeRequest` parameter used to call `youShengClient.generateQrCode(...)`, so as to avoid duplicating the network call request.
      5. Add helper method `getQrcodeUrlFromResponse(...)` to extract the `qrcodeUrl` field from `ResearchResponse<YouShengSignInQrcodeResponse>`.
      6. Change `String.valueOf(qrcodeInfo.getStartTime().getTime()/1000)` and `String.valueOf(qrcodeInfo.getEndTime().getTime()/1000)` to `Long.toString(qrcodeInfo.getStartTime().getTime()/1000)` and `Long.toString(qrcodeInfo.getEndTime().getTime()/1000)` respectively. */
      public String youShengSignInQrcodeGenerate(String classCode) {
        PclassQrcodeInfo qrcodeInfo = pclassInfoQueryMapper.getPclassQrcodeInfoByClassCode(classCode);
        if (qrcodeInfo == null) {
            throw new PclassNotFoundException();
        }
        ResearchResponse<YouShengSignInQrcodeResponse> response;
        if (qrcodeInfo.getQrcodeUrl() == null || qrcodeInfo.getQrcodeExpireTime().before(new Date())) {
            response = youShengClient.queryActivity(classCode, "92");
            if (response.getData() != null && response.getData().getQrcodeUrl() != null) {
                return getQrcodeUrlFromResponse(response,classCode);
            }
            YouShengSignInQrcodeRequest request = new YouShengSignInQrcodeRequest();
            request.setName(qrcodeInfo.getCourseName());
            request.setStime(String.valueOf(qrcodeInfo.getStartTime().getTime()/1000));
            request.setEtime(String.valueOf(qrcodeInfo.getEndTime().getTime()/1000));
            request.setType(1);
            request.setStatus("1");
            request.setCode(classCode);
            request.setChannel("92");
            request.setPneid(qrcodeInfo.getPneid());
            request.setUsername(qrcodeInfo.getPneName());
            request.setMobilePhone(qrcodeInfo.getPneMobile());

            youShengClient.generateQrCode(request);
            response = youShengClient.queryActivity(classCode, "92");
            log.info("call yousheng generate qrcode with classCode={},request={},response={}",classCode,new Gson().toJson(request),new Gson().toJson(response));
            if (response.getData() != null && response.getData().getQrcodeUrl() != null) {
                return getQrcodeUrlFromResponse(response,classCode);
            }
        }
        return qrcodeInfo.getQrcodeUrl();
    }

    private String getQrcodeUrlFromResponse(ResearchResponse<YouShengSignInQrcodeResponse> response,String classCode){
        YouShengSignInQrcodeResponse qrcodeResponse = response.getData();
        String qrcodeUrl = qrcodeResponse.getQrcodeUrl();
        pclassInfoQueryMapper.updatePclassQrcodeInfo(qrcodeUrl,
                new Date(Long.parseLong(qrcodeResponse.getExpireTime()) * 1000), classCode);
        return qrcodeUrl;
    }

    public Boolean getUserSignInCondition(UserSignInConditionRequest request) {
        return activitySignInMapper.getSignInCondition(request.getClassCode(), request.getCellphone());
    }

    public String ownerContactQrcodeGenerate(String classCode) {
        String ncId = pclassInfoQueryMapper.getOwnerContactQrcode(classCode);
        return pnecClassService.getPersonQrCode(ncId);
    }

    public Boolean getUserSurveySubCondition(UserSurveySubConditionRequest request) {
        Integer submitCount = associationSurveyMapper.getSubmitCountByCellphoneAndClassCode(request.getClassCode(), request.getCellphone());
        return submitCount > 0;
    }

    public String youShengSignInQrcodeUpate(String classCode) {
        PclassQrcodeInfo qrcodeInfo = pclassInfoQueryMapper.getPclassQrcodeInfoByClassCode(classCode);
        if (qrcodeInfo == null) {
            throw new PclassNotFoundException();
        }
        ResearchResponse<YouShengSignInQrcodeResponse> response;
        YouShengSignInQrcodeRequest request = new YouShengSignInQrcodeRequest();
        request.setName(qrcodeInfo.getCourseName());
        request.setStime(String.valueOf(qrcodeInfo.getStartTime().getTime()/1000));
        request.setEtime(String.valueOf(qrcodeInfo.getEndTime().getTime()/1000));
        request.setType(2);
        request.setStatus("1");
        request.setCode(classCode);
        request.setChannel("92");
        request.setPneid(qrcodeInfo.getPneid());
        request.setUsername(qrcodeInfo.getPneName());
        request.setMobilePhone(qrcodeInfo.getPneMobile());

        youShengClient.generateQrCode(request);
        response = youShengClient.queryActivity(classCode, "92");
        log.info("call yousheng generate qrcode with classCode={},request={},response={}",classCode,new Gson().toJson(request),new Gson().toJson(response));
        if (response.getCode() == 0) {
            log.error("insert or update qrCode failure , classesCode = {} , errorMessage = {}", classCode, response.getMsg());
            return null;
        }
      /*  if (response.getData() != null && response.getData().getQrcodeUrl() != null) {
            return getQrcodeUrlFromResponse(response,classCode);
        }*/
        return qrcodeInfo.getQrcodeUrl();
    }

}
