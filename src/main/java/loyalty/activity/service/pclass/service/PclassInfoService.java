package loyalty.activity.service.pclass.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.common.utils.CityUtils;
import loyalty.activity.service.error.NoPclassInCityException;
import loyalty.activity.service.error.NoPclassInCityWithPnecIdException;
import loyalty.activity.service.error.PclassLocationDifferentCityException;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.external.client.JmgActivityClient;
import loyalty.activity.service.external.dto.SignInfo;
import loyalty.activity.service.external.util.AESUtil;
import loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper;
import loyalty.activity.service.pclass.db.mapper.ActivitySignInMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.*;
import loyalty.activity.service.pclass.domain.request.*;
import loyalty.activity.service.pclass.domain.response.PclassInfoResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.sharelib.domain.bo.PclassType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignup;
import loyalty.activity.service.validator.pclass.PclassEnableValidator;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 上午 10:49
 * @describe
 */
@Service
@Slf4j
public class PclassInfoService {
    @Resource
    private ActivityQueryMapper activityQueryMapper;
    @Resource
    private PNECClassService pnecClassService;
    @Resource
    private ActivityUserSigninMapper activityUserSigninMapper;
    @Resource
    private ActivityUserSignupMapper activityUserSignupMapper;

    @Resource
    private ActivitySignInMapper activitySignInMapper;

    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;

    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;

    @Resource
    private JmgActivityClient jmgActivityClient;
    private static void checkPclassList(GetPclassListByCityRequest request, List<ActivityDetail> list) {
        if (list.isEmpty()) {
            if (StringUtils.isNotBlank(request.getPnecId())) {
                throw new NoPclassInCityWithPnecIdException();
            }
            throw new NoPclassInCityException();
        }
    }

    public PclassInfoResponse getPclassById(String pclassCode, String cellPhone) {
        ActivityDetail activityDetail = activityQueryMapper.getActivityDetailByPcalssId(pclassCode);
        new PclassEnableValidator().doValidator(getPclassBaseInfo(activityDetail));
        String ncQrCode = pnecClassService.getPersonQrCode(activityDetail.getOwnerId());
        PclassInfoResponse pclassInfoResponse = new PclassInfoResponse(activityDetail);
        pclassInfoResponse.setNcQrcode(ncQrCode);
        getUserSigninAndUserSignup(cellPhone, activityDetail, pclassInfoResponse);
        return pclassInfoResponse;
    }

    private void getUserSigninAndUserSignup(String cellPhone, ActivityDetail activityDetail, PclassInfoResponse pclassInfoResponse) {
        if (StringUtils.isNotEmpty(cellPhone) && activityDetail != null && activityDetail.getId() != null) {
            ActivityUserSignin userSignin = activityUserSigninMapper.getByActivityIdCellphone(activityDetail.getId().toString(), cellPhone);
            if (userSignin != null) {
                pclassInfoResponse.setIsSignIn(true);
            }
            ActivityUserSignup userSignup = activityUserSignupMapper.getByActivityIdCellphone(activityDetail.getId().toString(), cellPhone);
            if (userSignup != null) {
                pclassInfoResponse.setIsSignUp(true);
            }
        }
    }

    private PclassInfoBase getPclassBaseInfo(ActivityDetail activityDetail) {
        PclassInfoBase pclassInfoBase = new PclassInfoBase();
        if (activityDetail == null || activityDetail.getId() == null) {
            throw new PclassNotFoundException();
        }
        pclassInfoBase.setId(activityDetail.getId());
        pclassInfoBase.setIsDeleted(activityDetail.getIsDeleted());
        pclassInfoBase.setIsEnabled(activityDetail.getIsEnabled());
        return pclassInfoBase;
    }

    public PageResponse<PclassInfoResponse> getPclassListByCity(GetPclassListByCityRequest request) {
        PageResponse<PclassInfoResponse> pageResponse = new PageResponse<>();
        Page<List<ActivityDetail>> page =
                PageHelper.startPage(request.getPage(), request.getLimit(), true);
        List<String> channel = Arrays.asList(PclassType.OUTSIDE_PCLASS.getDesc(),
                PclassType.HEADQUARTERS.getDesc(), PclassType.REGION.getDesc());
        List<ActivityDetail> list = activityQueryMapper.getListByCity(CityUtils.getCity(request.getCity()),
                request.getPnecId(), channel, request.getListType(), new Date());
        checkPclassList(request, list);
        //根据手机号查询当前列表手机号是否报名、签到
        loadSignTimeByCellphone(request.getCellphone(), list);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(request.getPage());
        pageResponse.setResults(list.stream().map(PclassInfoResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }

    private void loadSignTimeByCellphone(String cellphone, List<ActivityDetail> list) {
        List<UserSignList> signList =
                activityQueryMapper.getSignList(
                        list.stream().map(ActivityDetail::getId).collect(Collectors.toList()), cellphone);
        list.forEach(e -> signList.forEach(f -> {
            if (f.getId().equals(e.getId())) {
                e.setIsSignUp(f.getIsSignUp());
                e.setIsSignIn(f.getIsSignIn());
            }
        }));
    }

    public void locationPclassInSameCity(LocationPclassInSameCityRequest request) {
        ActivityDetail activityDetail = activityQueryMapper.getActivityDetailByPcalssId(request.getPclassId());
        if (activityDetail == null) {
            throw new PclassNotFoundException();
        }
        for (String s : activityDetail.getCity().split("/")) {
            if (CityUtils.getCity(s).equals(CityUtils.getCity(request.getCity()))) {
                return;
            }
        }
        throw new PclassLocationDifferentCityException();
    }

    public PageResponse<PclassInfoResponse> getSignUpPclassListByCity(QueryPclassRequest request) {
        PageResponse<PclassInfoResponse> pageResponse = new PageResponse<>();
        Page<List<ActivityDetail>> page =
                PageHelper.startPage(request.getPage(), request.getLimit(), true);
        List<ActivityDetail> list = activityQueryMapper.getPclassListForSignUp(CityUtils.getCity(request.getCity()),request.getStatus());
        if(list.isEmpty()){
            throw new NoPclassInCityException();
        }
        //根据手机号查询当前列表手机号是否报名、签到
        loadSignTimeByCellphone(request.getCellphone(), list);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(request.getPage());
        pageResponse.setResults(list.stream().map(PclassInfoResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }

    public void cancelSignUp(SignUpCancelRequest request) {
        ActivityUserSignup activityUserSignup=activityUserSignupMapper.getByActivityIdCellphone(request.getActivityId().toString(), request.getCellphone());
        activitySignInMapper.cancelSignUp(request.getActivityId(), request.getCellphone());
        PclassInfoApplyCount pclassInfo =
                pclassInfoQueryMapper.getPclassInfoApplyCountById(request.getActivityId().intValue(),
                        request.getCellphone());
        callbackJmg(request, pclassInfo,activityUserSignup);
    }

    private void callbackJmg(SignUpCancelRequest request, PclassInfoApplyCount pclassInfo, ActivityUserSignup activityUserSignup) {
        SignUpHistory signUpHistory = pclassApplyQueryMapper.getSignUpHistory(new SignUpHistoryRequest(request.getCellphone(),pclassInfo.getPclassCode()));
        if(signUpHistory!=null&&activityUserSignup!=null){
            try{
                SignInfo signInfo=new SignInfo();
                signInfo.setUnionid(activityUserSignup.getUnionid());
                signInfo.setOpenid(activityUserSignup.getOpenid());
                signInfo.setNickname(signUpHistory.getUserName());
                signInfo.setBabybirthday(DateUtil.formatDate(signUpHistory.getBabyBirthday()));
                signInfo.setAttendance(signUpHistory.getAttendance()+"");
                signInfo.setCurrentlongitude(activityUserSignup.getCurrentLongitude()!=null? activityUserSignup.getCurrentLongitude().toString():null);
                signInfo.setCurrentlatitude(activityUserSignup.getCurrentLatitude()!=null? activityUserSignup.getCurrentLatitude().toString():null);
                signInfo.setUserphoneencrypt(AESUtil.encrypt(activityUserSignup.getCellphone()));
                signInfo.setContactphoneencrypt(AESUtil.encrypt(signUpHistory.getCellphone()));
                signInfo.setApplytime(DateUtil.formatDateTime(activityUserSignup.getTriggerTime()));
                signInfo.setSignstatus(0);
                jmgActivityClient.notifySignData(pclassInfo.getPclassCode(), Arrays.asList(signInfo));
            }catch (Exception e){
                log.error("signUp callback jmg error,with request={}", JSONUtil.toJsonStr(signUpHistory));
            }
        }
    }

    /**
     * 查询用户的报名列表
     * @param request 请求参数
     * @return 分页响应
     */
    public PageResponse<PclassInfoResponse> getMySignUpList(MySignUpListRequest request) {
        PageResponse<PclassInfoResponse> pageResponse = new PageResponse<>();
        Page<List<ActivityDetail>> page =
                PageHelper.startPage(request.getPage(), request.getLimit(), true);
        List<ActivityDetail> list = activityQueryMapper.getMySignUpList(request.getCellphone(), request.getSignUpStatus());
        if (list.isEmpty()) {
            return pageResponse;
        }
        // 根据手机号查询当前列表手机号是否报名、签到
        loadSignTimeByCellphone(request.getCellphone(), list);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(request.getPage());
        pageResponse.setResults(list.stream().map(PclassInfoResponse::new).collect(Collectors.toList()));
        return pageResponse;
    }
}
