package loyalty.activity.service.pclass.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pclass.domain.request.WxTemplateIdRequest;
import loyalty.activity.service.sharelib.domain.dto.Address;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppTemplateInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.WxmppTemplateInfo;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 下午 02:55
 * @describe
 */
@Service
@Slf4j
public class PclassStaticSourceService {
    @Resource(name = "redisObjectTemplate")
    private RedisTemplate<String, Object> template;
    @Resource
    private WxmppTemplateInfoMapper wxmppTemplateInfoMapper;

    //todo: to howard team:
    
    //    public List<Address.City> getPclassCityList(String province) {
//        List<Address.City> cityList = new ArrayList<>();
//        Object allCity = template.opsForValue().get("loyalty_city_all");
//        if (allCity != null) {
//            JSONObject jsonObject = JSONObject.parseObject(allCity.toString());
//            List<Address.Province> address = JSONArray.parseArray(jsonObject.get("provinces").toString(),
//                Address.Province.class);
//            for (Address.Province province1 : address) {
//                if (province1.getProvinceName().equals(province)) {
//                    cityList = province1.getCities();
//                }
//            }
//        }
//        return cityList;
//    }
//
//    public List<Address.Province> getPclassProvinceList() {
//        List<Address.Province> address = new ArrayList<>();
//        Object allCity = template.opsForValue().get("loyalty_city_all");
//        if (allCity != null) {
//            JSONObject jsonObject = JSONObject.parseObject(allCity.toString());
//            address = JSONArray.parseArray(jsonObject.get("provinces").toString(), Address.Province.class);
//            address.forEach(e -> e.setCities(null));
//        }
//        return address;
//    }
    public List<String> getPclassCityList(String province) {
        List<String> cityList = new ArrayList<>();
        Object allCity = template.opsForValue().get("loyalty_city_all");
        if (allCity != null) {
            JSONObject jsonObject = JSONObject.parseObject(allCity.toString());
            List<Address.Province> address = JSONArray.parseArray(jsonObject.get("provinces").toString(),
                    Address.Province.class);
            for (Address.Province province1 : address) {
                if (province1.getProvinceName().equals(province)) {
                    province1.getCities().forEach(e -> cityList.add(e.getCityName()));
                }
            }
        }
        return cityList;
    }

    //todo: to howard team:
       /*todo:Optimization List:
      - Rename the variables to be more meaningful and descriptive. For example, "address" and "address2" could be renamed to "provinceNames" and "provinces", respectively.
      - Add null checks, if necessary, to ensure that the program does not break if "allCity" or any other variables are null.
      - Replace the use of JSONObject and JSONArray with a dedicated JSON parsing library, such as Jackson or Gson, to improve readability and maintainability of the code.
      - Consider caching the results of this method to improve performance, depending on how frequently this data is accessed and how often it changes. */
     public List<String> getPclassProvinceList() {
        List<String> address = new ArrayList<>();
        Object allCity = template.opsForValue().get("loyalty_city_all");
        if (allCity != null) {
            JSONObject jsonObject = JSONObject.parseObject(allCity.toString());
            List<Address.Province> address2 = JSONArray.parseArray(jsonObject.get("provinces").toString(),
                    Address.Province.class);
            address2.forEach(e -> address.add(e.getProvinceName()));
        }
        return address;
    }

    public List<WxmppTemplateInfo> getWxTemplateId(WxTemplateIdRequest request) {
        return wxmppTemplateInfoMapper.selectList(
                new LambdaQueryWrapper<WxmppTemplateInfo>().in(WxmppTemplateInfo::getSubscribeType, request.getType()));
    }
}
