package loyalty.activity.service.pclass.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import loyalty.activity.service.common.dto.PageResponse;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.pclass.db.mapper.ActivityQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.ActivityDetail;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.db.model.PclassQrcodeInfo;
import loyalty.activity.service.pclass.domain.request.PnecQrCodeRequest;
import loyalty.activity.service.pclass.domain.request.SignInHistoryRequest;
import loyalty.activity.service.pclass.domain.response.UserSignInResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.policy.pclass.signIn.SignInPolicy;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityUserSigninInviterType;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSigninMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignin;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static loyalty.activity.service.common.constants.AppConstants.NCP_QUERY_PERSONSQL;
import static loyalty.activity.service.common.enums.InviterRoleType.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/24 下午 05:25
 * @describe
 */
@Service
public class PclassSignInService {
    @Resource
    private ActivityUserSigninMapper activityUserSigninMapper;

    //todo: to howard team:
    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Resource
    private SignInPolicy signInPolicy;
    @Autowired
    private PNECClassService pnecClassService;

    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Resource
    private JdbcTemplate ncpMasterJdbcTemplate;

    @Resource
    private ActivityQueryMapper activityQueryMapper;

    public UserSignInResponse getPnecQrCode(PnecQrCodeRequest request) {
        ActivityUserSignin byActivityIdCellphone = activityUserSigninMapper.getByActivityIdCellphone(request.getActivityId().toString(), request.getCellphone());
        String personQrCode = pnecClassService.getPersonQrCode(byActivityIdCellphone.getInviterId());
        UserSignInResponse userSignInResponse = new UserSignInResponse();
        userSignInResponse.setId(byActivityIdCellphone.getId().intValue());
        userSignInResponse.setQrCode(personQrCode);
        return userSignInResponse;
    }

    public ActivityUserSignin doInsertPclassSignInInfo(UserSignInRequest request, PclassInfoApplyCount pclassInfo, String type) {
        ActivityUserSignin activityUserSignin = new ActivityUserSignin();
        boolean isNcPerson=ncpMasterJdbcTemplate.queryForObject(NCP_QUERY_PERSONSQL,Integer.class,request.getCellphone())>0;
        if(isNcPerson){
            activityUserSignin.setMdPerson(true);
        }
        activityUserSignin.setActivityId(pclassInfo.getId());
        activityUserSignin.setCellphone(request.getCellphone());
        activityUserSignin.setUnionid(request.getUnionId());
        activityUserSignin.setOpenid(request.getOpenId());
       // activityUserSignin.setInviterId(
         //       StringUtils.isBlank(request.getPnecId()) ? pclassInfo.getOwnerId() : request.getPnecId());
        if(!ActivityUserSigninInviterType.TRS.name().equalsIgnoreCase(type)) {
            setInivererIdByPriority(activityUserSignin, request, pclassInfo);
            activityUserSignin.setExt0(new Gson().toJson(new UserSignInRequest.InviterInfo(request.getPnecId(),request.getPnaId(),request.getNcCode(),request.getTsrId(),request.getPersonId(),request.getPersonType())));
        }else{
            activityUserSignin.setInviterId(StringUtils.isBlank(request.getPersonId()) ? pclassInfo.getOwnerId() : request.getPersonId());
            activityUserSignin.setRoleType(StringUtils.isBlank(request.getPersonId()) ?OWNER.name():request.getPersonType());
            activityUserSignin.setExt0(pclassInfo.getExtAttr()==null?null:new Gson().toJson(pclassInfo.getExtAttr()));
        }
        activityUserSignin.setInviterType(type);
        activityUserSignin.setTriggerTime(new Date());
        activityUserSignin.setCurrentLatitude(request.getLatitude());
        activityUserSignin.setCurrentLongitude(request.getLongitude());
        activityUserSignin.setCreator(AppConstant.APP_NAME);
        activityUserSignin.setUpdater(AppConstant.APP_NAME);
        activityUserSignin.setUserName(request.getUserName());
        activityUserSignin.setBabyBirthday(request.getBabyBirthday());
        activityUserSignin.setBabyStatus(request.getBabyStatus());
        activityUserSignin.setAttendance(request.getAttendance());
        activityUserSignin.setRemark(request.getRemark());
        activityUserSigninMapper.insert(activityUserSignin);
        return activityUserSignin;
    }

    public void setInivererIdByPriority(ActivityUserSignin activityUserSignin,UserSignInRequest request,PclassInfoApplyCount pclassInfo){
        if(StringUtils.isNotBlank(request.getPersonId())&&!"null".equalsIgnoreCase(request.getPersonId())){
            activityUserSignin.setInviterId(request.getPersonId());
            activityUserSignin.setRoleType(request.getPersonType());
            return;
        }
        if(StringUtils.isNotBlank(request.getPnecId())&&!"null".equalsIgnoreCase(request.getPnecId())){
            activityUserSignin.setInviterId(request.getPnecId());
            activityUserSignin.setRoleType(PNE.name());
            return;
        }
        if(StringUtils.isNotBlank(request.getTsrId())&&!"null".equalsIgnoreCase(request.getTsrId())){
            activityUserSignin.setInviterId(request.getTsrId());
            activityUserSignin.setRoleType(TSR.name());
            return;
        }
        if(StringUtils.isNotBlank(request.getPnaId())&&!"null".equalsIgnoreCase(request.getPnaId())){
            activityUserSignin.setInviterId(request.getPnaId());
            activityUserSignin.setRoleType(PNA.name());
            return;
        }
        if(StringUtils.isNotBlank(request.getNcCode())&&!"null".equalsIgnoreCase(request.getNcCode())){
            activityUserSignin.setInviterId(request.getNcCode());
            activityUserSignin.setRoleType(NC.name());
            return;
        }
        if(StringUtils.isNotBlank(pclassInfo.getOwnerId())){
            activityUserSignin.setInviterId(pclassInfo.getOwnerId());
            activityUserSignin.setRoleType(OWNER.name());
            return;
        }
    }

    public UserSignInResponse userSignIn(UserSignInRequest request) {
        PclassQrcodeInfo pclassQrcodeInfo= pclassInfoQueryMapper.getPclassQrcodeInfoById(request.getActivityId());
        if("undefined".equalsIgnoreCase(request.getSignInType())||StringUtils.isBlank(request.getSignInType())){
            request.setSignInType(pclassQrcodeInfo.getChannel());
        }
        UserSignInResponse response= signInPolicy.doSignIn(request);
        response.setActivityCode(pclassQrcodeInfo.getClassesCode());
        return response;
    }

    public String getQrCodeEmployeeNo(PclassInfoApplyCount pclassInfo, UserSignInRequest userSignInRequest) {
        if(StringUtils.isNotBlank(userSignInRequest.getPnecId())){
            return userSignInRequest.getPnecId();
        }
        return pclassInfo.getOwnerId();
    }

    public PageResponse selectSignInList(@Valid SignInHistoryRequest request) {
        PageResponse<ActivityDetail> pageResponse = new PageResponse<>();
        Page page=PageHelper.startPage(request.getPageNum(), request.getPageSize(),  true);
        List<ActivityDetail> activityDetails=activityQueryMapper.querySignInListByCellPhone(request.getCellphone());
        pageResponse.setResults(activityDetails);
        pageResponse.setCount(page.getTotal());
        pageResponse.setCurrentPageNumber(page.getPages());
        return pageResponse;
    }
}
