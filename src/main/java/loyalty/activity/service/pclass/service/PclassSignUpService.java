package loyalty.activity.service.pclass.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.utils.Converters;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.external.client.JmgActivityClient;
import loyalty.activity.service.external.db.mapper.PClassExtMapper;
import loyalty.activity.service.external.dto.SignInfo;
import loyalty.activity.service.external.util.AESUtil;
import loyalty.activity.service.member.service.MemberService;
import loyalty.activity.service.pclass.db.mapper.PclassApplyQueryMapper;
import loyalty.activity.service.pclass.db.mapper.PclassInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.InsertSignUpBaseInfo;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.pclass.db.model.SignUpHistory;
import loyalty.activity.service.pclass.domain.request.ExpectCitiesRequest;
import loyalty.activity.service.pclass.domain.request.SignUpHistoryRequest;
import loyalty.activity.service.pclass.domain.request.UserApplyRequest;
import loyalty.activity.service.pclass.domain.response.IsAddNcResponse;
import loyalty.activity.service.pclass.domain.response.SignUpHistoryResponse;
import loyalty.activity.service.pclass.domain.response.UserSignUpResponse;
import loyalty.activity.service.pnec.service.PNECClassService;
import loyalty.activity.service.sharelib.sharelib.domain.bo.PclassUserSignupStatus;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.ActivityUserSignupMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserExpectCityRecordMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassUserSignupFormMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.ActivityUserSignup;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserExpectCityRecord;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassUserSignupForm;
import loyalty.activity.service.validator.pclass.PclassLimitValidator;
import loyalty.activity.service.validator.pclass.chain.PclassSignUpValidatorChain;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17 上午 10:49
 * @describe
 */
@Service
@Slf4j
public class PclassSignUpService {
    @Resource
    private ActivityUserSignupMapper activityUserSignupMapper;
    @Resource
    private PclassApplyQueryMapper pclassApplyQueryMapper;
    @Resource
    private PclassInfoQueryMapper pclassInfoQueryMapper;
    @Resource
    private PclassUserExpectCityRecordMapper pclassUserExpectCityRecordMapper;
    @Resource
    private PclassUserSignupFormMapper pclassUserSignupFormMapper;

    @Resource
    private PClassExtMapper pClassExtMapper;
    @Autowired
    private PNECClassService pnecClassService;

    @Resource
    private MemberService memberService;

    @Resource
    private JmgActivityClient jmgActivityClient;
    ;

    @Transactional(rollbackFor = Exception.class)
    public UserSignUpResponse userApply(UserApplyRequest userApplyRequest) {
        UserSignUpResponse userSignUpResponse = new UserSignUpResponse();
        //重复报名直接返回
//        if (countApplyTime(userApplyRequest, userSignUpResponse)) {
//            return userSignUpResponse;
//        }
        PclassInfoApplyCount pclassInfo =
                pclassInfoQueryMapper.getPclassInfoApplyCountById(userApplyRequest.getActivityId(),
                        userApplyRequest.getCellphone());
        //报名验证
        new PclassSignUpValidatorChain(pClassExtMapper).doValidator(Converters.convertToNewPclassInfoBase(pclassInfo));
        //获取二维码
        getPclassPnecQrCode(pclassInfo.getOwnerId(), userApplyRequest, userSignUpResponse);
        //是否Nc
        //判断是不是nc
        userSignUpResponse.setIsAdd(false);
        if (userApplyRequest.getCellphone() != null && userApplyRequest.getUnionId() != null) {
            IsAddNcResponse addNc = memberService.isAddNc(userApplyRequest.getCellphone(), userApplyRequest.getUnionId());
            log.info("IsAddNcResponse is valve:{}", addNc.toString());
            if (addNc.getIsAdd() != null) {
                userSignUpResponse.setIsAdd(addNc.getIsAdd());
            }
        }
        //记录报名信息
        doInsertPclassUserApplyDetail(pclassInfo, getInsertBaseInfo(userApplyRequest));


        return userSignUpResponse;
    }

    public void doInsertPclassUserApplyDetail(PclassInfoApplyCount pclassInfo,
                                              InsertSignUpBaseInfo insertSignUpBaseInfo) {
        //二次软检查是否超出人数限制
        doubleCheckSignUpLimit(pclassInfo, insertSignUpBaseInfo.getCellphone());
        ActivityUserSignup activityUserSignup = doInsertActivityUserSignup(pclassInfo, insertSignUpBaseInfo);
        doInsertPclassUserSignupForm(activityUserSignup, pclassInfo, insertSignUpBaseInfo);
        callbackJmg(pclassInfo, insertSignUpBaseInfo);
    }

    private void callbackJmg(PclassInfoApplyCount pclassInfo, InsertSignUpBaseInfo insertSignUpBaseInfo) {
        try{
            SignInfo signInfo=new SignInfo();
            signInfo.setUnionid(insertSignUpBaseInfo.getUnionId());
            signInfo.setOpenid(insertSignUpBaseInfo.getOpenId());
            signInfo.setNickname(insertSignUpBaseInfo.getUserName());
            signInfo.setBabybirthday(DateUtil.formatDate(insertSignUpBaseInfo.getBabyBirthday()));
            signInfo.setAttendance(insertSignUpBaseInfo.getAttendance()+"");
            signInfo.setCurrentlongitude(insertSignUpBaseInfo.getLongitude()!=null? insertSignUpBaseInfo.getLongitude().toString():null);
            signInfo.setCurrentlatitude(insertSignUpBaseInfo.getLatitude()!=null? insertSignUpBaseInfo.getLatitude().toString():null);
            signInfo.setUserphoneencrypt(AESUtil.encrypt(insertSignUpBaseInfo.getCellphone()));
            signInfo.setContactphoneencrypt(AESUtil.encrypt(insertSignUpBaseInfo.getInputCellphone()));
            signInfo.setApplytime(DateUtil.formatDateTime(insertSignUpBaseInfo.getTriggerTime()));
            signInfo.setSignstatus(1);
            jmgActivityClient.notifySignData(pclassInfo.getPclassCode(), Arrays.asList(signInfo));
        }catch (Exception e){
            log.error("signUp callback jmg error,with request={}", JSONUtil.toJsonStr(insertSignUpBaseInfo));
        }
    }

    private ActivityUserSignup doInsertActivityUserSignup(PclassInfoApplyCount pclassInfo,
                                                          InsertSignUpBaseInfo insertSignUpBaseInfo) {
        ActivityUserSignup activityUserSignup = new ActivityUserSignup();
        activityUserSignup.setActivityId(pclassInfo.getId());
        activityUserSignup.setCellphone(insertSignUpBaseInfo.getCellphone());
        activityUserSignup.setUnionid(insertSignUpBaseInfo.getUnionId());
        activityUserSignup.setOpenid(insertSignUpBaseInfo.getOpenId());
        activityUserSignup.setTriggerTime(new Date());
        activityUserSignup.setCurrentLatitude(insertSignUpBaseInfo.getLatitude());
        activityUserSignup.setCurrentLongitude(insertSignUpBaseInfo.getLongitude());
        activityUserSignup.setRemark(insertSignUpBaseInfo.getRemark());
        activityUserSignup.setCreator(StringUtils.isBlank(insertSignUpBaseInfo.getCreator())?AppConstant.APP_NAME:insertSignUpBaseInfo.getCreator());
        activityUserSignup.setUpdater(StringUtils.isBlank(insertSignUpBaseInfo.getCreator())?AppConstant.APP_NAME:insertSignUpBaseInfo.getCreator());
        activityUserSignup.setIsEnabled(true);
        insertSignUpBaseInfo.setTriggerTime(activityUserSignup.getTriggerTime());
        activityUserSignupMapper.insertUpdateEntity(activityUserSignup);
        return activityUserSignup;
    }

    private void doubleCheckSignUpLimit(PclassInfoApplyCount pclassInfo, String cellphone) {
        PclassInfoApplyCount doubleCheckInfo =
                pclassInfoQueryMapper.getPclassInfoApplyCountById(Math.toIntExact(pclassInfo.getId()), cellphone);
        PclassLimitValidator pclassLimitValidator = new PclassLimitValidator();
        pclassLimitValidator.doValidator(Converters.convertToNewPclassInfoBase(doubleCheckInfo));
    }

    private void doInsertPclassUserSignupForm(ActivityUserSignup activityUserSignup, PclassInfoApplyCount pclassInfo,
                                              InsertSignUpBaseInfo insertSignUpBaseInfo) {
        PclassUserSignupForm pclassUserSignupForm = new PclassUserSignupForm();
        pclassUserSignupForm.setSignupId(activityUserSignup.getId());
        pclassUserSignupForm.setPnecId(insertSignUpBaseInfo.getPnecId() == null ? pclassInfo.getOwnerId() :
                insertSignUpBaseInfo.getPnecId());
        pclassUserSignupForm.setUserName(insertSignUpBaseInfo.getUserName());
        pclassUserSignupForm.setBabyBirthday(insertSignUpBaseInfo.getBabyBirthday());
        pclassUserSignupForm.setBabyStatus(insertSignUpBaseInfo.getBabyStatus());
        pclassUserSignupForm.setAttendance(insertSignUpBaseInfo.getAttendance());
        pclassUserSignupForm.setStatus(PclassUserSignupStatus.REGISTERED.getName());
        pclassUserSignupForm.setCreator(StringUtils.isBlank(insertSignUpBaseInfo.getCreator())?AppConstant.APP_NAME:insertSignUpBaseInfo.getCreator());
        pclassUserSignupForm.setUpdater(StringUtils.isBlank(insertSignUpBaseInfo.getCreator())?AppConstant.APP_NAME:insertSignUpBaseInfo.getCreator());
        pclassUserSignupForm.setInputCellphone(insertSignUpBaseInfo.getInputCellphone());
        pclassUserSignupFormMapper.insertUpdateEntity(pclassUserSignupForm);
    }

//    private boolean countApplyTime(UserApplyRequest userApplyRequest, UserSignUpResponse userSignUpResponse) {
//        UserSignUpResponse applyHistoryQrCode = pclassApplyQueryMapper.getApplyHistoryQrCode(userApplyRequest);
//        boolean notBlank = applyHistoryQrCode != null && applyHistoryQrCode.getId() != null;
//        if (notBlank) {
//            userSignUpResponse.setQrCode(applyHistoryQrCode.getQrCode());
//            userSignUpResponse.setId(applyHistoryQrCode.getId());
//        }
//        return notBlank;
//    }

    private InsertSignUpBaseInfo getInsertBaseInfo(UserApplyRequest userApplyRequest) {
        InsertSignUpBaseInfo insertSignUpBaseInfo = new InsertSignUpBaseInfo();
        insertSignUpBaseInfo.setCellphone(userApplyRequest.getCellphone());
        insertSignUpBaseInfo.setUnionId(userApplyRequest.getUnionId());
        insertSignUpBaseInfo.setOpenId(userApplyRequest.getOpenId());
        insertSignUpBaseInfo.setLatitude(userApplyRequest.getLatitude());
        insertSignUpBaseInfo.setLongitude(userApplyRequest.getLongitude());
        insertSignUpBaseInfo.setBabyStatus(userApplyRequest.getBabyStatus());
        insertSignUpBaseInfo.setPnecId(userApplyRequest.getPnecId());
        insertSignUpBaseInfo.setAttendance(userApplyRequest.getAttendance());
        insertSignUpBaseInfo.setUserName(userApplyRequest.getUserName());
        insertSignUpBaseInfo.setBabyBirthday(userApplyRequest.getBabyBirthday());
        insertSignUpBaseInfo.setRemark(userApplyRequest.getRemark());
        insertSignUpBaseInfo.setInputCellphone(userApplyRequest.getInputCellphone());
        return insertSignUpBaseInfo;
    }

    public void expectCities(ExpectCitiesRequest expectCitiesRequest) {
//        PclassUserExpectCityRecord history =
//            pclassUserExpectCityRecordMapper.selectOne(new LambdaQueryWrapper<PclassUserExpectCityRecord>().eq
//            (PclassUserExpectCityRecord::getCellphone, expectCitiesRequest.getCellphone()));
//        if (history != null) {
//            throw new PclassUserAlreadyExpectCityException();
//        }
        PclassUserExpectCityRecord pclassUserExpectCityRecord = new PclassUserExpectCityRecord();
        pclassUserExpectCityRecord.setCellphone(expectCitiesRequest.getCellphone());
        pclassUserExpectCityRecord.setExpectCity(expectCitiesRequest.getExpectCity());
        pclassUserExpectCityRecord.setSelectProvince(expectCitiesRequest.getProvince());
        pclassUserExpectCityRecord.setFillTime(new Date());
        pclassUserExpectCityRecord.setCreator(AppConstant.APP_NAME);
        pclassUserExpectCityRecord.setUpdater(AppConstant.APP_NAME);
        pclassUserExpectCityRecordMapper.insertIgnoreEntity(pclassUserExpectCityRecord);
    }

    public SignUpHistoryResponse getSignUpHistory(SignUpHistoryRequest signUpHistoryRequest) {
        SignUpHistory signUpHistory = pclassApplyQueryMapper.getSignUpHistory(signUpHistoryRequest);
        return signUpHistory == null ? new SignUpHistoryResponse() : new SignUpHistoryResponse(signUpHistory);
    }

    private void getPclassPnecQrCode(String pnecId, UserApplyRequest userApplyRequest,
                                     UserSignUpResponse userSignUpResponse) {
        pnecId = StringUtils.isNotBlank(userApplyRequest.getPnecId()) ? userApplyRequest.getPnecId() : pnecId;
        userSignUpResponse.setQrCode(pnecClassService.getPersonQrCode(pnecId));
    }
}
