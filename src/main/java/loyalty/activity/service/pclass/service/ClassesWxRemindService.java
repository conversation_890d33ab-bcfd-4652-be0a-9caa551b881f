package loyalty.activity.service.pclass.service;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.config.AppConstant;
import loyalty.activity.service.pclass.db.mapper.PclassSubscribeMsgInfoQueryMapper;
import loyalty.activity.service.pclass.db.model.SubscriptionStatus;
import loyalty.activity.service.pclass.domain.request.GetSubscriptionStatusRequest;
import loyalty.activity.service.pclass.domain.request.SaveRemindSubscriptionRequest;
import loyalty.activity.service.pclass.domain.response.GetSubscriptionStatusResponse;
import loyalty.activity.service.sharelib.sharelib.domain.bo.PclassSubscribeMsgInfoSubscribeType;
import loyalty.activity.service.sharelib.sharelib.domain.bo.SubscribeStatus;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.PclassSubscribeMsgInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.mapper.WxmppSubscribeMsgInfoMapper;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.PclassSubscribeMsgInfo;
import loyalty.activity.service.sharelib.sharelib.domain.db.model.WxmppSubscribeMsgInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 下午 04:19
 * @describe
 */
@Service
@Slf4j
public class ClassesWxRemindService {
    @Resource
    private PclassSubscribeMsgInfoMapper pclassSubscribeMsgInfoMapper;
    @Resource
    private PclassSubscribeMsgInfoQueryMapper pclassSubscribeMsgInfoQueryMapper;
    @Resource
    private WxMessageService wxMessageService;
    @Resource
    private WxmppSubscribeMsgInfoMapper wxmppSubscribeMsgInfoMapper;

    public GetSubscriptionStatusResponse getSubscriptionStatus(GetSubscriptionStatusRequest getSubscriptionStatusRequest) {
        GetSubscriptionStatusResponse getSubscriptionStatusResponse = new GetSubscriptionStatusResponse();
        getSubscriptionStatusResponse.setPclassCode(getSubscriptionStatusRequest.getPlcassCode());
        List<GetSubscriptionStatusResponse.SubscribeTypeStatus> subscribeTypeStatusList =
                getSubscriptionStatusResponse.getSubscribeTypeStatusList();
        for (SubscriptionStatus subscriptionStatus :
                pclassSubscribeMsgInfoQueryMapper.getSubscriptionStatus(getSubscriptionStatusRequest.getCellphone(),
                        getSubscriptionStatusRequest.getPlcassCode())) {
            GetSubscriptionStatusResponse.SubscribeTypeStatus status =
                    new GetSubscriptionStatusResponse.SubscribeTypeStatus();
            status.setType(subscriptionStatus.getType());
            status.setIsAccept(subscriptionStatus.getIsAccept());
            status.setIsSend(subscriptionStatus.getIsSend());
            subscribeTypeStatusList.add(status);
        }
        return getSubscriptionStatusResponse;
    }

    //todo: to howard team: 这个for循环里循环发送signUp的写法是什么呢？ 所以如果getAcceptSubscriptionCount真的返回多条这里是要循环发送吗。
    //实际的写法就是 for循环里只做insert， 然后找出应该发的那条，for结束后接着处理
    public void saveRemindSubscription(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        //数据库应只存在一条accept
        List<String> acceptSubscriptionCount = getAcceptSubscriptionCount(saveRemindSubscriptionRequest);
        for (SaveRemindSubscriptionRequest.Template template : saveRemindSubscriptionRequest.getTemplate()) {
            if (template.getSubscribeStatus().equals(SubscribeStatus.accept.name())
                    && acceptSubscriptionCount.contains(template.getType())) {
                continue;
            }
            doInsertSubscribeMsgInfo(saveRemindSubscriptionRequest, template);
            //报名即刻通知
            if (template.getSubscribeStatus().equals(SubscribeStatus.accept.name())
                    && template.getType().equals(PclassSubscribeMsgInfoSubscribeType.signUp.name())) {
                wxMessageService.doSendSignUpMessage(saveRemindSubscriptionRequest);
            }
        }
    }

    public void sendRemindSubscription(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        for (SaveRemindSubscriptionRequest.Template template : saveRemindSubscriptionRequest.getTemplate()) {
            doInsertSubscribeMsgInfo(saveRemindSubscriptionRequest, template);
            wxMessageService.doSendSubscribeMessage(saveRemindSubscriptionRequest);
        }
    }

    private List<String> getAcceptSubscriptionCount(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest) {
        return pclassSubscribeMsgInfoQueryMapper.getAcceptSubscriptionCount(saveRemindSubscriptionRequest.getPclassCode(), saveRemindSubscriptionRequest.getCellphone());
    }

    @Transactional(rollbackFor = Exception.class)
    public void doInsertSubscribeMsgInfo(SaveRemindSubscriptionRequest saveRemindSubscriptionRequest,
                                         SaveRemindSubscriptionRequest.Template template) {
        WxmppSubscribeMsgInfo wxmppSubscribeMsgInfo = new WxmppSubscribeMsgInfo();
        wxmppSubscribeMsgInfo.setAppid(saveRemindSubscriptionRequest.getAppid());
        wxmppSubscribeMsgInfo.setCellphone(saveRemindSubscriptionRequest.getCellphone());
        wxmppSubscribeMsgInfo.setUnionid(saveRemindSubscriptionRequest.getUnionid());
        wxmppSubscribeMsgInfo.setOpenid(saveRemindSubscriptionRequest.getOpenid());
        wxmppSubscribeMsgInfo.setEvent(saveRemindSubscriptionRequest.getEvent());
        wxmppSubscribeMsgInfo.setTemplateId(template.getTemplateId());
        wxmppSubscribeMsgInfo.setSubscribeStatus(template.getSubscribeStatus());
        wxmppSubscribeMsgInfo.setSubscribeTime(saveRemindSubscriptionRequest.getSubscribeTime());
        wxmppSubscribeMsgInfo.setCreator(AppConstant.APP_NAME);
        wxmppSubscribeMsgInfo.setUpdater(AppConstant.APP_NAME);
        wxmppSubscribeMsgInfoMapper.insertIgnoreEntity(wxmppSubscribeMsgInfo);
        PclassSubscribeMsgInfo pclassSubscribeMsgInfo = new PclassSubscribeMsgInfo();
        pclassSubscribeMsgInfo.setCellphone(saveRemindSubscriptionRequest.getCellphone());
        pclassSubscribeMsgInfo.setUnionid(saveRemindSubscriptionRequest.getUnionid());
        pclassSubscribeMsgInfo.setOpenid(saveRemindSubscriptionRequest.getOpenid());
        pclassSubscribeMsgInfo.setPclassCode(saveRemindSubscriptionRequest.getPclassCode());
        pclassSubscribeMsgInfo.setSubscribeType(template.getType());
        pclassSubscribeMsgInfo.setWxmppSubscribeMsgInfoId(wxmppSubscribeMsgInfo.getId());
        pclassSubscribeMsgInfo.setIsSend(false);
        pclassSubscribeMsgInfo.setCreator(AppConstant.APP_NAME);
        pclassSubscribeMsgInfo.setUpdater(AppConstant.APP_NAME);
        pclassSubscribeMsgInfoMapper.insertIgnoreEntity(pclassSubscribeMsgInfo);
    }


}
