package loyalty.activity.service.pclass.service;


import com.google.gson.Gson;
import freemarker.template.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;


@Slf4j
@Component
public abstract class MailService<T> {
    protected final Configuration configuration;
    private final JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String mailSenderUserName;

    public MailService(JavaMailSender mailSender, Configuration configuration) {
        this.mailSender = mailSender;
        this.configuration = configuration;
    }


    public void sendMail(T params,String title, String mailSendToUserAccount) throws Exception {
        log.info("Start to send email {} for params=[{}]", mailSendToUserAccount, new Gson().toJson(params));
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
        helper.setValidateAddresses(false);
        helper.setFrom(mailSenderUserName, title);
        helper.setTo(mailSendToUserAccount.split(","));
        enhanceMail(helper, params, title);
        try {
            mailSender.send(mimeMessage);
            log.info("end to sending email with account = {} ", mailSendToUserAccount);
        } catch (Exception e) {
            log.error("Error occur while sending email", e);
        }
    }

    abstract void enhanceMail(MimeMessageHelper mimeMessageHelper, T params, String title) throws Exception;

}
