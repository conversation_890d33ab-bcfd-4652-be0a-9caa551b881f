package loyalty.activity.service.pclass.service;

import freemarker.template.Configuration;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.pnec.db.model.PclassEmailInfo;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PclassEmailService extends MailService<PclassEmailInfo>{

    public PclassEmailService(Configuration configuration, JavaMailSender javaMailSender) {
        super(javaMailSender, configuration);
    }

    @Override
     void enhanceMail(MimeMessageHelper mimeMessageHelper, PclassEmailInfo params, String title) throws Exception {
        mimeMessageHelper.setText(params.getDescription(), true);
        mimeMessageHelper.setSubject(title);
        if(params.getAttachment()!=null){
            mimeMessageHelper.addAttachment(params.getAttachment().getName(),params.getAttachment());
        }
    }
}
