package loyalty.activity.service.common.service;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.SearchCriteria;
import loyalty.activity.service.error.QueryParamErrorException;
import loyalty.activity.service.sharelib.common.MainBaseAdminEntity;
import org.springframework.beans.factory.annotation.Value;

import java.lang.reflect.Field;

@Slf4j
public class BaseCallServiceImpl {
    @Value("${spring.application.name}")
    protected String appName;


     public <T extends MainBaseAdminEntity> void setUpdaterInfo(Integer userId, T mainEntity){
         mainEntity.setUpdatedByUser(userId);
         mainEntity.setUpdater(appName);
     }

    public <T extends MainBaseAdminEntity> void setCreatorInfo(Integer userId, T mainEntity){
        mainEntity.setCreatedByUser(userId);
        mainEntity.setCreator(appName);
    }

    protected void verifySearchCri(SearchCriteria searchCriteria, Class clazz){
             if(searchCriteria.getCriterias()!=null && !searchCriteria.getCriterias().isEmpty()) {
                 for (SearchCriteria.Criteria criteria : searchCriteria.getCriterias()){
                     Field[] fields = clazz.getDeclaredFields();
                     boolean found = false;
                     for(Field field : fields){
                         if(field.getName().equals(criteria.getKey())){
                             found = true;
                             break;
                         }
                     }
                     if(!found){
                         log.error("Can not find property {} in class {}", criteria.getKey(), clazz.getName());
                         throw new QueryParamErrorException();
                     }
                 }
             }
        }
}
