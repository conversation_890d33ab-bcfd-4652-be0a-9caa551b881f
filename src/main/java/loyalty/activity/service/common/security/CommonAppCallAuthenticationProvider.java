package loyalty.activity.service.common.security;

import io.jsonwebtoken.ExpiredJwtException;
import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.utils.TokenUtils;
import loyalty.activity.service.pclass.db.mapper.ImitationTestMapper;
import loyalty.activity.service.sharelib.domain.bo.AuthUserInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import static java.util.Arrays.asList;
import static loyalty.activity.service.common.constants.AppConstants.TOKEN_HEADER;
import static loyalty.activity.service.common.constants.AppConstants.TOKEN_TYPE_USERINFO;

@Slf4j
public class CommonAppCallAuthenticationProvider implements AuthenticationProvider, InitializingBean {

    private final Boolean enableAllCallAuthCheck;
    private final ImitationTestMapper imitationTestMapper;

    public CommonAppCallAuthenticationProvider(Boolean enableAllCallAuthCheck,ImitationTestMapper imitationTestMapper) {
        this.enableAllCallAuthCheck = enableAllCallAuthCheck;
        this.imitationTestMapper = imitationTestMapper;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        log.debug("Setting properties for CommonAppCallAuthenticationProvider");
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String principal = authentication.getPrincipal().toString();
        if (!enableAllCallAuthCheck) {
            if (TOKEN_TYPE_USERINFO.equals(principal)) {
                AuthUserInfo userTokenInfo = new AuthUserInfo();
                userTokenInfo.setIdentifier(-1);
                UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken("data", userTokenInfo, asList(new SimpleGrantedAuthority("AppUser")));
                result.setDetails(userTokenInfo);
                return result;
            } else {
                UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(principal, "80404", asList(new SimpleGrantedAuthority("AppUser")));
                result.setDetails("80404");
                return result;
            }
        }

        if (authentication.getPrincipal() == null) {
            return authentication;
        }
        String jwtData = (String) authentication.getCredentials();

        if (jwtData == null) {
            log.debug("Authentication failed: no credentials provided");
            throw new BadCredentialsException("Bad credentials");
        } else {
            if (StringUtils.isNotBlank(jwtData) && jwtData.startsWith(TOKEN_HEADER)) {
                final String token = jwtData.substring(TOKEN_HEADER.length());
                try {
                    if (TOKEN_TYPE_USERINFO.equals(principal)) {
                        AuthUserInfo userTokenInfo = TokenUtils.getVO(token, AuthUserInfo.class);
                        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(principal, userTokenInfo, asList(new SimpleGrantedAuthority("AppUser")));
                        result.setDetails(userTokenInfo);
                        return result;
                    } else {
                        String code = TokenUtils.getVO(token, String.class);
                        String imitationId = imitationTestMapper.getImitationId(code);
                        String userId = imitationId == null ? code : imitationId;
                        log.info("originId = {},resultId = {}",code,userId);
                        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(principal, userId, asList(new SimpleGrantedAuthority("AppUser")));
                        result.setDetails(userId);
                        return result;
                    }
                } catch (ExpiredJwtException expiredJwtException) {
                    log.warn("Token expired {{}}", token);
                    throw new BadCredentialsException("Token expired");
                } catch (Exception e) {
                    log.error("Error occured while parsing token {},msg{}", token, e.getMessage());
                    throw new BadCredentialsException("Token error!!!");
                }
            } else {
                log.error("Invalid Authorization jwtdata={}", jwtData);
                throw new BadCredentialsException("Token error!!!");
            }
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
