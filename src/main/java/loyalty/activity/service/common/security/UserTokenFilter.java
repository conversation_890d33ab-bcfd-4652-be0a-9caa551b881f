package loyalty.activity.service.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static loyalty.activity.service.common.constants.AppConstants.TOKEN_TYPE_USERINFO;

@Slf4j
public class UserTokenFilter extends BasicAuthenticationFilter {

    public UserTokenFilter(AuthenticationManager authManager) {
        super(authManager);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest req, HttpServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        final String authHeader = req.getHeader("Authorization");
        UsernamePasswordAuthenticationToken authenticationRequest = new UsernamePasswordAuthenticationToken(TOKEN_TYPE_USERINFO, authHeader);
        Authentication result = this.getAuthenticationManager().authenticate(authenticationRequest);
        SecurityContextHolder.getContext().setAuthentication(result);
        chain.doFilter(req, res);

    }
}