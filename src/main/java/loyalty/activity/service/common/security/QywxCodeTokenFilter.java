package loyalty.activity.service.common.security;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static loyalty.activity.service.common.constants.AppConstants.TOKEN_TYPE_QYWXCODE;

@Slf4j
@Data
public class QywxCodeTokenFilter extends OncePerRequestFilter {

    private AuthenticationManager authenticationManager;

    public QywxCodeTokenFilter(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws ServletException, IOException {
        final String authHeader = req.getHeader("Authorization-qywxCode");
        UsernamePasswordAuthenticationToken authenticationRequest = new UsernamePasswordAuthenticationToken(TOKEN_TYPE_QYWXCODE, authHeader);
        Authentication result = this.getAuthenticationManager().authenticate(authenticationRequest);
        SecurityContextHolder.getContext().setAuthentication(result);
        chain.doFilter(req, res);
    }
}
