package loyalty.activity.service.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignInHistory {

    private Long activityId;

    private String classesCode;

    private String channel;

    private String activityType;

    private String province;

    private String city;

    private Date signInTime;

    private String pclassType;
}
