package loyalty.activity.service.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ToString
public class SearchCriteria extends PageRequest {
    @ApiModelProperty(value = "筛选条件")
    private List<Criteria> criterias;

    public SearchCriteria(){
        criterias=new ArrayList<>();
    }

    @Data
    public static class Criteria {
        @ApiModelProperty(value = "筛选属性", example = "id")
        private String key;
        @ApiModelProperty(value = "筛选条件", example = "1")
        private String value;
        private String minValue;
        private String maxValue;

    }
}
