package loyalty.activity.service.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@ToString
@Data
public class PageRequest {
    @ApiModelProperty(value = "每页数据条数", example = "20")
    @Min(value = 1, message = "每页行数不得少于1")
    @Max(value = 100, message = "每页行数不得大于100")
    private Integer limit;

    @ApiModelProperty(value = "当前页，最小为1", example = "1")
    @Min(value = 1, message = "页数不得少于1")
    @Max(value = 10000, message = "页数不得大于10000")
    private Integer page;
}
