package loyalty.activity.service.common.controller;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.error.CustomizeException;
import loyalty.activity.service.error.SignInValidateException;
import loyalty.activity.service.sharelib.domain.dto.InternalResponse;
import loyalty.activity.service.sharelib.errors.InternalException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@ControllerAdvice
public class BaseExceptionController {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public InternalResponse onException(Exception e, HttpServletRequest request) {
        log.error("Error occurred while execute {} ", request.getRequestURI(), e);
        return InternalResponse.fail();
    }

    @ExceptionHandler(value = SignInValidateException.class)
    @ResponseBody
    public InternalResponse onInternalException(SignInValidateException ie, HttpServletRequest request) {
        log.warn("Internal error occurred while signIn {} with errorCode={}, msg={}", request.getRequestURI(), ie.getErrorCode(), ie.getCustomizeMessage());
        return ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()) : InternalResponse.fail(ie.getErrorCode(), ie.getParams());
    }

    @ExceptionHandler(value = CustomizeException.class)
    @ResponseBody
    public InternalResponse onInternalException(CustomizeException ie, HttpServletRequest request) {
        log.warn("Internal error occurred while request {} with errorCode={}, msg={}", request.getRequestURI(), ie.getErrorCode(), ie.getCustomizeMessage());
        return ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()) : InternalResponse.fail(ie.getErrorCode(), ie.getParams());
    }

    @ExceptionHandler(value = InternalException.class)
    @ResponseBody
    public InternalResponse onInternalException(InternalException ie, HttpServletRequest request) {
        log.warn("Internal error occurred while execute {} with errorCode={}, msg={}", request.getRequestURI(), ie.getErrorCode(), ie.getCustomizeMessage());
        return ie.getParams() == null ? InternalResponse.fail(ie.getErrorCode()) : InternalResponse.fail(ie.getErrorCode(), ie.getParams());
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public InternalResponse onMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("Parameter error occurred while execute {} with msg={}", request.getRequestURI(), e.getAllErrors().get(0).getDefaultMessage());
        return InternalResponse.fail("04", e.getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(value = DuplicateKeyException.class)
    @ResponseBody
    public InternalResponse onConstraintViolationException(DuplicateKeyException e, HttpServletRequest request) {
        log.warn("Duplicated data error occurred while execute {} with msg={}", request.getRequestURI(), e.getCause().getLocalizedMessage());
        return InternalResponse.fail("05");
    }

    @ExceptionHandler(value = DataIntegrityViolationException.class)
    @ResponseBody
    public InternalResponse onDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        String message = e.getCause().getLocalizedMessage();
        log.warn("Foreign constaint error occurred while execute {} with msg={}", request.getRequestURI(), message);
        if (message.contains("doesn't have a default value")) {
            InternalResponse.fail("07");
        }
        return InternalResponse.fail("06");
    }
}
