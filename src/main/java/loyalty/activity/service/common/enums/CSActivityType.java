package loyalty.activity.service.common.enums;

import java.util.Arrays;
import java.util.Optional;

public enum CSActivityType {

    LARGE_CS("大型CS"),
    MIDDLE_CS("中型CS"),
    MINI_CS("小型CS"),
    MIDDLE_CAR_CS("中型-大篷车CS"),
    MINI_NC("迷你活动-NC"),
    BIG_KA("大咖来了"),
    MINI_SHOW("小型路演"),
    MIDDLE_SHOW("中型路演"),
    BIG_SHOW("大型路演"),
    MIDDLE_AREA_SHOW("小型路演-区域"),
    SAM_SHOW("客制化路演-山姆"),
    CUSTOMIZED_SHOW("客制化路演");

    private String value;

    CSActivityType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static boolean isCsActivity(String value){
      return Arrays.stream(CSActivityType.values()).filter(csActivityType -> csActivityType.value.equalsIgnoreCase(value)).findAny().isPresent();
    }
}
