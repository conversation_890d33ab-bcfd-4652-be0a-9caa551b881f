package loyalty.activity.service.common.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ActivityStatus {
    Active(true),Inactive(false);

    private boolean value;

    public static boolean getByStatus(String status){
       for(ActivityStatus activityStatus:ActivityStatus.values()){
           if(activityStatus.name().equalsIgnoreCase(status)){
               return activityStatus.value;
           }
       }
       return false;
    }
}
