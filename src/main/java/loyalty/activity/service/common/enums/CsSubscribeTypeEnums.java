package loyalty.activity.service.common.enums;

public enum CsSubscribeTypeEnums {
    CS_SIGN_IN(1, "签到提醒"),
    CS_UPATE(2, "活动变更提醒"),
    CS_CANCEL(3, "活动取消提醒");

    private Integer code;
    private String desc;

    CsSubscribeTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CsSubscribeTypeEnums getByCode(Integer code) {
        for (CsSubscribeTypeEnums subscribeTypeEnums : CsSubscribeTypeEnums.values()) {
            if (subscribeTypeEnums.getCode().equals(code)) {
                return subscribeTypeEnums;
            }
        }
        return null;
    }
}
