package loyalty.activity.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import loyalty.activity.service.sharelib.sharelib.domain.bo.ActivityChannel;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Stream;

@AllArgsConstructor
public enum ConsumerActivityBigType {

    BIG_ACTIVITY("大型活动"),
    MIDDLE_ACTIVITY("中型活动"),
    SMALL_ACTIVITY("小型活动"),
    CLIENT_UNION_OUTDOOR("客户联合外拓"),
    LIVE("直播"),
    INSIDE_PCLASS("院内活动");

    private String value;

    public static ConsumerActivityBigType getByValue(String value){
      Optional<ConsumerActivityBigType> op= Arrays.stream(ConsumerActivityBigType.values()).filter(consumerActivityBigType -> consumerActivityBigType.value.equalsIgnoreCase(value)).findFirst();
      if(op.isPresent()){
          return op.get();
      }
      return null;
    }

    public static ActivityChannel get(String consumerActivityTypeValue,String activityTypeValue){
        if(INSIDE_PCLASS.equals(getByValue(consumerActivityTypeValue))){
            return ActivityChannel.INSIDE_PCLASS;
        }
        if(CSActivityType.isCsActivity(activityTypeValue)){
            return ActivityChannel.CS;
        }
        if(LIVE.value.equalsIgnoreCase(consumerActivityTypeValue)){
            return ActivityChannel.OUTSIDE_PCLASS;
        }
        return ActivityChannel.OUTSIDE_PCLASS;
    }

}
