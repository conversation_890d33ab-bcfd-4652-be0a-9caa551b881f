package loyalty.activity.service.common.utils;


import com.cstools.data.internal.domain.crm.response.CRMMemberInfoBaseResponse;
import com.cstools.data.internal.domain.crm.response.CRMQueryMemberInfoResponse;
import loyalty.activity.service.error.PclassNotFoundException;
import loyalty.activity.service.member.domain.response.PclassMemberResponse;
import loyalty.activity.service.pclass.db.model.PclassInfoApplyCount;
import loyalty.activity.service.policy.pclass.signIn.UserSignInRequest;
import loyalty.activity.service.validator.pclass.PclassInfoBase;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;


public final class Converters {
    public static PclassInfoBase convertToNewPclassInfoBaseSignIn(PclassInfoApplyCount pclassInfo,
                                                                  UserSignInRequest request) {
        PclassInfoBase pclassInfoBase = convertToNewPclassInfoBase(pclassInfo);
        pclassInfoBase.setLatitude(request.getLatitude());
        pclassInfoBase.setLongitude(request.getLongitude());
        pclassInfoBase.setTargetLatitude(pclassInfo.getLatitude());
        pclassInfoBase.setTargetLongitude(pclassInfo.getLongitude());
        pclassInfoBase.setProvince(pclassInfo.getProvince());
        pclassInfoBase.setCity(pclassInfo.getCity());
        pclassInfoBase.setPclassType(pclassInfo.getPclassType());
        return pclassInfoBase;
    }

    public static PclassMemberResponse convertToPclassMemberResponse(CRMQueryMemberInfoResponse crmQueryMemberInfoResponse) throws ParseException {
        PclassMemberResponse pclassMemberResponse = new PclassMemberResponse();
        CRMMemberInfoBaseResponse crmMemberInfoResponse = crmQueryMemberInfoResponse.getCrmMemberInfoResponse();
        pclassMemberResponse.setCellphone(crmMemberInfoResponse.getCellphone());
        pclassMemberResponse.setUserName(crmMemberInfoResponse.getName());
        pclassMemberResponse.setBabyStatus(
                crmMemberInfoResponse.getSecBabyBirth() == null && crmMemberInfoResponse.getBabyBirth() == null ? 0 : 1);
        if (crmMemberInfoResponse.getSecBabyBirth() != null) {
            pclassMemberResponse.setBirthday(StringFormatDate(crmMemberInfoResponse.getSecBabyBirth()));
        } else {
            pclassMemberResponse.setBirthday(StringFormatDate(crmMemberInfoResponse.getBabyBirth()));
        }
        return pclassMemberResponse;
    }

    public static Date StringFormatDate(String strDate) throws ParseException {
        if (StringUtils.isNotBlank(strDate)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(strDate);
        }
        return null;
    }

    public static PclassInfoBase convertToNewPclassInfoBase(PclassInfoApplyCount pclassInfo) {
        if (pclassInfo == null || pclassInfo.getId() == null) {
            throw new PclassNotFoundException();
        }
        PclassInfoBase pclassInfoBase = new PclassInfoBase();
        pclassInfoBase.setClassCode(pclassInfo.getPclassCode());
        pclassInfoBase.setId(pclassInfo.getId());
        pclassInfoBase.setStartTime(pclassInfo.getStartTime());
        pclassInfoBase.setEndTime(pclassInfo.getEndTime());
        pclassInfoBase.setIsEnabled(pclassInfo.getIsEnabled());
        pclassInfoBase.setLimitCount(pclassInfo.getLimitCount());
        pclassInfoBase.setStatus(pclassInfo.getStatus());
        pclassInfoBase.setApplyCount(pclassInfo.getApplyCount());
        pclassInfoBase.setIsDeleted(pclassInfo.getIsDeleted());
        pclassInfoBase.setIsOnline(pclassInfo.getIsOnline());
        pclassInfoBase.setActivityType(pclassInfo.getActivityType());
        return pclassInfoBase;
    }
}
