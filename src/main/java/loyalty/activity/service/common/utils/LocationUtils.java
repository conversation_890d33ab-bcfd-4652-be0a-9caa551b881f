package loyalty.activity.service.common.utils;


import loyalty.activity.service.common.constants.LocationConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/19 上午 10:25
 * @describe 地理位置工具类
 */
public class LocationUtils {

    /**
     * 判断经纬度是否合法，经度的数值是0度－180度，纬度的数值是0度－90度。
     *
     * @param latitude  纬度
     * @param longitude 经度
     */
    public static boolean checkLatitudeAndLongitude(BigDecimal latitude, BigDecimal longitude) {
        latitude = latitude.abs();
        longitude = longitude.abs();
        return (latitude.compareTo(BigDecimal.ZERO) < 0 || latitude.compareTo(BigDecimal.valueOf(90)) > 0)
                || (longitude.compareTo(BigDecimal.ZERO) < 0 || longitude.compareTo(BigDecimal.valueOf(180)) > 0);
    }

    /**
     * 判断定位是否在目标点指定范围内
     *
     * @param targetLatitude    目标纬度
     * @param targetLongitude   目标经度
     * @param positionLatitude  定位纬度
     * @param positionLongitude 定位经度
     * @param radius            半径
     * @return 是否
     */
    public static boolean inRange(BigDecimal targetLatitude, BigDecimal targetLongitude,
                                  BigDecimal positionLatitude, BigDecimal positionLongitude,
                                  int radius) {
        return calculateDistance(targetLatitude, targetLongitude, positionLatitude, positionLongitude).intValue() <= radius;
    }

    /**
     * 计算两个经纬度之间的距离
     *
     * @param targetLatitude    目标纬度
     * @param targetLongitude   目标经度
     * @param positionLatitude  定位纬度
     * @param positionLongitude 定位经度
     * @return 距离（单位：米）
     */
    public static BigDecimal calculateDistance(BigDecimal targetLatitude, BigDecimal targetLongitude,
                                               BigDecimal positionLatitude, BigDecimal positionLongitude) {
        double radLat1 = rad(targetLatitude.doubleValue());
        double radLat2 = rad(positionLatitude.doubleValue());
        double a = radLat1 - radLat2;
        double b = rad(targetLongitude.doubleValue()) - rad(positionLongitude.doubleValue());
        double s = 2 * Math.asin(
                Math.sqrt(
                        Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * LocationConstants.EARTH_RADIUS;
        //向上进一位 200.1 -> 201  199.3 -> 200
        return BigDecimal.valueOf(s).setScale(0, RoundingMode.UP);
    }

    /**
     * 转化为弧度(rad)
     */
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 距离转换成中文  小于1000米  返回X米，大于1000米，返回X.X公里
     *
     * @param distance 距离
     * @return 距离
     */
    public static String transformDistance(BigDecimal distance) {
        int distanceInt = distance.intValue();
        if (distanceInt < 1000) {
            return distanceInt + "米";
        } else {
            distance = distance
                    .divide(BigDecimal.valueOf(100), RoundingMode.DOWN)
                    .setScale(1, RoundingMode.DOWN);
            return distance.doubleValue() / 10 + "公里";
        }
    }
}
