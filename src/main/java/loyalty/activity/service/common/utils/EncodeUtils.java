package loyalty.activity.service.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang.time.DateUtils;

import java.util.Date;

public class EncodeUtils {
    public static String getJWTtoken(String subject, String tokenTitle, Object value, String signKey) {
        return Jwts.builder().setSubject(String.valueOf(subject)).claim(tokenTitle, value).setIssuedAt(new Date()).signWith(SignatureAlgorithm.HS256, signKey).compact();
    }

    public static String getJWTtoken(String subject, String tokenTitle, Object value, String signKey, int expiredOffset) {
        return Jwts.builder().setSubject(String.valueOf(subject)).claim(tokenTitle, value).setIssuedAt(new Date()).setExpiration(DateUtils.addMilliseconds(new Date(), expiredOffset)).signWith(SignatureAlgorithm.HS256, signKey).compact();
    }

    public static <T> T parseJWTtoken(String tokenTitle, String token, String signKey, Class<T> valueType) {
        Claims claims = (Claims) Jwts.parser().setSigningKey(signKey).parseClaimsJws(token).getBody();
        if (claims.get(tokenTitle) != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(claims.get(tokenTitle), valueType);
        } else {
            return null;
        }
    }
}
