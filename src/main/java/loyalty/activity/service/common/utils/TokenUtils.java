package loyalty.activity.service.common.utils;

public class TokenUtils {

    private static String SUBJECT = "subject";
    private static String TOKEN_TITLE = "naturade_exam";
    private static String SIGN = "naturade_mpp";


    public static String getToken(Object vo) {
        return EncodeUtils.getJWTtoken(SUBJECT, TOKEN_TITLE, vo, SIGN, 240 * 3600 * 1000);
    }

    public static String getTokenNoLimitTime(Object vo) {
        return EncodeUtils.getJWTtoken(SUBJECT, TOKEN_TITLE, vo, SIGN);
    }

    public static <T> T getVO(String token, Class<T> valueType) {
        return EncodeUtils.parseJWTtoken(TOKEN_TITLE, token, SIGN, valueType);
    }


}
