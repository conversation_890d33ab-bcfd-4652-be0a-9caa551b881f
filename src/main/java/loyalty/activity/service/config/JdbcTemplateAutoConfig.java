package loyalty.activity.service.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.Data;
import loyalty.activity.service.config.properties.NcpDbProperties;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties({JdbcTemplateAutoConfig.DbProperties.class, NcpDbProperties.class})
public class JdbcTemplateAutoConfig {

    public DruidDataSource ncpDs(JdbcTemplateAutoConfig.DbProperties dbProperties){
        DruidDataSource druidDataSource=new DruidDataSource();
        druidDataSource.setDriverClassName(dbProperties.getDriverClassName());
        druidDataSource.setUrl(dbProperties.getJdbcUrl());
        druidDataSource.setUsername(dbProperties.getUsername());
        druidDataSource.setPassword(dbProperties.getPassword());
        return druidDataSource;
    }

   @Bean(name = "ncpJdbcTemplate")
    public JdbcTemplate ncpJdbcTemplate(JdbcTemplateAutoConfig.DbProperties dbProperties){
        return new JdbcTemplate(this.ncpDs(dbProperties));
    }

    @Bean(name = "ncpMasterJdbcTemplate")
    public JdbcTemplate ncpMasterJdbcTemplate(NcpDbProperties ncpDbProperties){
        DruidDataSource druidDataSource=new DruidDataSource();
        druidDataSource.setDriverClassName(ncpDbProperties.getDriverClassName());
        druidDataSource.setUrl(ncpDbProperties.getJdbcUrl());
        druidDataSource.setUsername(ncpDbProperties.getUsername());
        druidDataSource.setPassword(ncpDbProperties.getPassword());
        return new JdbcTemplate(druidDataSource);
    }



    @ConfigurationProperties("thirdparty.ncp.datasource")
    @Data
    public static class DbProperties{
        private String driverClassName;
        private String jdbcUrl;
        private String username;
        private String password;
    }

}
