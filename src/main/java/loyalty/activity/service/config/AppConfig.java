package loyalty.activity.service.config;

import com.cstools.data.internal.client.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import common.healthcheck.monitoring.starter.HealthCheckConfig;
import loyalty.activity.service.config.properties.WebclientProperties;
import loyalty.activity.service.sharelib.rest.client.MallInternalClient;
import loyalty.activity.service.sharelib.rest.client.YoushengClient;
import loyalty.activity.service.sharelib.utils.log.EnabledAPILog;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;

@Import({HealthCheckConfig.class})
@EnabledAPILog
@Configuration
@MapperScan(basePackages = {"loyalty.activity.service.sharelib.domain.db", "loyalty.activity.service"},
        annotationClass = Mapper.class)
@ComponentScan(basePackages = {"com.cstools.data.internal.client", "common.healthcheck.monitoring.starter"})
public class AppConfig {
    @Autowired
    private WebclientProperties webclientProperties;

    @Bean
    public CRMClient crmClient(
            @Value("${mall.crm.url:}") String crmUrl, RestTemplate restTemplate,
            @Value("${rsa.key.name}") String certPath) {
        return new CRMClient(restTemplate, crmUrl, certPath);
    }

    @Bean
    public WXCropClient wxCropClient(RestTemplate restTemplate, @Value("${qywx.appId}") String appId, @Value("${qywx.agentid}") String agentid, @Value("${qywx.sass.host}") String qywxSassHost) {
        return new WXCropClient(restTemplate, appId, agentid, qywxSassHost);
    }

    //todo: to howard team: 为什么同时使用webClient和RestTemplate， 选一个，而且这个restTemplate也没设置线程，默认只有10的
    @Bean
    public RestTemplate restTemplate(@Value("${rest.call.connect.timeout}") int connectTimeout,
                                     @Value("${rest.call.read.timeout}") int readTimeout) {
        SimpleClientHttpRequestFactory clientHttpRequestFactory
                = new SimpleClientHttpRequestFactory();
        clientHttpRequestFactory.setConnectTimeout(connectTimeout);
        clientHttpRequestFactory.setReadTimeout(readTimeout);
        return new RestTemplate(clientHttpRequestFactory);
    }

    @Bean(name = "WebClient")
    public WebClient webClient() {
        //配置固定大小连接池
        ConnectionProvider provider = ConnectionProvider
                .builder("web-client")
                // 等待超时时间
                .pendingAcquireTimeout(Duration.ofSeconds(webclientProperties.getPendingAcquireTimeout()))
                // 最大连接数
                .maxConnections(webclientProperties.getMaxConnections())
                // 等待队列大小
                .pendingAcquireMaxCount(webclientProperties.getPendingAcquireMaxCount())
                .build();

        HttpClient httpClient = HttpClient.create(provider);
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    @Bean(name = "redisObjectTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 配置连接工厂
        template.setConnectionFactory(factory);

        //使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值（默认使用JDK的序列化方式）
        Jackson2JsonRedisSerializer jacksonSeial = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper om = new ObjectMapper();
        // 指定要序列化的域，field,get和set,以及修饰符范围，ANY是都有包括private和public
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        //序列化对象校验，不加的话反序列化会变为泛型
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY);
        jacksonSeial.setObjectMapper(om);

        // 值采用json序列化
        template.setValueSerializer(jacksonSeial);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());

        // 设置hash key 和value序列化模式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(jacksonSeial);
        template.afterPropertiesSet();

        return template;
    }

    @Bean
    public WxClient wxClient(RestTemplate restTemplate) {
        return new WxClient(restTemplate);
    }

    @Bean
    public YoushengClient youshengClient(@Value("${yousheng.client.host}")String host, @Value("${yousheng.client.apiKey}")String apiKey, @Value("${yousheng.client.apiSecret}")String apiSecret, RestTemplate restTemplate){
        return new YoushengClient(host,apiKey,apiSecret,restTemplate);
    }

    @Bean
    public NCPV2Client ncpV2Client(RestTemplate restTemplate,@Value("${ncp.client.url}") String ncpHost) {
        return new NCPV2Client(restTemplate,ncpHost);
    }

    @Bean
    public MallInternalClient mallInternalClient(@Value("${mallInternal.client.host}")String host,RestTemplate restTemplate){
        return new MallInternalClient(null,null,host,restTemplate);
    }

}
