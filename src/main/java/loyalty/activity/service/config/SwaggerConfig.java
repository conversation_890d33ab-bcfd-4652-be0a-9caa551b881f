package loyalty.activity.service.config;

import io.swagger.annotations.Api;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
@EnableOpenApi
public class SwaggerConfig {
    public SwaggerConfig() {
    }

    @Bean
    public Docket createRestApi() {
        return (new Docket(DocumentationType.SWAGGER_2)).apiInfo(this.apiInfo()).select().apis(RequestHandlerSelectors.withClassAnnotation(Api.class)).paths(PathSelectors.any()).build();
        //return (new Docket(DocumentationType.SWAGGER_2)).apiInfo(this.apiInfo()).select().paths(PathSelectors.ant("/member/getPclassMemberBaseInfo")).build();
    }

    private ApiInfo apiInfo() {
        return (new ApiInfoBuilder()).version("1.0").license("version1.0").title("CE系统配合-接口文档").build();

    }
}
