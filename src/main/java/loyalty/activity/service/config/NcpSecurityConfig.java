package loyalty.activity.service.config;

import loyalty.activity.service.common.security.CommonAppCallAuthenticationProvider;
import loyalty.activity.service.common.security.QywxCodeTokenFilter;
import loyalty.activity.service.pclass.db.mapper.ImitationTestMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@Order(1)
public class NcpSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${security.enable.appauth.ncp.check:true}")
    private Boolean enableAppAuthCheck;
    @Autowired
    private ImitationTestMapper imitationTestMapper;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.antMatcher("/ncp/**")
                .addFilterAfter(new QywxCodeTokenFilter(authenticationManager()), UsernamePasswordAuthenticationFilter.class)
                .csrf().disable();
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(ncpAllCallAuthProvider());
    }

    @Bean("ncpAllCallAuthProvider")
    protected AuthenticationProvider ncpAllCallAuthProvider() {
        return new CommonAppCallAuthenticationProvider(enableAppAuthCheck,imitationTestMapper);
    }
}
