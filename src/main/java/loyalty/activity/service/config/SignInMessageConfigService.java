package loyalty.activity.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 签到成功提示语配置服务
 * 从 Nacos 配置中心读取不同课程类型对应的签到成功提示语
 */
@Slf4j
@Service
@RefreshScope
public class SignInMessageConfigService {

    /**
     * 默认签到成功提示语
     */
    private static final String DEFAULT_SUCCESS_MESSAGE = "签到成功！";

    /**
     * 小型活动签到提示语配置
     * 格式：activity_type1:message1,activity_type2:message2
     */
    @Value("${signin.message.small.activity:}")
    private String smallActivityMessages;

    /**
     * 中型活动签到提示语配置
     * 格式：activity_type1:message1,activity_type2:message2
     */
    @Value("${signin.message.medium.activity:}")
    private String mediumActivityMessages;

    /**
     * 大型活动签到提示语配置
     * 格式：activity_type1:message1,activity_type2:message2
     */
    @Value("${signin.message.large.activity:}")
    private String largeActivityMessages;

    /**
     * CS活动签到提示语配置
     * 格式：activity_type1:message1,activity_type2:message2
     */
    @Value("${signin.message.cs.activity:}")
    private String csActivityMessages;

    /**
     * 根据课程类型和活动类型获取签到成功提示语
     *
     * @param pclassType   课程类型（小型活动/中型活动/大型活动）
     * @param activityType 活动类型（高端妈妈班/豪华妈妈班/精品妈妈班等）
     * @return 签到成功提示语
     */
    public String getSignInSuccessMessage(String pclassType, String activityType) {
        try {
            log.debug("获取签到提示语，pclassType: {}, activityType: {}", pclassType, activityType);

            if (!StringUtils.hasText(pclassType) || !StringUtils.hasText(activityType)) {
                log.warn("课程类型或活动类型为空，使用默认提示语");
                return DEFAULT_SUCCESS_MESSAGE;
            }

            String configMessages = getConfigMessagesByPclassType(pclassType);
            if (!StringUtils.hasText(configMessages)) {
                log.warn("未找到课程类型 {} 的配置，使用默认提示语", pclassType);
                return DEFAULT_SUCCESS_MESSAGE;
            }

            Map<String, String> messageMap = parseConfigMessages(configMessages);
            String message = messageMap.get(activityType);

            if (StringUtils.hasText(message)) {
                log.debug("找到匹配的提示语: {}", message);
                return message;
            } else {
                log.warn("未找到活动类型 {} 的提示语配置，使用默认提示语", activityType);
                return DEFAULT_SUCCESS_MESSAGE;
            }

        } catch (Exception e) {
            log.error("获取签到提示语时发生异常，pclassType: {}, activityType: {}", pclassType, activityType, e);
            return DEFAULT_SUCCESS_MESSAGE;
        }
    }

    /**
     * 根据课程类型获取对应的配置字符串
     *
     * @param pclassType 课程类型
     * @return 配置字符串
     */
    private String getConfigMessagesByPclassType(String pclassType) {
        if (pclassType == null) {
            return null;
        }

        switch (pclassType.trim()) {
            case "小型活动":
                return smallActivityMessages;
            case "中型活动":
                return mediumActivityMessages;
            case "大型活动":
                return largeActivityMessages;
            case "CS":
                return csActivityMessages;
            default:
                log.warn("未知的课程类型: {}", pclassType);
                return null;
        }
    }

    /**
     * 解析配置字符串为 Map
     * 格式：activity_type1:message1,activity_type2:message2
     *
     * @param configMessages 配置字符串
     * @return 解析后的 Map
     */
    private Map<String, String> parseConfigMessages(String configMessages) {
        Map<String, String> messageMap = new HashMap<>();

        if (!StringUtils.hasText(configMessages)) {
            return messageMap;
        }

        try {
            String[] pairs = configMessages.split(",");
            for (String pair : pairs) {
                if (StringUtils.hasText(pair)) {
                    String[] keyValue = pair.trim().split(":");
                    if (keyValue.length == 2) {
                        String activityType = keyValue[0].trim();
                        String message = keyValue[1].trim();
                        if (StringUtils.hasText(activityType) && StringUtils.hasText(message)) {
                            messageMap.put(activityType, message);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析配置字符串时发生异常: {}", configMessages, e);
        }

        return messageMap;
    }

    /**
     * 获取默认签到成功提示语
     *
     * @return 默认提示语
     */
    public String getDefaultSuccessMessage() {
        return DEFAULT_SUCCESS_MESSAGE;
    }
}
