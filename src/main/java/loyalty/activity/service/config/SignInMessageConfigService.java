package loyalty.activity.service.config;

import lombok.extern.slf4j.Slf4j;
import loyalty.activity.service.common.dto.SignInHistory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 签到成功提示语配置服务
 * 根据签到类型、课程类型、活动类型和签到次数返回相应的提示语
 */
@Slf4j
@Service
@RefreshScope
public class SignInMessageConfigService {

    /**
     * 默认签到成功提示语
     */
    private static final String DEFAULT_SUCCESS_MESSAGE = "签到成功！";

    // ========== 院外活动配置 ==========
    /**
     * 院外小型活动第一次签到提示语配置
     * 格式：activity_type1:message1,activity_type2:message2
     */
    @Value("${signin.message.outside.small.first:}")
    private String outsideSmallFirstMessages;

    /**
     * 院外小型活动第二次签到提示语配置
     */
    @Value("${signin.message.outside.small.second:}")
    private String outsideSmallSecondMessages;

    /**
     * 院外中型活动第一次签到提示语配置
     */
    @Value("${signin.message.outside.medium.first:}")
    private String outsideMediumFirstMessages;

    /**
     * 院外中型活动第二次签到提示语配置
     */
    @Value("${signin.message.outside.medium.second:}")
    private String outsideMediumSecondMessages;

    /**
     * 院外大型活动第一次签到提示语配置
     */
    @Value("${signin.message.outside.large.first:}")
    private String outsideLargeFirstMessages;

    /**
     * 院外大型活动第二次签到提示语配置
     */
    @Value("${signin.message.outside.large.second:}")
    private String outsideLargeSecondMessages;

    // ========== 院内活动配置 ==========
    /**
     * 院内活动第一次签到提示语配置
     */
    @Value("${signin.message.inside.first:}")
    private String insideFirstMessages;

    /**
     * 院内活动第二次签到提示语配置
     */
    @Value("${signin.message.inside.second:}")
    private String insideSecondMessages;

    // ========== CS活动配置 ==========
    /**
     * CS活动第一次签到提示语配置
     */
    @Value("${signin.message.cs.first:}")
    private String csFirstMessages;

    /**
     * CS活动第二次签到提示语配置
     */
    @Value("${signin.message.cs.second:}")
    private String csSecondMessages;

    /**
     * CS活动第三次及以上签到提示语配置
     */
    @Value("${signin.message.cs.third:}")
    private String csThirdMessages;

    /**
     * 根据签到类型、课程类型、活动类型和当月签到历史获取签到成功提示语
     *
     * @param signInType        签到类型（INSIDE_PCLASS/OUTSIDE_PCLASS/CS）
     * @param pclassType        课程类型（小型活动/中型活动/大型活动）
     * @param activityType      活动类型（高端妈妈班/豪华妈妈班/精品妈妈班等）
     * @param signInHistoryList 当月签到历史
     * @return 签到成功提示语
     */
    public String getSignInSuccessMessage(String signInType, String pclassType, String activityType,
                                        List<SignInHistory> signInHistoryList) {
        try {
            log.debug("获取签到提示语，signInType: {}, pclassType: {}, activityType: {}, 当月签到历史数量: {}",
                    signInType, pclassType, activityType, signInHistoryList != null ? signInHistoryList.size() : 0);

            if (!StringUtils.hasText(signInType)) {
                log.warn("签到类型为空，使用默认提示语");
                return DEFAULT_SUCCESS_MESSAGE;
            }

            // 计算具体的签到次数（包括本次签到）
            int signInCount = calculateSpecificSignInCount(signInType, pclassType, activityType, signInHistoryList) + 1;
            log.debug("计算得出的具体签到次数: {}", signInCount);

            String configMessages = getConfigMessagesBySignInTypeAndCount(signInType, pclassType, signInCount);
            if (!StringUtils.hasText(configMessages)) {
                log.warn("未找到签到类型 {} 课程类型 {} 第{}次签到的配置，使用默认提示语", signInType, pclassType, signInCount);
                return DEFAULT_SUCCESS_MESSAGE;
            }

            Map<String, String> messageMap = parseConfigMessages(configMessages);
            String message = messageMap.get(activityType);

            if (StringUtils.hasText(message)) {
                log.debug("找到匹配的提示语: {}", message);
                return message;
            } else {
                log.warn("未找到活动类型 {} 的提示语配置，使用默认提示语", activityType);
                return DEFAULT_SUCCESS_MESSAGE;
            }

        } catch (Exception e) {
            log.error("获取签到提示语时发生异常，signInType: {}, pclassType: {}, activityType: {}",
                    signInType, pclassType, activityType, e);
            return DEFAULT_SUCCESS_MESSAGE;
        }
    }

    /**
     * 计算具体的签到次数
     * - 院外活动：按课程类型分别计算（小型活动、中型活动、大型活动）
     * - 中型活动：还需要按活动类型分别计算（高端妈妈班、豪华妈妈班、精品妈妈班）
     * - 院内活动：按院内活动整体计算
     * - CS活动：按CS活动整体计算
     *
     * @param signInType        签到类型
     * @param pclassType        课程类型
     * @param activityType      活动类型
     * @param signInHistoryList 当月签到历史
     * @return 签到次数
     */
    private int calculateSpecificSignInCount(String signInType, String pclassType, String activityType,
                                           List<SignInHistory> signInHistoryList) {
        if (signInHistoryList == null || signInHistoryList.isEmpty()) {
            return 0;
        }

        if ("OUTSIDE_PCLASS".equals(signInType)) {
            // 院外活动：按课程类型分别计算签到次数
            if ("中型活动".equals(pclassType)) {
                // 中型活动：还需要按活动类型分别计算
                return (int) signInHistoryList.stream()
                        .filter(history -> "OUTSIDE_PCLASS".equals(history.getChannel())
                                && "中型活动".equals(history.getPclassType())
                                && activityType.equals(history.getActivityType()))
                        .count();
            } else {
                // 小型活动、大型活动：按课程类型计算
                return (int) signInHistoryList.stream()
                        .filter(history -> "OUTSIDE_PCLASS".equals(history.getChannel())
                                && pclassType.equals(history.getPclassType()))
                        .count();
            }
        } else if ("INSIDE_PCLASS".equals(signInType)) {
            // 院内活动：按院内活动整体计算
            return (int) signInHistoryList.stream()
                    .filter(history -> "INSIDE_PCLASS".equals(history.getChannel()))
                    .count();
        } else if ("CS".equals(signInType)) {
            // CS活动：按CS活动整体计算
            return (int) signInHistoryList.stream()
                    .filter(history -> "CS".equals(history.getChannel()))
                    .count();
        }

        return 0;
    }

    /**
     * 根据签到类型、课程类型和签到次数获取对应的配置字符串
     *
     * @param signInType  签到类型
     * @param pclassType  课程类型
     * @param signInCount 签到次数
     * @return 配置字符串
     */
    private String getConfigMessagesBySignInTypeAndCount(String signInType, String pclassType, int signInCount) {
        if (signInType == null) {
            return null;
        }

        switch (signInType.trim()) {
            case "OUTSIDE_PCLASS":
                return getOutsideActivityMessages(pclassType, signInCount);
            case "INSIDE_PCLASS":
                return getInsideActivityMessages(signInCount);
            case "CS":
                return getCsActivityMessages(signInCount);
            default:
                log.warn("未知的签到类型: {}", signInType);
                return null;
        }
    }

    /**
     * 获取院外活动提示语配置
     */
    private String getOutsideActivityMessages(String pclassType, int signInCount) {
        if (pclassType == null) {
            return null;
        }

        boolean isFirst = signInCount == 1;

        switch (pclassType.trim()) {
            case "小型活动":
                return isFirst ? outsideSmallFirstMessages : outsideSmallSecondMessages;
            case "中型活动":
                return isFirst ? outsideMediumFirstMessages : outsideMediumSecondMessages;
            case "大型活动":
                return isFirst ? outsideLargeFirstMessages : outsideLargeSecondMessages;
            default:
                log.warn("未知的院外活动课程类型: {}", pclassType);
                return null;
        }
    }

    /**
     * 获取院内活动提示语配置
     */
    private String getInsideActivityMessages(int signInCount) {
        return signInCount == 1 ? insideFirstMessages : insideSecondMessages;
    }

    /**
     * 获取CS活动提示语配置
     */
    private String getCsActivityMessages(int signInCount) {
        if (signInCount == 1) {
            return csFirstMessages;
        } else if (signInCount == 2) {
            return csSecondMessages;
        } else {
            return csThirdMessages;
        }
    }

    /**
     * 解析配置字符串为 Map
     * 格式：activity_type1:message1,activity_type2:message2
     *
     * @param configMessages 配置字符串
     * @return 解析后的 Map
     */
    private Map<String, String> parseConfigMessages(String configMessages) {
        Map<String, String> messageMap = new HashMap<>();

        if (!StringUtils.hasText(configMessages)) {
            return messageMap;
        }

        try {
            String[] pairs = configMessages.split(",");
            for (String pair : pairs) {
                if (StringUtils.hasText(pair)) {
                    String[] keyValue = pair.trim().split(":");
                    if (keyValue.length == 2) {
                        String activityType = keyValue[0].trim();
                        String message = keyValue[1].trim();
                        if (StringUtils.hasText(activityType) && StringUtils.hasText(message)) {
                            messageMap.put(activityType, message);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析配置字符串时发生异常: {}", configMessages, e);
        }

        return messageMap;
    }

    /**
     * 获取默认签到成功提示语
     *
     * @return 默认提示语
     */
    public String getDefaultSuccessMessage() {
        return DEFAULT_SUCCESS_MESSAGE;
    }
}
