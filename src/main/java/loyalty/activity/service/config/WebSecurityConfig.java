package loyalty.activity.service.config;

import loyalty.activity.service.common.security.CommonAppCallAuthenticationProvider;
import loyalty.activity.service.common.security.UserTokenFilter;
import loyalty.activity.service.pclass.db.mapper.ImitationTestMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

@EnableWebSecurity
@Order(2)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
    private static final String ACCESS = "/access/**";
    private static final String SWAGGER_URL_v3 = "/swagger-ui/**";
    private static final String SWAGGER_RESOURCE_URL = "/webjars/springfox-swagger-ui/**";
    private static final String SWAGGER_RESOURCE_URL2 = "/swagger-resources/**";
    private static final String SWAGGER_RESOURCE_URL3 = "/v2/**";
    private static final String ACUTATOR = "/actuator/**";
    private static final String LOGGER = "/loggers/**";
    private static final String NCP_ACCESS = "/ncp/access/**";

    @Value("${security.enable.appauth.check:true}")
    private Boolean enableAppAuthCheck;
    @Autowired
    private ImitationTestMapper imitationTestMapper;

    protected String[] getPermitAllUrlPatternsForPOST() {
        return new String[]{LOGGER};
    }


    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers(ACCESS,
                SWAGGER_URL_v3, SWAGGER_RESOURCE_URL, SWAGGER_RESOURCE_URL2, SWAGGER_RESOURCE_URL3,
                LOGGER,
                ACUTATOR, NCP_ACCESS);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .antMatchers(HttpMethod.GET,
                        getPermitAllUrlPatternsForGet()).permitAll()
                .antMatchers(HttpMethod.POST, getPermitAllUrlPatternsForPOST()).permitAll()
                .anyRequest().hasAuthority("AppUser").and()
                .addFilter(new UserTokenFilter(authenticationManager()))
                .csrf().disable();
    }

    protected String[] getPermitAllUrlPatternsForGet() {
        return new String[]{ACCESS,
                SWAGGER_URL_v3, SWAGGER_RESOURCE_URL, SWAGGER_RESOURCE_URL2, SWAGGER_RESOURCE_URL3,
                LOGGER,
                ACUTATOR, NCP_ACCESS};

    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(commonAllCallAuthProvider());
    }

    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Bean("commonAllCallAuthProvider")
    protected AuthenticationProvider commonAllCallAuthProvider() {
        return new CommonAppCallAuthenticationProvider(enableAppAuthCheck,imitationTestMapper);
    }


}
