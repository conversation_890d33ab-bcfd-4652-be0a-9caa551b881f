package loyalty.activity.service.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("common.web.client")
public class WebclientProperties {
    /**
     * 超时时间 单位秒
     */
    private int pendingAcquireTimeout = 15;

    /**
     * 最大等待任务数
     */
    private int pendingAcquireMaxCount = 3000;

    /**
     * 连接池最大连接数
     */
    private int maxConnections = 1500;
}
