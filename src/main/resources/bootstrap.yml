jasypt:
  encryptor:
    password: loyalty-activity-servicefikMc
server:
  port: 9072
  servlet:
    context-path: /loyalty-activity-service
spring:
  application:
    name: loyalty-activity-service
  cloud:
    nacos:
      server-addr: 192.168.0.240:8848
      config:
        file-extension: yaml
        namespace: loyalty-activity-service
  config:
    activate:
      on-profile: local
logging:
  config: classpath:logback-demo.xml
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
---
jasypt:
  encryptor:
    password: loyalty-activity-servicefikMc
server:
  port: 9072
  servlet:
    context-path: /loyalty-activity-service
spring:
  application:
    name: loyalty-activity-service
  cloud:
    nacos:
      server-addr: 192.168.0.240:8848
      config:
        file-extension: yaml
        namespace: loyalty-activity-service
  config:
    activate:
      on-profile: demo
logging:
  config: classpath:logback-demo.xml
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
---
jasypt:
  encryptor:
    password: loyalty-activity-servicefikMc
server:
  port: 9072
  servlet:
    context-path: /loyalty-activity-service
spring:
  application:
    name: loyalty-activity-service
  cloud:
    nacos:
      server-addr: 192.168.0.240:8848
      config:
        file-extension: yaml
        namespace: loyalty-activity-service
        refresh:
          enabled: true
  config:
    activate:
      on-profile: uat
logging:
  config: classpath:logback-demo.xml
  level:
    loyalty.activity.service.pnec.db.mapper: debug
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
---
jasypt:
  encryptor:
    password: loyalty-activity-servicefiEOF
server:
  port: 9072
  servlet:
    context-path: /loyalty-activity-service
spring:
  application:
    name: loyalty-activity-service
  cloud:
    nacos:
      server-addr: nacos.tools.svc.cluster.local:8848
      config:
        file-extension: yaml
        namespace: loyalty-activity-service
        refresh:
          enabled: true
  config:
    activate:
      on-profile: prod
logging:
  config: classpath:logback-prod.xml
---
jasypt:
  encryptor:
    password: loyalty-activity-servicefikMc
server:
  port: 9072
  servlet:
    context-path: /loyalty-activity-service
spring:
  application:
    name: loyalty-activity-service
  cloud:
    nacos:
      server-addr: 192.168.0.205:8848
      config:
        file-extension: yaml
        namespace: loyalty-activity-service
  config:
    activate:
      on-profile: sit
logging:
  config: classpath:logback-sit.xml

