spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************
    username: 
    password: ENC(nCzHVr6jItyvoC3+qEIAHA==)
    tomcat:
      max-wait: 10000
      max-idle: 30
rest:
  call:
    pool:
      max-total: 5000
      max-per-route: 5000
      validate-after-inactivity: 5000
    connect:
      timeout: 5000
    read:
      timeout: 5000
  http:
    timeout: 5000
security:
  enable:
    appauth:
      check: false

