<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true" scanPeriod="30 seconds">
    <timestamp key="byDate" datePattern="yyyy-MM-dd_HH.mm.ss"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%green(%d{yyyy-MM-dd HH:mm:ss})[%X{trace}][%blue(%thread)] [%class:%red(%line)] [%magenta(%level)] - %m%n
            </pattern>
        </encoder>
    </appender>

    <appender name="ERROR-OUT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志存放位置 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}[%X{trace}][%thread] [%class:%line] - %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/error-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>90</maxHistory>
            <maxFileSize>50MB</maxFileSize>
        </rollingPolicy>
    </appender>


    <appender name="INFO-OUT"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}[%X{trace}][%thread] [%class:%line] [%level] - %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/info-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>60</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
    </appender>


    <!-- 配置好前面对应的appender -->
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ERROR-OUT"/>
        <appender-ref ref="INFO-OUT"/>

    </root>
</configuration>
