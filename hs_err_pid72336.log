#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 34816 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (c:/BuildAgent/work/1eae4dac1d648407/src/hotspot/share/memory/arena.cpp:197), pid=72336, tid=77080
#
# JRE version: OpenJDK Runtime Environment (11.0.6+8) (build 11.0.6+8-b765.25)
# Java VM: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 22621 (10.0.22621.1928)
Time: Fri Aug  4 05:07:39 2023 �й���׼ʱ�� elapsed time: 0 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000012f4c031800):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=77080, stack(0x0000001eb8600000,0x0000001eb8700000)]


Current CompileTask:
C2:     52   68       4       java.lang.String::hashCode (49 bytes)

Stack: [0x0000001eb8600000,0x0000001eb8700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5de3ca]
V  [jvm.dll+0x710ae5]
V  [jvm.dll+0x712005]
V  [jvm.dll+0x7126b3]
V  [jvm.dll+0x23e708]
V  [jvm.dll+0xb89bc]
V  [jvm.dll+0xb8ebc]
V  [jvm.dll+0x2b20b4]
V  [jvm.dll+0x502cc2]
V  [jvm.dll+0x1f9108]
V  [jvm.dll+0x1f6a3f]
V  [jvm.dll+0x17f90c]
V  [jvm.dll+0x204ed7]
V  [jvm.dll+0x203741]
V  [jvm.dll+0x6daa4f]
V  [jvm.dll+0x6d38ad]
V  [jvm.dll+0x5dd366]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x126ad]
C  [ntdll.dll+0x5aa68]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000012f4bff4920, length=11, elements={
0x0000012f2221c800, 0x0000012f4c00f800, 0x0000012f4c010800, 0x0000012f4c02f800,
0x0000012f4c030800, 0x0000012f4c031800, 0x0000012f4c032800, 0x0000012f4c033800,
0x0000012f4c177000, 0x0000012f4c17a000, 0x0000012f4c199800
}

Java Threads: ( => current thread )
  0x0000012f2221c800 JavaThread "main" [_thread_in_vm, id=77272, stack(0x0000001eb7b00000,0x0000001eb7c00000)]
  0x0000012f4c00f800 JavaThread "Reference Handler" daemon [_thread_blocked, id=74376, stack(0x0000001eb8200000,0x0000001eb8300000)]
  0x0000012f4c010800 JavaThread "Finalizer" daemon [_thread_blocked, id=76432, stack(0x0000001eb8300000,0x0000001eb8400000)]
  0x0000012f4c02f800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=72684, stack(0x0000001eb8400000,0x0000001eb8500000)]
  0x0000012f4c030800 JavaThread "Attach Listener" daemon [_thread_blocked, id=73352, stack(0x0000001eb8500000,0x0000001eb8600000)]
=>0x0000012f4c031800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=77080, stack(0x0000001eb8600000,0x0000001eb8700000)]
  0x0000012f4c032800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=72672, stack(0x0000001eb8700000,0x0000001eb8800000)]
  0x0000012f4c033800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=12120, stack(0x0000001eb8800000,0x0000001eb8900000)]
  0x0000012f4c177000 JavaThread "Service Thread" daemon [_thread_blocked, id=70552, stack(0x0000001eb8900000,0x0000001eb8a00000)]
  0x0000012f4c17a000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=75788, stack(0x0000001eb8b00000,0x0000001eb8c00000)]
  0x0000012f4c199800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=77192, stack(0x0000001eb8c00000,0x0000001eb8d00000)]

Other Threads:
  0x0000012f4c00c000 VMThread "VM Thread" [stack: 0x0000001eb8100000,0x0000001eb8200000] [id=75724]
  0x0000012f4c178000 WatcherThread [stack: 0x0000001eb8a00000,0x0000001eb8b00000] [id=74748]
  0x0000012f22236800 GCTaskThread "GC Thread#0" [stack: 0x0000001eb7c00000,0x0000001eb7d00000] [id=74956]
  0x0000012f222b5800 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000001eb7d00000,0x0000001eb7e00000] [id=75720]
  0x0000012f222b7800 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000001eb7e00000,0x0000001eb7f00000] [id=70924]
  0x0000012f4b71d800 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000001eb7f00000,0x0000001eb8000000] [id=72900]
  0x0000012f4b720800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x0000001eb8000000,0x0000001eb8100000] [id=71112]

Threads with active compile tasks:
C2 CompilerThread0     89   68       4       java.lang.String::hashCode (49 bytes)
C1 CompilerThread0     89  162       3       java.nio.DirectByteBuffer::ix (10 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000603e00000, size: 8130 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000800000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000840000000

Heap:
 garbage-first heap   total 522240K, used 2048K [0x0000000603e00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 0 survivors (0K)
 Metaspace       used 510K, capacity 4603K, committed 4864K, reserved 1056768K
  class space    used 43K, capacity 425K, committed 512K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000603e00000, 0x0000000603e00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603e00000, 0x0000000603e00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604200000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|   2|0x0000000604200000, 0x0000000604200000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604200000, 0x0000000604200000| Untracked 
|   3|0x0000000604400000, 0x0000000604400000, 0x0000000604600000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|   4|0x0000000604600000, 0x0000000604600000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604600000, 0x0000000604600000| Untracked 
|   5|0x0000000604800000, 0x0000000604800000, 0x0000000604a00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|   6|0x0000000604a00000, 0x0000000604a00000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604a00000, 0x0000000604a00000| Untracked 
|   7|0x0000000604c00000, 0x0000000604c00000, 0x0000000604e00000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   8|0x0000000604e00000, 0x0000000604e00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604e00000, 0x0000000604e00000| Untracked 
|   9|0x0000000605000000, 0x0000000605000000, 0x0000000605200000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  10|0x0000000605200000, 0x0000000605200000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605200000, 0x0000000605200000| Untracked 
|  11|0x0000000605400000, 0x0000000605400000, 0x0000000605600000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  12|0x0000000605600000, 0x0000000605600000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605600000, 0x0000000605600000| Untracked 
|  13|0x0000000605800000, 0x0000000605800000, 0x0000000605a00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  14|0x0000000605a00000, 0x0000000605a00000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605a00000, 0x0000000605a00000| Untracked 
|  15|0x0000000605c00000, 0x0000000605c00000, 0x0000000605e00000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  16|0x0000000605e00000, 0x0000000605e00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605e00000, 0x0000000605e00000| Untracked 
|  17|0x0000000606000000, 0x0000000606000000, 0x0000000606200000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  18|0x0000000606200000, 0x0000000606200000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606200000, 0x0000000606200000| Untracked 
|  19|0x0000000606400000, 0x0000000606400000, 0x0000000606600000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  20|0x0000000606600000, 0x0000000606600000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606600000, 0x0000000606600000| Untracked 
|  21|0x0000000606800000, 0x0000000606800000, 0x0000000606a00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  22|0x0000000606a00000, 0x0000000606a00000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606a00000, 0x0000000606a00000| Untracked 
|  23|0x0000000606c00000, 0x0000000606c00000, 0x0000000606e00000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  24|0x0000000606e00000, 0x0000000606e00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606e00000, 0x0000000606e00000| Untracked 
|  25|0x0000000607000000, 0x0000000607000000, 0x0000000607200000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  26|0x0000000607200000, 0x0000000607200000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607200000, 0x0000000607200000| Untracked 
|  27|0x0000000607400000, 0x0000000607400000, 0x0000000607600000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  28|0x0000000607600000, 0x0000000607600000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607600000, 0x0000000607600000| Untracked 
|  29|0x0000000607800000, 0x0000000607800000, 0x0000000607a00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  30|0x0000000607a00000, 0x0000000607a00000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607a00000, 0x0000000607a00000| Untracked 
|  31|0x0000000607c00000, 0x0000000607c00000, 0x0000000607e00000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  32|0x0000000607e00000, 0x0000000607e00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607e00000, 0x0000000607e00000| Untracked 
|  33|0x0000000608000000, 0x0000000608000000, 0x0000000608200000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  34|0x0000000608200000, 0x0000000608200000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608200000, 0x0000000608200000| Untracked 
|  35|0x0000000608400000, 0x0000000608400000, 0x0000000608600000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  36|0x0000000608600000, 0x0000000608600000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608600000, 0x0000000608600000| Untracked 
|  37|0x0000000608800000, 0x0000000608800000, 0x0000000608a00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  38|0x0000000608a00000, 0x0000000608a00000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608a00000, 0x0000000608a00000| Untracked 
|  39|0x0000000608c00000, 0x0000000608c00000, 0x0000000608e00000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  40|0x0000000608e00000, 0x0000000608e00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608e00000, 0x0000000608e00000| Untracked 
|  41|0x0000000609000000, 0x0000000609000000, 0x0000000609200000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  42|0x0000000609200000, 0x0000000609200000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609200000, 0x0000000609200000| Untracked 
|  43|0x0000000609400000, 0x0000000609400000, 0x0000000609600000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  44|0x0000000609600000, 0x0000000609600000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609600000, 0x0000000609600000| Untracked 
|  45|0x0000000609800000, 0x0000000609800000, 0x0000000609a00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  46|0x0000000609a00000, 0x0000000609a00000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609a00000, 0x0000000609a00000| Untracked 
|  47|0x0000000609c00000, 0x0000000609c00000, 0x0000000609e00000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  48|0x0000000609e00000, 0x0000000609e00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609e00000, 0x0000000609e00000| Untracked 
|  49|0x000000060a000000, 0x000000060a000000, 0x000000060a200000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  50|0x000000060a200000, 0x000000060a200000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a200000, 0x000000060a200000| Untracked 
|  51|0x000000060a400000, 0x000000060a400000, 0x000000060a600000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  52|0x000000060a600000, 0x000000060a600000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a600000, 0x000000060a600000| Untracked 
|  53|0x000000060a800000, 0x000000060a800000, 0x000000060aa00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  54|0x000000060aa00000, 0x000000060aa00000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060aa00000, 0x000000060aa00000| Untracked 
|  55|0x000000060ac00000, 0x000000060ac00000, 0x000000060ae00000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  56|0x000000060ae00000, 0x000000060ae00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ae00000, 0x000000060ae00000| Untracked 
|  57|0x000000060b000000, 0x000000060b000000, 0x000000060b200000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  58|0x000000060b200000, 0x000000060b200000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b200000, 0x000000060b200000| Untracked 
|  59|0x000000060b400000, 0x000000060b400000, 0x000000060b600000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  60|0x000000060b600000, 0x000000060b600000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b600000, 0x000000060b600000| Untracked 
|  61|0x000000060b800000, 0x000000060b800000, 0x000000060ba00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  62|0x000000060ba00000, 0x000000060ba00000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060ba00000, 0x000000060ba00000| Untracked 
|  63|0x000000060bc00000, 0x000000060bc00000, 0x000000060be00000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  64|0x000000060be00000, 0x000000060be00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060be00000, 0x000000060be00000| Untracked 
|  65|0x000000060c000000, 0x000000060c000000, 0x000000060c200000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  66|0x000000060c200000, 0x000000060c200000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c200000, 0x000000060c200000| Untracked 
|  67|0x000000060c400000, 0x000000060c400000, 0x000000060c600000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  68|0x000000060c600000, 0x000000060c600000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c600000, 0x000000060c600000| Untracked 
|  69|0x000000060c800000, 0x000000060c800000, 0x000000060ca00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  70|0x000000060ca00000, 0x000000060ca00000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060ca00000, 0x000000060ca00000| Untracked 
|  71|0x000000060cc00000, 0x000000060cc00000, 0x000000060ce00000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  72|0x000000060ce00000, 0x000000060ce00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060ce00000, 0x000000060ce00000| Untracked 
|  73|0x000000060d000000, 0x000000060d000000, 0x000000060d200000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  74|0x000000060d200000, 0x000000060d200000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d200000, 0x000000060d200000| Untracked 
|  75|0x000000060d400000, 0x000000060d400000, 0x000000060d600000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  76|0x000000060d600000, 0x000000060d600000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d600000, 0x000000060d600000| Untracked 
|  77|0x000000060d800000, 0x000000060d800000, 0x000000060da00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  78|0x000000060da00000, 0x000000060da00000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060da00000, 0x000000060da00000| Untracked 
|  79|0x000000060dc00000, 0x000000060dc00000, 0x000000060de00000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  80|0x000000060de00000, 0x000000060de00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060de00000, 0x000000060de00000| Untracked 
|  81|0x000000060e000000, 0x000000060e000000, 0x000000060e200000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  82|0x000000060e200000, 0x000000060e200000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e200000, 0x000000060e200000| Untracked 
|  83|0x000000060e400000, 0x000000060e400000, 0x000000060e600000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  84|0x000000060e600000, 0x000000060e600000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e600000, 0x000000060e600000| Untracked 
|  85|0x000000060e800000, 0x000000060e800000, 0x000000060ea00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  86|0x000000060ea00000, 0x000000060ea00000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060ea00000, 0x000000060ea00000| Untracked 
|  87|0x000000060ec00000, 0x000000060ec00000, 0x000000060ee00000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  88|0x000000060ee00000, 0x000000060ee00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ee00000, 0x000000060ee00000| Untracked 
|  89|0x000000060f000000, 0x000000060f000000, 0x000000060f200000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  90|0x000000060f200000, 0x000000060f200000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f200000, 0x000000060f200000| Untracked 
|  91|0x000000060f400000, 0x000000060f400000, 0x000000060f600000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  92|0x000000060f600000, 0x000000060f600000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f600000, 0x000000060f600000| Untracked 
|  93|0x000000060f800000, 0x000000060f800000, 0x000000060fa00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  94|0x000000060fa00000, 0x000000060fa00000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060fa00000, 0x000000060fa00000| Untracked 
|  95|0x000000060fc00000, 0x000000060fc00000, 0x000000060fe00000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  96|0x000000060fe00000, 0x000000060fe00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fe00000, 0x000000060fe00000| Untracked 
|  97|0x0000000610000000, 0x0000000610000000, 0x0000000610200000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  98|0x0000000610200000, 0x0000000610200000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610200000, 0x0000000610200000| Untracked 
|  99|0x0000000610400000, 0x0000000610400000, 0x0000000610600000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
| 100|0x0000000610600000, 0x0000000610600000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610600000, 0x0000000610600000| Untracked 
| 101|0x0000000610800000, 0x0000000610800000, 0x0000000610a00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
| 102|0x0000000610a00000, 0x0000000610a00000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610a00000, 0x0000000610a00000| Untracked 
| 103|0x0000000610c00000, 0x0000000610c00000, 0x0000000610e00000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
| 104|0x0000000610e00000, 0x0000000610e00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610e00000, 0x0000000610e00000| Untracked 
| 105|0x0000000611000000, 0x0000000611000000, 0x0000000611200000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
| 106|0x0000000611200000, 0x0000000611200000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611200000, 0x0000000611200000| Untracked 
| 107|0x0000000611400000, 0x0000000611400000, 0x0000000611600000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
| 108|0x0000000611600000, 0x0000000611600000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611600000, 0x0000000611600000| Untracked 
| 109|0x0000000611800000, 0x0000000611800000, 0x0000000611a00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
| 110|0x0000000611a00000, 0x0000000611a00000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611a00000, 0x0000000611a00000| Untracked 
| 111|0x0000000611c00000, 0x0000000611c00000, 0x0000000611e00000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
| 112|0x0000000611e00000, 0x0000000611e00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611e00000, 0x0000000611e00000| Untracked 
| 113|0x0000000612000000, 0x0000000612000000, 0x0000000612200000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
| 114|0x0000000612200000, 0x0000000612200000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612200000, 0x0000000612200000| Untracked 
| 115|0x0000000612400000, 0x0000000612400000, 0x0000000612600000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
| 116|0x0000000612600000, 0x0000000612600000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612600000, 0x0000000612600000| Untracked 
| 117|0x0000000612800000, 0x0000000612800000, 0x0000000612a00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
| 118|0x0000000612a00000, 0x0000000612a00000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612a00000, 0x0000000612a00000| Untracked 
| 119|0x0000000612c00000, 0x0000000612c00000, 0x0000000612e00000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
| 120|0x0000000612e00000, 0x0000000612e00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612e00000, 0x0000000612e00000| Untracked 
| 121|0x0000000613000000, 0x0000000613000000, 0x0000000613200000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
| 122|0x0000000613200000, 0x0000000613200000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613200000, 0x0000000613200000| Untracked 
| 123|0x0000000613400000, 0x0000000613400000, 0x0000000613600000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
| 124|0x0000000613600000, 0x0000000613600000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613600000, 0x0000000613600000| Untracked 
| 125|0x0000000613800000, 0x0000000613800000, 0x0000000613a00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
| 126|0x0000000613a00000, 0x0000000613a00000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613a00000, 0x0000000613a00000| Untracked 
| 127|0x0000000613c00000, 0x0000000613c00000, 0x0000000613e00000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
| 128|0x0000000613e00000, 0x0000000613e00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613e00000, 0x0000000613e00000| Untracked 
| 129|0x0000000614000000, 0x0000000614000000, 0x0000000614200000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
| 130|0x0000000614200000, 0x0000000614200000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614200000, 0x0000000614200000| Untracked 
| 131|0x0000000614400000, 0x0000000614400000, 0x0000000614600000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
| 132|0x0000000614600000, 0x0000000614600000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614600000, 0x0000000614600000| Untracked 
| 133|0x0000000614800000, 0x0000000614800000, 0x0000000614a00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
| 134|0x0000000614a00000, 0x0000000614a00000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614a00000, 0x0000000614a00000| Untracked 
| 135|0x0000000614c00000, 0x0000000614c00000, 0x0000000614e00000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
| 136|0x0000000614e00000, 0x0000000614e00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614e00000, 0x0000000614e00000| Untracked 
| 137|0x0000000615000000, 0x0000000615000000, 0x0000000615200000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
| 138|0x0000000615200000, 0x0000000615200000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615200000, 0x0000000615200000| Untracked 
| 139|0x0000000615400000, 0x0000000615400000, 0x0000000615600000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
| 140|0x0000000615600000, 0x0000000615600000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615600000, 0x0000000615600000| Untracked 
| 141|0x0000000615800000, 0x0000000615800000, 0x0000000615a00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
| 142|0x0000000615a00000, 0x0000000615a00000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615a00000, 0x0000000615a00000| Untracked 
| 143|0x0000000615c00000, 0x0000000615c00000, 0x0000000615e00000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
| 144|0x0000000615e00000, 0x0000000615e00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615e00000, 0x0000000615e00000| Untracked 
| 145|0x0000000616000000, 0x0000000616000000, 0x0000000616200000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
| 146|0x0000000616200000, 0x0000000616200000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616200000, 0x0000000616200000| Untracked 
| 147|0x0000000616400000, 0x0000000616400000, 0x0000000616600000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
| 148|0x0000000616600000, 0x0000000616600000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616600000, 0x0000000616600000| Untracked 
| 149|0x0000000616800000, 0x0000000616800000, 0x0000000616a00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
| 150|0x0000000616a00000, 0x0000000616a00000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616a00000, 0x0000000616a00000| Untracked 
| 151|0x0000000616c00000, 0x0000000616c00000, 0x0000000616e00000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
| 152|0x0000000616e00000, 0x0000000616e00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616e00000, 0x0000000616e00000| Untracked 
| 153|0x0000000617000000, 0x0000000617000000, 0x0000000617200000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
| 154|0x0000000617200000, 0x0000000617200000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617200000, 0x0000000617200000| Untracked 
| 155|0x0000000617400000, 0x0000000617400000, 0x0000000617600000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
| 156|0x0000000617600000, 0x0000000617600000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617600000, 0x0000000617600000| Untracked 
| 157|0x0000000617800000, 0x0000000617800000, 0x0000000617a00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
| 158|0x0000000617a00000, 0x0000000617a00000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617a00000, 0x0000000617a00000| Untracked 
| 159|0x0000000617c00000, 0x0000000617c00000, 0x0000000617e00000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
| 160|0x0000000617e00000, 0x0000000617e00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617e00000, 0x0000000617e00000| Untracked 
| 161|0x0000000618000000, 0x0000000618000000, 0x0000000618200000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
| 162|0x0000000618200000, 0x0000000618200000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618200000, 0x0000000618200000| Untracked 
| 163|0x0000000618400000, 0x0000000618400000, 0x0000000618600000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
| 164|0x0000000618600000, 0x0000000618600000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618600000, 0x0000000618600000| Untracked 
| 165|0x0000000618800000, 0x0000000618800000, 0x0000000618a00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
| 166|0x0000000618a00000, 0x0000000618a00000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618a00000, 0x0000000618a00000| Untracked 
| 167|0x0000000618c00000, 0x0000000618c00000, 0x0000000618e00000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
| 168|0x0000000618e00000, 0x0000000618e00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618e00000, 0x0000000618e00000| Untracked 
| 169|0x0000000619000000, 0x0000000619000000, 0x0000000619200000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
| 170|0x0000000619200000, 0x0000000619200000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619200000, 0x0000000619200000| Untracked 
| 171|0x0000000619400000, 0x0000000619400000, 0x0000000619600000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
| 172|0x0000000619600000, 0x0000000619600000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619600000, 0x0000000619600000| Untracked 
| 173|0x0000000619800000, 0x0000000619800000, 0x0000000619a00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
| 174|0x0000000619a00000, 0x0000000619a00000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619a00000, 0x0000000619a00000| Untracked 
| 175|0x0000000619c00000, 0x0000000619c00000, 0x0000000619e00000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
| 176|0x0000000619e00000, 0x0000000619e00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619e00000, 0x0000000619e00000| Untracked 
| 177|0x000000061a000000, 0x000000061a000000, 0x000000061a200000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
| 178|0x000000061a200000, 0x000000061a200000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a200000, 0x000000061a200000| Untracked 
| 179|0x000000061a400000, 0x000000061a400000, 0x000000061a600000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
| 180|0x000000061a600000, 0x000000061a600000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a600000, 0x000000061a600000| Untracked 
| 181|0x000000061a800000, 0x000000061a800000, 0x000000061aa00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
| 182|0x000000061aa00000, 0x000000061aa00000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061aa00000, 0x000000061aa00000| Untracked 
| 183|0x000000061ac00000, 0x000000061ac00000, 0x000000061ae00000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
| 184|0x000000061ae00000, 0x000000061ae00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ae00000, 0x000000061ae00000| Untracked 
| 185|0x000000061b000000, 0x000000061b000000, 0x000000061b200000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
| 186|0x000000061b200000, 0x000000061b200000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b200000, 0x000000061b200000| Untracked 
| 187|0x000000061b400000, 0x000000061b400000, 0x000000061b600000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
| 188|0x000000061b600000, 0x000000061b600000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b600000, 0x000000061b600000| Untracked 
| 189|0x000000061b800000, 0x000000061b800000, 0x000000061ba00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
| 190|0x000000061ba00000, 0x000000061ba00000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061ba00000, 0x000000061ba00000| Untracked 
| 191|0x000000061bc00000, 0x000000061bc00000, 0x000000061be00000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
| 192|0x000000061be00000, 0x000000061be00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061be00000, 0x000000061be00000| Untracked 
| 193|0x000000061c000000, 0x000000061c000000, 0x000000061c200000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
| 194|0x000000061c200000, 0x000000061c200000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c200000, 0x000000061c200000| Untracked 
| 195|0x000000061c400000, 0x000000061c400000, 0x000000061c600000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
| 196|0x000000061c600000, 0x000000061c600000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c600000, 0x000000061c600000| Untracked 
| 197|0x000000061c800000, 0x000000061c800000, 0x000000061ca00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
| 198|0x000000061ca00000, 0x000000061ca00000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061ca00000, 0x000000061ca00000| Untracked 
| 199|0x000000061cc00000, 0x000000061cc00000, 0x000000061ce00000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
| 200|0x000000061ce00000, 0x000000061ce00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061ce00000, 0x000000061ce00000| Untracked 
| 201|0x000000061d000000, 0x000000061d000000, 0x000000061d200000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
| 202|0x000000061d200000, 0x000000061d200000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d200000, 0x000000061d200000| Untracked 
| 203|0x000000061d400000, 0x000000061d400000, 0x000000061d600000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 204|0x000000061d600000, 0x000000061d600000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d600000, 0x000000061d600000| Untracked 
| 205|0x000000061d800000, 0x000000061d800000, 0x000000061da00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 206|0x000000061da00000, 0x000000061da00000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061da00000, 0x000000061da00000| Untracked 
| 207|0x000000061dc00000, 0x000000061dc00000, 0x000000061de00000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 208|0x000000061de00000, 0x000000061de00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061de00000, 0x000000061de00000| Untracked 
| 209|0x000000061e000000, 0x000000061e000000, 0x000000061e200000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 210|0x000000061e200000, 0x000000061e200000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e200000, 0x000000061e200000| Untracked 
| 211|0x000000061e400000, 0x000000061e400000, 0x000000061e600000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 212|0x000000061e600000, 0x000000061e600000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e600000, 0x000000061e600000| Untracked 
| 213|0x000000061e800000, 0x000000061e800000, 0x000000061ea00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 214|0x000000061ea00000, 0x000000061ea00000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061ea00000, 0x000000061ea00000| Untracked 
| 215|0x000000061ec00000, 0x000000061ec00000, 0x000000061ee00000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 216|0x000000061ee00000, 0x000000061ee00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ee00000, 0x000000061ee00000| Untracked 
| 217|0x000000061f000000, 0x000000061f000000, 0x000000061f200000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 218|0x000000061f200000, 0x000000061f200000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f200000, 0x000000061f200000| Untracked 
| 219|0x000000061f400000, 0x000000061f400000, 0x000000061f600000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 220|0x000000061f600000, 0x000000061f600000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f600000, 0x000000061f600000| Untracked 
| 221|0x000000061f800000, 0x000000061f800000, 0x000000061fa00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 222|0x000000061fa00000, 0x000000061fa00000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061fa00000, 0x000000061fa00000| Untracked 
| 223|0x000000061fc00000, 0x000000061fc00000, 0x000000061fe00000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 224|0x000000061fe00000, 0x000000061fe00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fe00000, 0x000000061fe00000| Untracked 
| 225|0x0000000620000000, 0x0000000620000000, 0x0000000620200000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 226|0x0000000620200000, 0x0000000620200000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620200000, 0x0000000620200000| Untracked 
| 227|0x0000000620400000, 0x0000000620400000, 0x0000000620600000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 228|0x0000000620600000, 0x0000000620600000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620600000, 0x0000000620600000| Untracked 
| 229|0x0000000620800000, 0x0000000620800000, 0x0000000620a00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 230|0x0000000620a00000, 0x0000000620a00000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620a00000, 0x0000000620a00000| Untracked 
| 231|0x0000000620c00000, 0x0000000620c00000, 0x0000000620e00000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 232|0x0000000620e00000, 0x0000000620e00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620e00000, 0x0000000620e00000| Untracked 
| 233|0x0000000621000000, 0x0000000621000000, 0x0000000621200000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 234|0x0000000621200000, 0x0000000621200000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621200000, 0x0000000621200000| Untracked 
| 235|0x0000000621400000, 0x0000000621400000, 0x0000000621600000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 236|0x0000000621600000, 0x0000000621600000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621600000, 0x0000000621600000| Untracked 
| 237|0x0000000621800000, 0x0000000621800000, 0x0000000621a00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 238|0x0000000621a00000, 0x0000000621a00000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621a00000, 0x0000000621a00000| Untracked 
| 239|0x0000000621c00000, 0x0000000621c00000, 0x0000000621e00000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 240|0x0000000621e00000, 0x0000000621e00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621e00000, 0x0000000621e00000| Untracked 
| 241|0x0000000622000000, 0x0000000622000000, 0x0000000622200000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 242|0x0000000622200000, 0x0000000622200000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622200000, 0x0000000622200000| Untracked 
| 243|0x0000000622400000, 0x0000000622400000, 0x0000000622600000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 244|0x0000000622600000, 0x0000000622600000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622600000, 0x0000000622600000| Untracked 
| 245|0x0000000622800000, 0x0000000622800000, 0x0000000622a00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 246|0x0000000622a00000, 0x0000000622a00000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622a00000, 0x0000000622a00000| Untracked 
| 247|0x0000000622c00000, 0x0000000622c00000, 0x0000000622e00000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 248|0x0000000622e00000, 0x0000000622e00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622e00000, 0x0000000622e00000| Untracked 
| 249|0x0000000623000000, 0x0000000623000000, 0x0000000623200000|  0%| F|  |TAMS 0x0000000623000000, 0x0000000623000000| Untracked 
| 250|0x0000000623200000, 0x0000000623200000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623200000, 0x0000000623200000| Untracked 
| 251|0x0000000623400000, 0x0000000623400000, 0x0000000623600000|  0%| F|  |TAMS 0x0000000623400000, 0x0000000623400000| Untracked 
| 252|0x0000000623600000, 0x0000000623600000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623600000, 0x0000000623600000| Untracked 
| 253|0x0000000623800000, 0x00000006238fd370, 0x0000000623a00000| 49%| E|  |TAMS 0x0000000623800000, 0x0000000623800000| Complete 
| 254|0x0000000623a00000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623a00000, 0x0000000623a00000| Complete 

Card table byte_map: [0x0000012f37310000,0x0000012f38300000] _byte_map_base: 0x0000012f342f1000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000012f222a4d38, (CMBitMap*) 0x0000012f222a4d70
 Prev Bits: [0x0000012f392f0000, 0x0000012f411f8000)
 Next Bits: [0x0000012f41200000, 0x0000012f49108000)

Polling page: 0x0000012f222d0000

Metaspace:

Usage:
  Non-class:      4.14 MB capacity,   541.99 KB ( 13%) used,     3.61 MB ( 87%) free+waste,   576 bytes ( <1%) overhead. 
      Class:    425.00 KB capacity,    55.53 KB ( 13%) used,   369.03 KB ( 87%) free+waste,   448 bytes ( <1%) overhead. 
       Both:      4.56 MB capacity,   597.52 KB ( 13%) used,     3.97 MB ( 87%) free+waste,     1.00 KB ( <1%) overhead. 

Virtual space:
  Non-class space:        8.00 MB reserved,       4.25 MB ( 53%) committed 
      Class space:        1.00 GB reserved,     512.00 KB ( <1%) committed 
             Both:        1.01 GB reserved,       4.75 MB ( <1%) committed 

Chunk freelists:
   Non-Class:  46.00 KB
       Class:  23.00 KB
        Both:  69.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB

CodeHeap 'non-profiled nmethods': size=120000Kb used=37Kb max_used=37Kb free=119962Kb
 bounds [0x0000012f2e170000, 0x0000012f2e3e0000, 0x0000012f356a0000]
CodeHeap 'profiled nmethods': size=120000Kb used=236Kb max_used=236Kb free=119763Kb
 bounds [0x0000012f26c40000, 0x0000012f26eb0000, 0x0000012f2e170000]
CodeHeap 'non-nmethods': size=5760Kb used=1059Kb max_used=1059Kb free=4700Kb
 bounds [0x0000012f266a0000, 0x0000012f26910000, 0x0000012f26c40000]
 total_blobs=831 nmethods=171 adapters=247
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.050 Thread 0x0000012f4c032800 nmethod 62 0x0000012f26c56d10 code [0x0000012f26c56ec0, 0x0000012f26c57098]
Event: 0.050 Thread 0x0000012f4c032800   61       1       java.lang.module.ModuleDescriptor::isAutomatic (5 bytes)
Event: 0.050 Thread 0x0000012f4c032800 nmethod 61 0x0000012f2e173210 code [0x0000012f2e1733c0, 0x0000012f2e1734f8]
Event: 0.050 Thread 0x0000012f4c032800   64       1       java.lang.module.ModuleDescriptor$Exports::targets (5 bytes)
Event: 0.050 Thread 0x0000012f4c032800 nmethod 64 0x0000012f2e173590 code [0x0000012f2e173740, 0x0000012f2e173878]
Event: 0.050 Thread 0x0000012f4c032800   63       1       java.lang.module.ResolvedModule::configuration (5 bytes)
Event: 0.050 Thread 0x0000012f4c032800 nmethod 63 0x0000012f2e173910 code [0x0000012f2e173ac0, 0x0000012f2e173bf8]
Event: 0.050 Thread 0x0000012f4c032800   66       3       java.util.AbstractCollection::isEmpty (13 bytes)
Event: 0.050 Thread 0x0000012f4c031800   67       4       java.util.ImmutableCollections$SetN$SetNIterator::hasNext (13 bytes)
Event: 0.050 Thread 0x0000012f4c032800 nmethod 66 0x0000012f26c57110 code [0x0000012f26c572e0, 0x0000012f26c57590]
Event: 0.050 Thread 0x0000012f4c032800   65       3       java.util.ImmutableCollections$Set12$1::next (92 bytes)
Event: 0.050 Thread 0x0000012f4c032800 nmethod 65 0x0000012f26c57610 code [0x0000012f26c577e0, 0x0000012f26c57b30]
Event: 0.050 Thread 0x0000012f4c031800 nmethod 67 0x0000012f2e173c90 code [0x0000012f2e173e20, 0x0000012f2e173e98]
Event: 0.050 Thread 0x0000012f4c031800   68       4       java.lang.String::hashCode (49 bytes)
Event: 0.050 Thread 0x0000012f4c032800   69       3       java.util.Map::entry (10 bytes)
Event: 0.051 Thread 0x0000012f4c032800 nmethod 69 0x0000012f26c57c90 code [0x0000012f26c57ea0, 0x0000012f26c584a0]
Event: 0.051 Thread 0x0000012f4c032800   71       3       java.util.ImmutableCollections$Set12::size (13 bytes)
Event: 0.051 Thread 0x0000012f4c032800 nmethod 71 0x0000012f26c58690 code [0x0000012f26c58840, 0x0000012f26c58a38]
Event: 0.051 Thread 0x0000012f4c032800   70       3       java.util.ImmutableCollections$MapN::get (35 bytes)
Event: 0.051 Thread 0x0000012f4c032800 nmethod 70 0x0000012f26c58b10 code [0x0000012f26c58d00, 0x0000012f26c59100]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (10 events):
Event: 0.010 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
Event: 0.010 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll
Event: 0.024 Thread 0x0000012f2221c800 Thread added: 0x0000012f2221c800
Event: 0.026 Thread 0x0000012f4c00f800 Thread added: 0x0000012f4c00f800
Event: 0.026 Thread 0x0000012f4c010800 Thread added: 0x0000012f4c010800
Event: 0.038 Thread 0x0000012f4c02f800 Thread added: 0x0000012f4c02f800
Event: 0.038 Thread 0x0000012f4c030800 Thread added: 0x0000012f4c030800
Event: 0.038 Thread 0x0000012f4c031800 Thread added: 0x0000012f4c031800
Event: 0.038 Thread 0x0000012f4c032800 Thread added: 0x0000012f4c032800
Event: 0.038 Thread 0x0000012f4c033800 Thread added: 0x0000012f4c033800


Dynamic libraries:
0x00007ff6568f0000 - 0x00007ff6568fa000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.exe
0x00007ff890e70000 - 0x00007ff891084000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8902a0000 - 0x00007ff890362000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff88e8a0000 - 0x00007ff88ec43000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff88e780000 - 0x00007ff88e891000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff85adb0000 - 0x00007ff85adc7000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\VCRUNTIME140.dll
0x00007ff8660c0000 - 0x00007ff8660d9000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jli.dll
0x00007ff88f760000 - 0x00007ff88f90a000 	C:\WINDOWS\System32\USER32.dll
0x00007ff88e580000 - 0x00007ff88e5a6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff88f4e0000 - 0x00007ff88f509000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff88e3e0000 - 0x00007ff88e4f9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff88e620000 - 0x00007ff88e6ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8650c0000 - 0x00007ff86534e000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.1635_none_270f70857386168e\COMCTL32.dll
0x00007ff88f630000 - 0x00007ff88f6d7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff890c50000 - 0x00007ff890c81000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000070640000 - 0x000000007064c000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ff88f510000 - 0x00007ff88f5be000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff88f910000 - 0x00007ff88f9b4000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8906b0000 - 0x00007ff8907c7000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff87bfb0000 - 0x00007ff87c05b000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ff88ece0000 - 0x00007ff88f4d9000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff88f5d0000 - 0x00007ff88f62e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ff88dc50000 - 0x00007ff88dc5a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff84ccd0000 - 0x00007ff84cd6d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\msvcp140.dll
0x00007ff831ee0000 - 0x00007ff83299d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server\jvm.dll
0x00007ff890550000 - 0x00007ff890558000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff880e80000 - 0x00007ff880eb4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff875370000 - 0x00007ff875379000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff890220000 - 0x00007ff890291000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff88d1e0000 - 0x00007ff88d1f8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff87e3f0000 - 0x00007ff87e401000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\verify.dll
0x00007ff88bb60000 - 0x00007ff88bd8e000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff88fbd0000 - 0x00007ff88ff59000 	C:\WINDOWS\System32\combase.dll
0x00007ff890370000 - 0x00007ff890447000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff87e4e0000 - 0x00007ff87e512000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff88e500000 - 0x00007ff88e57a000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff87e2f0000 - 0x00007ff87e319000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.dll
0x00007ff872780000 - 0x00007ff872798000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
0x00007ff88afa0000 - 0x00007ff88afab000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll
0x00007ff88c180000 - 0x00007ff88ca4e000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff88c040000 - 0x00007ff88c17e000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff8905b0000 - 0x00007ff8906a1000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff88e1a0000 - 0x00007ff88e1c6000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff872760000 - 0x00007ff87277a000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\net.dll
0x00007ff887b60000 - 0x00007ff887c97000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff88d660000 - 0x00007ff88d6c9000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8723a0000 - 0x00007ff8723b4000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.1635_none_270f70857386168e;C:\Program Files (x86)\360\360Safe\safemon;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/software/IntelliJ IDEA Community Edition 2020.1/plugins/git4idea/lib/git4idea-rt.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/xmlrpc-2.0.1.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/commons-codec-1.14.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/util.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 534773760                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8524922880                                {product} {ergonomic}
   size_t MaxNewSize                               = 5114953728                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;D:\software\jdk-17.0.2\bin;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=EDY
DISPLAY=:0.0
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22621 (10.0.22621.1928)
HyperV virtualization detected

CPU:total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 32512M (7034M free)
TotalPageFile size 56216M (AvailPageFile size 614M)
current process WorkingSet (physical memory assigned to process): 53M, peak: 53M
current process commit charge ("private bytes"): 610M, peak: 610M

vm_info: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25) for windows-amd64 JRE (11.0.6+8-b765.25), built on Mar 30 2020 12:57:22 by "" with MS VC++ 14.0 (VS2015)

END.
