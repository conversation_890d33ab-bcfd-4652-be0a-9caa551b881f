#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1149648 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=40308, tid=41076
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Sat Feb  1 11:51:35 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.607966 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002261b13f6d0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=41076, stack(0x0000008ce0400000,0x0000008ce0500000) (1024K)]


Current CompileTask:
C2:    608 1082       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x0000008ce0400000,0x0000008ce0500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0x3c2f9b]
V  [jvm.dll+0x1e5d82]
V  [jvm.dll+0x24fcc4]
V  [jvm.dll+0x24f141]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002261b9e1820, length=16, elements={
0x000002267a5181a0, 0x000002261b0c2130, 0x000002261b0c2cf0, 0x000002261b141070,
0x000002261b11fed0, 0x000002261b120930, 0x000002261b121390, 0x000002261b13f6d0,
0x000002261b116f20, 0x000002261b2f0f10, 0x000002261b2f3db0, 0x000002261b9b1810,
0x000002261ba07360, 0x000002261bb38b50, 0x000002261bb49fb0, 0x000002261bb4a660
}

Java Threads: ( => current thread )
  0x000002267a5181a0 JavaThread "main"                              [_thread_blocked, id=31088, stack(0x0000008cdf600000,0x0000008cdf700000) (1024K)]
  0x000002261b0c2130 JavaThread "Reference Handler"          daemon [_thread_blocked, id=34652, stack(0x0000008cdfe00000,0x0000008cdff00000) (1024K)]
  0x000002261b0c2cf0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=7468, stack(0x0000008cdff00000,0x0000008ce0000000) (1024K)]
  0x000002261b141070 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=14432, stack(0x0000008ce0000000,0x0000008ce0100000) (1024K)]
  0x000002261b11fed0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=15348, stack(0x0000008ce0100000,0x0000008ce0200000) (1024K)]
  0x000002261b120930 JavaThread "Service Thread"             daemon [_thread_blocked, id=12916, stack(0x0000008ce0200000,0x0000008ce0300000) (1024K)]
  0x000002261b121390 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4664, stack(0x0000008ce0300000,0x0000008ce0400000) (1024K)]
=>0x000002261b13f6d0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=41076, stack(0x0000008ce0400000,0x0000008ce0500000) (1024K)]
  0x000002261b116f20 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=2380, stack(0x0000008ce0500000,0x0000008ce0600000) (1024K)]
  0x000002261b2f0f10 JavaThread "Notification Thread"        daemon [_thread_blocked, id=31264, stack(0x0000008ce0600000,0x0000008ce0700000) (1024K)]
  0x000002261b2f3db0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=43528, stack(0x0000008ce0700000,0x0000008ce0800000) (1024K)]
  0x000002261b9b1810 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=18900, stack(0x0000008ce0800000,0x0000008ce0900000) (1024K)]
  0x000002261ba07360 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_blocked, id=43368, stack(0x0000008ce0a00000,0x0000008ce0b00000) (1024K)]
  0x000002261bb38b50 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=14328, stack(0x0000008ce0b00000,0x0000008ce0c00000) (1024K)]
  0x000002261bb49fb0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=43988, stack(0x0000008ce0c00000,0x0000008ce0d00000) (1024K)]
  0x000002261bb4a660 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=33552, stack(0x0000008ce0d00000,0x0000008ce0e00000) (1024K)]
Total: 16

Other Threads:
  0x000002261b092730 VMThread "VM Thread"                           [id=19852, stack(0x0000008cdfd00000,0x0000008cdfe00000) (1024K)]
  0x000002267f5d8d70 WatcherThread "VM Periodic Task Thread"        [id=43672, stack(0x0000008cdfc00000,0x0000008cdfd00000) (1024K)]
  0x000002267f490980 WorkerThread "GC Thread#0"                     [id=27144, stack(0x0000008cdf700000,0x0000008cdf800000) (1024K)]
  0x000002267a58ee90 ConcurrentGCThread "G1 Main Marker"            [id=33604, stack(0x0000008cdf800000,0x0000008cdf900000) (1024K)]
  0x000002267a58fb40 WorkerThread "G1 Conc#0"                       [id=38516, stack(0x0000008cdf900000,0x0000008cdfa00000) (1024K)]
  0x000002267f51c2b0 ConcurrentGCThread "G1 Refine#0"               [id=3836, stack(0x0000008cdfa00000,0x0000008cdfb00000) (1024K)]
  0x000002267f51ce20 ConcurrentGCThread "G1 Service"                [id=2320, stack(0x0000008cdfb00000,0x0000008cdfc00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0      645 1082       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 18479K, committed 18816K, reserved 1114112K
  class space    used 1836K, committed 1984K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x00000006229ec2b0, 0x0000000622c00000| 48%| E|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 124|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|CS|TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000002260fff0000,0x0000022610fe0000] _byte_map_base: 0x000002260cfd2000

Marking Bits: (CMBitMap*) 0x000002267a57e7a0
 Bits: [0x0000022610fe0000, 0x0000022618ef0000)

Polling page: 0x00000226789e0000

Metaspace:

Usage:
  Non-class:     16.25 MB used.
      Class:      1.79 MB used.
       Both:     18.05 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      16.44 MB ( 26%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      18.38 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  14.86 MB
       Class:  14.10 MB
        Both:  28.95 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 266.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 294.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 468.
num_chunk_merges: 0.
num_chunk_splits: 266.
num_chunks_enlarged: 125.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=403Kb max_used=403Kb free=119596Kb
 bounds [0x0000022607ad0000, 0x0000022607d40000, 0x000002260f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=2043Kb max_used=2043Kb free=117956Kb
 bounds [0x0000022600000000, 0x0000022600270000, 0x0000022607530000]
CodeHeap 'non-nmethods': size=5760Kb used=1439Kb max_used=1468Kb free=4320Kb
 bounds [0x0000022607530000, 0x00000226077a0000, 0x0000022607ad0000]
 total_blobs=1749 nmethods=1228 adapters=426
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.554 Thread 0x000002261b116f20 nmethod 1169 0x00000226001e2310 code [0x00000226001e24c0, 0x00000226001e26d0]
Event: 0.554 Thread 0x000002261b116f20 1170   !   3       java.lang.invoke.MethodHandleNatives::linkMethodHandleConstant (50 bytes)
Event: 0.554 Thread 0x000002261b116f20 nmethod 1170 0x00000226001e2810 code [0x00000226001e29e0, 0x00000226001e2be8]
Event: 0.554 Thread 0x000002261b116f20 1171       3       java.lang.invoke.MethodHandles$Lookup::linkMethodHandleConstant (223 bytes)
Event: 0.554 Thread 0x000002261bb4a660 1172       4       java.lang.Class::getClassLoader (28 bytes)
Event: 0.554 Thread 0x000002261bb4a660 nmethod 1172 0x0000022607b2fa10 code [0x0000022607b2fb80, 0x0000022607b2fc20]
Event: 0.555 Thread 0x000002261b116f20 nmethod 1171 0x00000226001e2d90 code [0x00000226001e3180, 0x00000226001e4a28]
Event: 0.555 Thread 0x000002261b116f20 1173   !   3       jdk.internal.misc.ScopedMemoryAccess::copyMemory (27 bytes)
Event: 0.555 Thread 0x000002261b116f20 nmethod 1173 0x00000226001e5210 code [0x00000226001e5420, 0x00000226001e5a48]
Event: 0.555 Thread 0x000002261b116f20 1174   !   3       jdk.internal.misc.ScopedMemoryAccess::copyMemoryInternal (56 bytes)
Event: 0.555 Thread 0x000002261bb4a660 1175       4       jdk.internal.util.StrongReferenceKey::hashCode (8 bytes)
Event: 0.555 Thread 0x000002261b116f20 nmethod 1174 0x00000226001e5e10 code [0x00000226001e6000, 0x00000226001e6540]
Event: 0.556 Thread 0x000002261b116f20 1176       1       java.util.concurrent.atomic.AtomicInteger::get (5 bytes)
Event: 0.556 Thread 0x000002261b116f20 nmethod 1176 0x0000022607b2fd10 code [0x0000022607b2fea0, 0x0000022607b2ff68]
Event: 0.556 Thread 0x000002261b116f20 1177       3       java.nio.MappedByteBuffer::limit (6 bytes)
Event: 0.556 Thread 0x000002261b116f20 nmethod 1177 0x00000226001e6890 code [0x00000226001e6a40, 0x00000226001e6c50]
Event: 0.556 Thread 0x000002261bb49fb0 nmethod 1161 0x0000022607b30010 code [0x0000022607b301e0, 0x0000022607b309e0]
Event: 0.557 Thread 0x000002261b116f20 1178       1       sun.security.provider.DigestBase::engineGetDigestLength (5 bytes)
Event: 0.557 Thread 0x000002261b116f20 nmethod 1178 0x0000022607b30e10 code [0x0000022607b30fa0, 0x0000022607b31068]
Event: 0.558 Thread 0x000002261bb4a660 nmethod 1175 0x0000022607b31110 code [0x0000022607b31320, 0x0000022607b31780]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.013 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022607b21740 relative=0x0000000000000280
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022607b21740 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.516 Thread 0x000002261ba07360 DEOPT PACKING pc=0x0000022607b21740 sp=0x0000008ce0afe120
Event: 0.516 Thread 0x000002261ba07360 DEOPT UNPACKING pc=0x00000226075846a2 sp=0x0000008ce0afe0f0 mode 2
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022607b21740 relative=0x0000000000000280
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022607b21740 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.516 Thread 0x000002261ba07360 DEOPT PACKING pc=0x0000022607b21740 sp=0x0000008ce0afe120
Event: 0.516 Thread 0x000002261ba07360 DEOPT UNPACKING pc=0x00000226075846a2 sp=0x0000008ce0afe0f0 mode 2
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022607b21740 relative=0x0000000000000280
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022607b21740 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.516 Thread 0x000002261ba07360 DEOPT PACKING pc=0x0000022607b21740 sp=0x0000008ce0afe120
Event: 0.516 Thread 0x000002261ba07360 DEOPT UNPACKING pc=0x00000226075846a2 sp=0x0000008ce0afe0f0 mode 2
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022607b21740 relative=0x0000000000000280
Event: 0.516 Thread 0x000002261ba07360 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022607b21740 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.516 Thread 0x000002261ba07360 DEOPT PACKING pc=0x0000022607b21740 sp=0x0000008ce0afe120
Event: 0.516 Thread 0x000002261ba07360 DEOPT UNPACKING pc=0x00000226075846a2 sp=0x0000008ce0afe0f0 mode 2
Event: 0.555 Thread 0x000002261b9b1810 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022607b1dd3c relative=0x000000000000097c
Event: 0.555 Thread 0x000002261b9b1810 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022607b1dd3c method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 81 c2
Event: 0.555 Thread 0x000002261b9b1810 DEOPT PACKING pc=0x0000022607b1dd3c sp=0x0000008ce08ff210
Event: 0.555 Thread 0x000002261b9b1810 DEOPT UNPACKING pc=0x00000226075846a2 sp=0x0000008ce08ff1d0 mode 2

Classes loaded (20 events):
Event: 0.538 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 0.538 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 0.538 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 0.538 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 0.538 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp
Event: 0.538 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp done
Event: 0.538 Loading class sun/security/ssl/CipherSuite$1
Event: 0.538 Loading class sun/security/ssl/CipherSuite$1 done
Event: 0.539 Loading class java/util/concurrent/CompletionException
Event: 0.540 Loading class java/util/concurrent/CompletionException done
Event: 0.551 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet
Event: 0.552 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet done
Event: 0.552 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
Event: 0.552 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1 done
Event: 0.552 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
Event: 0.552 Loading class java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry done
Event: 0.557 Loading class sun/security/ssl/NewSessionTicket$T13NewSessionTicketMessage
Event: 0.557 Loading class sun/security/ssl/NewSessionTicket$NewSessionTicketMessage
Event: 0.557 Loading class sun/security/ssl/NewSessionTicket$NewSessionTicketMessage done
Event: 0.557 Loading class sun/security/ssl/NewSessionTicket$T13NewSessionTicketMessage done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.321 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237cedd0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x00000006237cedd0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.321 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237d5868}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000006237d5868) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.322 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237dc420}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x00000006237dc420) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.323 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237e0aa8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000006237e0aa8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.327 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062300e1b8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000062300e1b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.328 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062301d4d8}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062301d4d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.331 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062304f180}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x000000062304f180) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.331 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623055a50}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623055a50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.332 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623058e70}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623058e70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.393 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623271d38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623271d38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.415 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f2130}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233f2130) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.415 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f5a98}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233f5a98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.420 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c24258}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c24258) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.432 Thread 0x000002267a5181a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ca7868}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ca7868) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.432 Thread 0x000002261b9b1810 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623212790}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623212790) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.437 Thread 0x000002261b9b1810 Exception <a 'java/lang/NoSuchMethodError'{0x000000062324c890}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062324c890) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.438 Thread 0x000002261ba07360 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1cad0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1cad0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.442 Thread 0x000002261ba07360 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6e988}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6e988) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.512 Thread 0x000002261ba07360 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622f85fb0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000622f85fb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.539 Thread 0x000002261ba07360 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ffddc8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ffddc8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (10 events):
Event: 0.103 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.103 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.111 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.111 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.259 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.259 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.434 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.434 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.514 Executing VM operation: ICBufferFull
Event: 0.514 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.058 Thread 0x000002267a5181a0 Thread added: 0x000002261b0c2cf0
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b141070
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b11fed0
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b120930
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b121390
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b13f6d0
Event: 0.059 Thread 0x000002267a5181a0 Thread added: 0x000002261b116f20
Event: 0.083 Thread 0x000002267a5181a0 Thread added: 0x000002261b2f0f10
Event: 0.086 Thread 0x000002267a5181a0 Thread added: 0x000002261b2f3db0
Event: 0.092 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.094 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.100 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.155 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.170 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.382 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.388 Thread 0x000002267a5181a0 Thread added: 0x000002261b9b1810
Event: 0.437 Thread 0x000002261b9b1810 Thread added: 0x000002261ba07360
Event: 0.447 Thread 0x000002261b9b1810 Thread added: 0x000002261bb38b50
Event: 0.476 Thread 0x000002261b116f20 Thread added: 0x000002261bb49fb0
Event: 0.477 Thread 0x000002261b116f20 Thread added: 0x000002261bb4a660


Dynamic libraries:
0x00007ff665af0000 - 0x00007ff665afa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007fff2b5d0000 - 0x00007fff2b7e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff2aa50000 - 0x00007fff2ab14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff288a0000 - 0x00007fff28c5a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff28e00000 - 0x00007fff28f11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff23f60000 - 0x00007fff23f7b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007fff14a40000 - 0x00007fff14a58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007fff2b3e0000 - 0x00007fff2b58e000 	C:\WINDOWS\System32\USER32.dll
0x00007fff28fa0000 - 0x00007fff28fc6000 	C:\WINDOWS\System32\win32u.dll
0x00007fff29670000 - 0x00007fff29699000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff28ce0000 - 0x00007fff28dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff29230000 - 0x00007fff292ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffefb160000 - 0x00007ffefb3f2000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007fff29fb0000 - 0x00007fff2a057000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff2ab40000 - 0x00007fff2ab71000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000072030000 - 0x000000007203d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff2abd0000 - 0x00007fff2ac82000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff2a610000 - 0x00007fff2a6b7000 	C:\WINDOWS\System32\sechost.dll
0x00007fff29200000 - 0x00007fff29228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fff2a4e0000 - 0x00007fff2a5f4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffedfa30000 - 0x00007ffedfb35000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff29720000 - 0x00007fff29f98000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff296a0000 - 0x00007fff296fe000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007fff281f0000 - 0x00007fff281fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff24070000 - 0x00007fff2407c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffef9fe0000 - 0x00007ffefa06d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffea7220000 - 0x00007ffea7fd7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007fff292d0000 - 0x00007fff29341000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff28770000 - 0x00007fff287bd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff1a710000 - 0x00007fff1a744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff28750000 - 0x00007fff28763000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff27810000 - 0x00007fff27828000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff24050000 - 0x00007fff2405a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007fff26160000 - 0x00007fff26392000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff2ac90000 - 0x00007fff2b01f000 	C:\WINDOWS\System32\combase.dll
0x00007fff29350000 - 0x00007fff29427000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff12590000 - 0x00007fff125c2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff28f20000 - 0x00007fff28f9b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff14a20000 - 0x00007fff14a3f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007fff14960000 - 0x00007fff14978000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007fff26740000 - 0x00007fff27048000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff26600000 - 0x00007fff2673f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007fff2b020000 - 0x00007fff2b11a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff287d0000 - 0x00007fff287fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff14a10000 - 0x00007fff14a20000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007fff222b0000 - 0x00007fff223e6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff27c90000 - 0x00007fff27cf9000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff145b0000 - 0x00007fff145c6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007fff0ccf0000 - 0x00007fff0ccfe000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007fff28fd0000 - 0x00007fff29136000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff281b0000 - 0x00007fff281dd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff28170000 - 0x00007fff281a7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff27ef0000 - 0x00007fff27f0b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff27770000 - 0x00007fff277a5000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff27d80000 - 0x00007fff27da8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff27ee0000 - 0x00007fff27eec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff28200000 - 0x00007fff2822d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff2a600000 - 0x00007fff2a609000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe9c2d0000 - 0x00007ffe9c2d8000 	C:\WINDOWS\system32\wshunix.dll
0x00007fff0cce0000 - 0x00007fff0cce9000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 95632K (0% of 33293192K total physical memory with 2217612K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 16448K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 2010K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 20416B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 25 days 2:21 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (2165M free)
TotalPageFile size 42752M (AvailPageFile size 9M)
current process WorkingSet (physical memory assigned to process): 93M, peak: 93M
current process commit charge ("private bytes"): 635M, peak: 637M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
