#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1598624 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=28308, tid=15608
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\de678b1f6bdd3a892fc5d71c674383a0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-40ea00a212ef6251737ae544768233a4-sock

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Wed Apr  2 15:07:53 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4974) elapsed time: 1.919206 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001baefb4add0):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=15608, stack(0x0000009a7ae00000,0x0000009a7af00000) (1024K)]


Current CompileTask:
C2:1919 2760       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x0000009a7ae00000,0x0000009a7af00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0x3b678c]
V  [jvm.dll+0x1dfe13]
V  [jvm.dll+0x247b42]
V  [jvm.dll+0x246fcf]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001baf65a2d30, length=23, elements={
0x000001ba98630cc0, 0x000001ba986bdd80, 0x000001baad519750, 0x000001baef7b3f70,
0x000001baef7b4f00, 0x000001baef7bab40, 0x000001baef7bee30, 0x000001baef7cbcb0,
0x000001baef7cc400, 0x000001baef893950, 0x000001baefb5e9b0, 0x000001baefb23b50,
0x000001baefb4add0, 0x000001baf586b000, 0x000001baf5b4b430, 0x000001baf5818630,
0x000001baf57d0060, 0x000001baf5b08070, 0x000001baf5a90410, 0x000001baf5bc9960,
0x000001baf5d39dc0, 0x000001baf5cc2b40, 0x000001baf5cc31d0
}

Java Threads: ( => current thread )
  0x000001ba98630cc0 JavaThread "main"                              [_thread_blocked, id=16192, stack(0x0000009a79f00000,0x0000009a7a000000) (1024K)]
  0x000001ba986bdd80 JavaThread "Reference Handler"          daemon [_thread_blocked, id=44088, stack(0x0000009a7a300000,0x0000009a7a400000) (1024K)]
  0x000001baad519750 JavaThread "Finalizer"                  daemon [_thread_blocked, id=5220, stack(0x0000009a7a400000,0x0000009a7a500000) (1024K)]
  0x000001baef7b3f70 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=44196, stack(0x0000009a7a500000,0x0000009a7a600000) (1024K)]
  0x000001baef7b4f00 JavaThread "Attach Listener"            daemon [_thread_blocked, id=7784, stack(0x0000009a7a600000,0x0000009a7a700000) (1024K)]
  0x000001baef7bab40 JavaThread "Service Thread"             daemon [_thread_blocked, id=46092, stack(0x0000009a7a700000,0x0000009a7a800000) (1024K)]
  0x000001baef7bee30 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=36316, stack(0x0000009a7a800000,0x0000009a7a900000) (1024K)]
  0x000001baef7cbcb0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=2256, stack(0x0000009a7a900000,0x0000009a7aa00000) (1024K)]
  0x000001baef7cc400 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=20224, stack(0x0000009a7aa00000,0x0000009a7ab00000) (1024K)]
  0x000001baef893950 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8404, stack(0x0000009a7ab00000,0x0000009a7ac00000) (1024K)]
  0x000001baefb5e9b0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=4304, stack(0x0000009a7ac00000,0x0000009a7ad00000) (1024K)]
  0x000001baefb23b50 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=27660, stack(0x0000009a7ad00000,0x0000009a7ae00000) (1024K)]
=>0x000001baefb4add0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=15608, stack(0x0000009a7ae00000,0x0000009a7af00000) (1024K)]
  0x000001baf586b000 JavaThread "Active Thread: Equinox Container: 4cc938fb-17cf-4d8e-a21f-e702ccda88d1"        [_thread_blocked, id=35824, stack(0x0000009a7b500000,0x0000009a7b600000) (1024K)]
  0x000001baf5b4b430 JavaThread "Framework Event Dispatcher: Equinox Container: 4cc938fb-17cf-4d8e-a21f-e702ccda88d1" daemon [_thread_blocked, id=44332, stack(0x0000009a7b600000,0x0000009a7b700000) (1024K)]
  0x000001baf5818630 JavaThread "Start Level: Equinox Container: 4cc938fb-17cf-4d8e-a21f-e702ccda88d1" daemon [_thread_in_Java, id=12796, stack(0x0000009a7b700000,0x0000009a7b800000) (1024K)]
  0x000001baf57d0060 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=27704, stack(0x0000009a7b800000,0x0000009a7b900000) (1024K)]
  0x000001baf5b08070 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=18724, stack(0x0000009a7ba00000,0x0000009a7bb00000) (1024K)]
  0x000001baf5a90410 JavaThread "Worker-JM"                         [_thread_blocked, id=32916, stack(0x0000009a7bb00000,0x0000009a7bc00000) (1024K)]
  0x000001baf5bc9960 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=43824, stack(0x0000009a7be00000,0x0000009a7bf00000) (1024K)]
  0x000001baf5d39dc0 JavaThread "Worker-0"                          [_thread_blocked, id=15804, stack(0x0000009a7bf00000,0x0000009a7c000000) (1024K)]
  0x000001baf5cc2b40 JavaThread "Worker-1"                          [_thread_blocked, id=15376, stack(0x0000009a7c000000,0x0000009a7c100000) (1024K)]
  0x000001baf5cc31d0 JavaThread "Java indexing"              daemon [_thread_blocked, id=8376, stack(0x0000009a7c100000,0x0000009a7c200000) (1024K)]
Total: 23

Other Threads:
  0x000001baad514b10 VMThread "VM Thread"                           [id=28132, stack(0x0000009a7a200000,0x0000009a7a300000) (1024K)]
  0x000001ba9869bc50 WatcherThread "VM Periodic Task Thread"        [id=13492, stack(0x0000009a7a100000,0x0000009a7a200000) (1024K)]
  0x000001ba9864f710 WorkerThread "GC Thread#0"                     [id=5384, stack(0x0000009a7a000000,0x0000009a7a100000) (1024K)]
  0x000001baf53293f0 WorkerThread "GC Thread#1"                     [id=11068, stack(0x0000009a7af00000,0x0000009a7b000000) (1024K)]
  0x000001baf57ecc60 WorkerThread "GC Thread#2"                     [id=12772, stack(0x0000009a7b000000,0x0000009a7b100000) (1024K)]
  0x000001baf57ed000 WorkerThread "GC Thread#3"                     [id=21028, stack(0x0000009a7b100000,0x0000009a7b200000) (1024K)]
  0x000001baf539e5a0 WorkerThread "GC Thread#4"                     [id=5532, stack(0x0000009a7b200000,0x0000009a7b300000) (1024K)]
  0x000001baf539e940 WorkerThread "GC Thread#5"                     [id=19028, stack(0x0000009a7b300000,0x0000009a7b400000) (1024K)]
  0x000001baf539f900 WorkerThread "GC Thread#6"                     [id=43128, stack(0x0000009a7b400000,0x0000009a7b500000) (1024K)]
  0x000001baf57828b0 WorkerThread "GC Thread#7"                     [id=46436, stack(0x0000009a7b900000,0x0000009a7ba00000) (1024K)]
  0x000001baf5a43820 WorkerThread "GC Thread#8"                     [id=47000, stack(0x0000009a7bc00000,0x0000009a7bd00000) (1024K)]
  0x000001baf59b2390 WorkerThread "GC Thread#9"                     [id=14568, stack(0x0000009a7bd00000,0x0000009a7be00000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  1973 2920       4       java.util.Properties::loadConvert (540 bytes)
C1 CompilerThread0  1973 2999       3       sun.util.locale.LanguageTag::parseVariants (98 bytes)
C2 CompilerThread1  1973 2932       4       java.lang.invoke.MethodType::makeImpl (109 bytes)
C2 CompilerThread2  1973 2760       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001baae000000-0x000001baaeba0000-0x000001baaeba0000), size 12189696, SharedBaseAddress: 0x000001baae000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001baaf000000-0x000001baef000000, reserved size: 1073741824
Narrow klass base: 0x000001baae000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 27648K, used 3721K [0x00000000eab00000, 0x00000000ecb00000, 0x0000000100000000)
  eden space 24576K, 3% used [0x00000000eab00000,0x00000000eabdd0a0,0x00000000ec300000)
  from space 3072K, 92% used [0x00000000ec500000,0x00000000ec7c5360,0x00000000ec800000)
  to   space 3072K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecb00000)
 ParOldGen       total 68608K, used 13584K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 19% used [0x00000000c0000000,0x00000000c0d44040,0x00000000c4300000)
 Metaspace       used 22783K, committed 23424K, reserved 1114112K
  class space    used 2118K, committed 2368K, reserved 1048576K

Card table byte_map: [0x000001baaadc0000,0x000001baaafd0000] _byte_map_base: 0x000001baaa7c0000

Marking Bits: (ParMarkBitMap*) 0x00007ffc811d3260
 Begin Bits: [0x000001baab130000, 0x000001baac130000)
 End Bits:   [0x000001baac130000, 0x000001baad130000)

Polling page: 0x000001ba96d30000

Metaspace:

Usage:
  Non-class:     20.18 MB used.
      Class:      2.07 MB used.
       Both:     22.25 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      20.56 MB ( 32%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      22.88 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  10.76 MB
       Class:  13.53 MB
        Both:  24.28 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 512.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 366.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1369.
num_chunk_merges: 8.
num_chunk_splits: 904.
num_chunks_enlarged: 581.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1359Kb max_used=1359Kb free=118641Kb
 bounds [0x000001baa36b0000, 0x000001baa3920000, 0x000001baaabe0000]
CodeHeap 'profiled nmethods': size=120000Kb used=5910Kb max_used=5910Kb free=114089Kb
 bounds [0x000001ba9bbe0000, 0x000001ba9c1b0000, 0x000001baa3110000]
CodeHeap 'non-nmethods': size=5760Kb used=1320Kb max_used=1363Kb free=4440Kb
 bounds [0x000001baa3110000, 0x000001baa3380000, 0x000001baa36b0000]
 total_blobs=3577 nmethods=2975 adapters=508
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.904 Thread 0x000001baef7cc400 2861       3       java.lang.invoke.MethodHandles$Lookup::findBoundCallerLookup (30 bytes)
Event: 1.904 Thread 0x000001baef7cc400 nmethod 2861 0x000001ba9c156b10 code [0x000001ba9c156d20, 0x000001ba9c157358]
Event: 1.904 Thread 0x000001baef7cc400 2862       3       java.lang.invoke.MethodHandles$Lookup::getDirectMethodCommon (300 bytes)
Event: 1.905 Thread 0x000001baef7cc400 nmethod 2862 0x000001ba9c157590 code [0x000001ba9c157900, 0x000001ba9c158a10]
Event: 1.905 Thread 0x000001baef7cc400 2864       3       jdk.internal.org.objectweb.asm.Frame::execute (2305 bytes)
Event: 1.908 Thread 0x000001baefb23b50 nmethod 2828 0x000001baa37f3c10 code [0x000001baa37f4040, 0x000001baa37f7310]
Event: 1.908 Thread 0x000001baef7cc400 nmethod 2864 0x000001ba9c159010 code [0x000001ba9c159e40, 0x000001ba9c1602c0]
Event: 1.908 Thread 0x000001baef7cc400 2863       3       java.lang.invoke.MethodHandles$Lookup::checkMethod (87 bytes)
Event: 1.909 Thread 0x000001baef7cc400 nmethod 2863 0x000001ba9c161f90 code [0x000001ba9c1621a0, 0x000001ba9c1627f8]
Event: 1.912 Thread 0x000001baef7cc400 2865       3       java.lang.reflect.Field::<init> (50 bytes)
Event: 1.912 Thread 0x000001baef7cc400 nmethod 2865 0x000001ba9c162a10 code [0x000001ba9c162bc0, 0x000001ba9c162e18]
Event: 1.912 Thread 0x000001baef7cc400 2866       1       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::getFragmentClasspaths (5 bytes)
Event: 1.912 Thread 0x000001baef7cc400 nmethod 2866 0x000001baa37fa090 code [0x000001baa37fa220, 0x000001baa37fa2e8]
Event: 1.912 Thread 0x000001baef7cbcb0 2867       4       java.util.Properties$LineReader::readLine (584 bytes)
Event: 1.913 Thread 0x000001baefb23b50 2868       4       java.util.Properties::loadConvert (540 bytes)
Event: 1.914 Thread 0x000001baef7cc400 2870       3       jdk.internal.reflect.MethodHandleObjectFieldAccessorImpl::fieldAccessor (96 bytes)
Event: 1.914 Thread 0x000001baef7cc400 nmethod 2870 0x000001ba9c162f10 code [0x000001ba9c163220, 0x000001ba9c164138]
Event: 1.914 Thread 0x000001baef7cc400 2878       3       java.lang.invoke.MethodHandleImpl::makePairwiseConvertByEditor (617 bytes)
Event: 1.917 Thread 0x000001baef7cc400 nmethod 2878 0x000001ba9c164610 code [0x000001ba9c164e00, 0x000001ba9c169c90]
Event: 1.917 Thread 0x000001baef7cc400 2879       3       jdk.internal.reflect.Reflection::verifyMemberAccess (200 bytes)

GC Heap History (14 events):
Event: 0.554 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4337K, committed 4608K, reserved 1114112K
  class space    used 436K, committed 576K, reserved 1048576K
}
Event: 0.557 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3326K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 81% used [0x00000000ec400000,0x00000000ec73f800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4337K, committed 4608K, reserved 1114112K
  class space    used 436K, committed 576K, reserved 1048576K
}
Event: 0.953 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28926K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 81% used [0x00000000ec400000,0x00000000ec73f800,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8276K, committed 8640K, reserved 1114112K
  class space    used 823K, committed 960K, reserved 1048576K
}
Event: 0.957 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4066K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf8958,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1061K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c01096b8,0x00000000c4300000)
 Metaspace       used 8276K, committed 8640K, reserved 1114112K
  class space    used 823K, committed 960K, reserved 1048576K
}
Event: 1.256 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29666K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf8958,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 1061K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c01096b8,0x00000000c4300000)
 Metaspace       used 12995K, committed 13440K, reserved 1114112K
  class space    used 1311K, committed 1472K, reserved 1048576K
}
Event: 1.259 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4094K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7ffa70,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2902K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 4% used [0x00000000c0000000,0x00000000c02d59c0,0x00000000c4300000)
 Metaspace       used 12995K, committed 13440K, reserved 1114112K
  class space    used 1311K, committed 1472K, reserved 1048576K
}
Event: 1.489 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29694K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7ffa70,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 2902K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 4% used [0x00000000c0000000,0x00000000c02d59c0,0x00000000c4300000)
 Metaspace       used 15321K, committed 15936K, reserved 1114112K
  class space    used 1556K, committed 1792K, reserved 1048576K
}
Event: 1.493 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4082K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfc948,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 8846K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c08a3a08,0x00000000c4300000)
 Metaspace       used 15321K, committed 15936K, reserved 1114112K
  class space    used 1556K, committed 1792K, reserved 1048576K
}
Event: 1.638 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29682K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfc948,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 8846K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 12% used [0x00000000c0000000,0x00000000c08a3a08,0x00000000c4300000)
 Metaspace       used 18513K, committed 19136K, reserved 1114112K
  class space    used 1770K, committed 2048K, reserved 1048576K
}
Event: 1.641 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4088K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe3e0,0x00000000ec800000)
  to   space 7168K, 0% used [0x00000000eca80000,0x00000000eca80000,0x00000000ed180000)
 ParOldGen       total 68608K, used 9626K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0966920,0x00000000c4300000)
 Metaspace       used 18513K, committed 19136K, reserved 1114112K
  class space    used 1770K, committed 2048K, reserved 1048576K
}
Event: 1.732 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 19538K [0x00000000eab00000, 0x00000000ed180000, 0x0000000100000000)
  eden space 25088K, 61% used [0x00000000eab00000,0x00000000eba164d0,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe3e0,0x00000000ec800000)
  to   space 7168K, 0% used [0x00000000eca80000,0x00000000eca80000,0x00000000ed180000)
 ParOldGen       total 68608K, used 9626K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0966920,0x00000000c4300000)
 Metaspace       used 20845K, committed 21504K, reserved 1114112K
  class space    used 1976K, committed 2240K, reserved 1048576K
}
Event: 1.734 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 29696K, used 4467K [0x00000000eab00000, 0x00000000ecf00000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4608K, 96% used [0x00000000eca80000,0x00000000ecedcf48,0x00000000ecf00000)
  to   space 5120K, 0% used [0x00000000ec500000,0x00000000ec500000,0x00000000eca00000)
 ParOldGen       total 68608K, used 9626K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0966920,0x00000000c4300000)
 Metaspace       used 20845K, committed 21504K, reserved 1114112K
  class space    used 1976K, committed 2240K, reserved 1048576K
}
Event: 1.734 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 29696K, used 4467K [0x00000000eab00000, 0x00000000ecf00000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4608K, 96% used [0x00000000eca80000,0x00000000ecedcf48,0x00000000ecf00000)
  to   space 5120K, 0% used [0x00000000ec500000,0x00000000ec500000,0x00000000eca00000)
 ParOldGen       total 68608K, used 9626K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0966920,0x00000000c4300000)
 Metaspace       used 20845K, committed 21504K, reserved 1114112K
  class space    used 1976K, committed 2240K, reserved 1048576K
}
Event: 1.756 GC heap after
{Heap after GC invocations=7 (full 1):
 PSYoungGen      total 29696K, used 0K [0x00000000eab00000, 0x00000000ecf00000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4608K, 0% used [0x00000000eca80000,0x00000000eca80000,0x00000000ecf00000)
  to   space 5120K, 0% used [0x00000000ec500000,0x00000000ec500000,0x00000000eca00000)
 ParOldGen       total 68608K, used 13576K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 19% used [0x00000000c0000000,0x00000000c0d42040,0x00000000c4300000)
 Metaspace       used 20832K, committed 21504K, reserved 1114112K
  class space    used 1973K, committed 2240K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.012 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.033 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.079 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.084 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.091 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.104 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.176 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 0.917 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927\eclipse_11909.dll
Event: 1.436 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-68506\jna12932844512686621909.dll

Deoptimization events (20 events):
Event: 1.546 Thread 0x000001baf5818630 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001baa37cb8dc relative=0x000000000000021c
Event: 1.546 Thread 0x000001baf5818630 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001baa37cb8dc method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 70 c2
Event: 1.546 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001baa37cb8dc sp=0x0000009a7b7ef630
Event: 1.546 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3163aa2 sp=0x0000009a7b7ef5d8 mode 2
Event: 1.697 Thread 0x000001baf5818630 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001baa37dea04 relative=0x0000000000000164
Event: 1.697 Thread 0x000001baf5818630 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001baa37dea04 method=org.lombokweb.asm.ByteVector.putByteArray([BII)Lorg/lombokweb/asm/ByteVector; @ 20 c2
Event: 1.697 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001baa37dea04 sp=0x0000009a7b7eecd0
Event: 1.697 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3163aa2 sp=0x0000009a7b7eeca0 mode 2
Event: 1.697 Thread 0x000001baf5818630 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001baa37e0c58 relative=0x0000000000000398
Event: 1.697 Thread 0x000001baf5818630 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001baa37e0c58 method=org.lombokweb.asm.Label.resolve([BLorg/lombokweb/asm/ByteVector;I)Z @ 81 c2
Event: 1.697 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001baa37e0c58 sp=0x0000009a7b7eed60
Event: 1.697 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3163aa2 sp=0x0000009a7b7eec68 mode 2
Event: 1.822 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001ba9bcf82b9 sp=0x0000009a7b7f80b0
Event: 1.822 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3164242 sp=0x0000009a7b7f75b0 mode 0
Event: 1.912 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001ba9bcfbca6 sp=0x0000009a7b7f7f60
Event: 1.912 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3164242 sp=0x0000009a7b7f7458 mode 0
Event: 1.913 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001ba9bcfbca6 sp=0x0000009a7b7f7f60
Event: 1.913 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3164242 sp=0x0000009a7b7f7458 mode 0
Event: 1.913 Thread 0x000001baf5818630 DEOPT PACKING pc=0x000001ba9bcfbca6 sp=0x0000009a7b7f7f60
Event: 1.913 Thread 0x000001baf5818630 DEOPT UNPACKING pc=0x000001baa3164242 sp=0x0000009a7b7f7458 mode 0

Classes loaded (20 events):
Event: 1.555 Loading class java/io/StringReader
Event: 1.556 Loading class java/io/StringReader done
Event: 1.558 Loading class java/net/SocketTimeoutException
Event: 1.558 Loading class java/io/InterruptedIOException
Event: 1.558 Loading class java/io/InterruptedIOException done
Event: 1.558 Loading class java/net/SocketTimeoutException done
Event: 1.558 Loading class java/net/SocketException
Event: 1.558 Loading class java/net/SocketException done
Event: 1.558 Loading class java/net/UnknownHostException
Event: 1.558 Loading class java/net/UnknownHostException done
Event: 1.558 Loading class java/net/ProtocolException
Event: 1.558 Loading class java/net/ProtocolException done
Event: 1.565 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 1.565 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 1.569 Loading class java/io/FileWriter
Event: 1.569 Loading class java/io/FileWriter done
Event: 1.602 Loading class java/lang/NegativeArraySizeException
Event: 1.602 Loading class java/lang/NegativeArraySizeException done
Event: 1.794 Loading class org/w3c/dom/DOMException
Event: 1.795 Loading class org/w3c/dom/DOMException done

Classes unloaded (7 events):
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17bc00 'java/lang/invoke/LambdaForm$MH+0x000001baaf17bc00'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17b800 'java/lang/invoke/LambdaForm$MH+0x000001baaf17b800'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17b400 'java/lang/invoke/LambdaForm$MH+0x000001baaf17b400'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17b000 'java/lang/invoke/LambdaForm$MH+0x000001baaf17b000'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17ac00 'java/lang/invoke/LambdaForm$BMH+0x000001baaf17ac00'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf17a800 'java/lang/invoke/LambdaForm$DMH+0x000001baaf17a800'
Event: 1.740 Thread 0x000001baad514b10 Unloading class 0x000001baaf179800 'java/lang/invoke/LambdaForm$DMH+0x000001baaf179800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.398 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebe2c2c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000000ebe2c2c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.423 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebf86e40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000ebf86e40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.448 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec086690}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000ec086690) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.449 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec08a020}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, int)'> (0x00000000ec08a020) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.449 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec090d58}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ec090d58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.463 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec19f670}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ec19f670) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.473 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec287fb8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000ec287fb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.474 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec28f748}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000ec28f748) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.474 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec293610}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000ec293610) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.480 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec3053d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ec3053d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.506 Thread 0x000001baf5818630 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eac4c028}: Found class java.lang.Object, but interface was expected> (0x00000000eac4c028) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1.511 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eac61bd8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eac61bd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.512 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eac6d918}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eac6d918) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.512 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eac71408}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eac71408) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.512 Thread 0x000001baf5d39dc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eacc6d68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000eacc6d68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.513 Thread 0x000001baf5d39dc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaccdaa0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaccdaa0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.522 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eadb9498}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eadb9498) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.538 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eafd0bd8}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000eafd0bd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.539 Thread 0x000001baf5818630 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eafd4298}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eafd4298) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.697 Thread 0x000001baf5818630 Implicit null exception at 0x000001baa37de8f5 to 0x000001baa37de9e8

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1.009 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.010 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.130 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.130 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.256 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.259 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.441 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.441 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.485 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.485 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.489 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.493 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.499 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.499 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.638 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.641 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.732 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 1.756 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 1.908 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.908 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.040 Thread 0x000001ba98630cc0 Thread added: 0x000001baad519750
Event: 0.040 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7b3f70
Event: 0.040 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7b4f00
Event: 0.041 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7bab40
Event: 0.041 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7bee30
Event: 0.041 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7cbcb0
Event: 0.041 Thread 0x000001ba98630cc0 Thread added: 0x000001baef7cc400
Event: 0.055 Thread 0x000001ba98630cc0 Thread added: 0x000001baef893950
Event: 0.308 Thread 0x000001ba98630cc0 Thread added: 0x000001baefb5e9b0
Event: 0.366 Thread 0x000001baef7cc400 Thread added: 0x000001baefb23b50
Event: 0.368 Thread 0x000001baef7cc400 Thread added: 0x000001baefb4add0
Event: 0.779 Thread 0x000001ba98630cc0 Thread added: 0x000001baf586b000
Event: 0.911 Thread 0x000001ba98630cc0 Thread added: 0x000001baf5b4b430
Event: 0.913 Thread 0x000001ba98630cc0 Thread added: 0x000001baf5818630
Event: 0.929 Thread 0x000001baf5818630 Thread added: 0x000001baf57d0060
Event: 1.003 Thread 0x000001baf5818630 Thread added: 0x000001baf5b08070
Event: 1.148 Thread 0x000001baf5818630 Thread added: 0x000001baf5a90410
Event: 1.454 Thread 0x000001baf5818630 Thread added: 0x000001baf5bc9960
Event: 1.477 Thread 0x000001baf5818630 Thread added: 0x000001baf5d39dc0
Event: 1.513 Thread 0x000001baf5818630 Thread added: 0x000001baf5cc2b40


Dynamic libraries:
0x00007ff7c8a20000 - 0x00007ff7c8a2e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffccc0b0000 - 0x00007ffccc2c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffccad00000 - 0x00007ffccadc4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffcc9310000 - 0x00007ffcc96e1000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffcc91f0000 - 0x00007ffcc9301000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffcbcec0000 - 0x00007ffcbced8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffcca720000 - 0x00007ffcca8d1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffcc9cd0000 - 0x00007ffcc9cf6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffcca270000 - 0x00007ffcca299000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffcc9b30000 - 0x00007ffcc9c4b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffcc9950000 - 0x00007ffcc99ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffcbcea0000 - 0x00007ffcbcebe000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffc9fc90000 - 0x00007ffc9ff22000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffcc9ee0000 - 0x00007ffcc9f87000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffccb490000 - 0x00007ffccb4c1000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000055400000 - 0x000000005540d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffcca1b0000 - 0x00007ffcca261000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffcca8e0000 - 0x00007ffcca987000 	C:\WINDOWS\System32\sechost.dll
0x00007ffcc97b0000 - 0x00007ffcc97d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffcca990000 - 0x00007ffccaaa4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffcb70d0000 - 0x00007ffcb71d5000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffccb680000 - 0x00007ffccbf08000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffcc99f0000 - 0x00007ffcc9b2f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffccb070000 - 0x00007ffccb400000 	C:\WINDOWS\System32\combase.dll
0x00007ffcca150000 - 0x00007ffcca1ae000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffcc8b40000 - 0x00007ffcc8b4a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffcc5bb0000 - 0x00007ffcc5bbc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffcae8e0000 - 0x00007ffcae96d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffc80520000 - 0x00007ffc812b0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffcc9d80000 - 0x00007ffcc9df1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffcc90c0000 - 0x00007ffcc910d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffcbb640000 - 0x00007ffcbb674000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffcc90a0000 - 0x00007ffcc90b3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffcc8170000 - 0x00007ffcc8188000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffcc0dc0000 - 0x00007ffcc0dca000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffcc6be0000 - 0x00007ffcc6e12000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffcc9e00000 - 0x00007ffcc9ed7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcb9580000 - 0x00007ffcb95b2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffcc9d00000 - 0x00007ffcc9d7b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffcbd520000 - 0x00007ffcbd52f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffcbce60000 - 0x00007ffcbce7f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffcc70b0000 - 0x00007ffcc79bd000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffccb4e0000 - 0x00007ffccb5ea000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffcc9120000 - 0x00007ffcc914b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcbcbd0000 - 0x00007ffcbcbe8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffcbce50000 - 0x00007ffcbce60000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffcc3bf0000 - 0x00007ffcc3d1c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffcc85e0000 - 0x00007ffcc864a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcae700000 - 0x00007ffcae716000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffcbb570000 - 0x00007ffcbb580000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffcae270000 - 0x00007ffcae2b5000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927\eclipse_11909.dll
0x00007ffccaab0000 - 0x00007ffccac51000 	C:\WINDOWS\System32\ole32.dll
0x00007ffcc8830000 - 0x00007ffcc884b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffcc80d0000 - 0x00007ffcc8107000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffcc86d0000 - 0x00007ffcc86f8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffcc8a80000 - 0x00007ffcc8a8c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffcc8b50000 - 0x00007ffcc8b7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffccb5f0000 - 0x00007ffccb5f9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffcae1e0000 - 0x00007ffcae229000 	C:\Users\<USER>\AppData\Local\Temp\jna-68506\jna12932844512686621909.dll
0x00007ffccb4d0000 - 0x00007ffccb4d8000 	C:\WINDOWS\System32\PSAPI.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files (x86)\360\360Safe\safemon;c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927;C:\Users\<USER>\AppData\Local\Temp\jna-68506

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\de678b1f6bdd3a892fc5d71c674383a0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-40ea00a212ef6251737ae544768233a4-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts\;D:\Program Files\Python311\;C:\Python310\Scripts\;C:\Python310\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet\;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;e:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;;C:\;rogram Files\python;C:\Program Files\python\Scripts;e:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI\;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 19 days 10:55 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (6353M free)
TotalPageFile size 42752M (AvailPageFile size 15M)
current process WorkingSet (physical memory assigned to process): 153M, peak: 155M
current process commit charge ("private bytes"): 256M, peak: 259M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
