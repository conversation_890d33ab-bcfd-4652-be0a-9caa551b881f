#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=33464, tid=20172
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Sat Feb  1 14:11:35 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.291312 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001f2a099f050):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=20172, stack(0x00000058c5300000,0x00000058c5400000) (1024K)]


Current CompileTask:
C2:    291  553       4       jdk.internal.org.objectweb.asm.ByteVector::putUTF8 (144 bytes)

Stack: [0x00000058c5300000,0x00000058c5400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0x70f3a5]
V  [jvm.dll+0x1e5e4e]
V  [jvm.dll+0x24fcc4]
V  [jvm.dll+0x24f141]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f2a0b1e5e0, length=11, elements={
0x000001f280866c60, 0x000001f2a0948520, 0x000001f2a094af90, 0x000001f2a0975390,
0x000001f2a097df70, 0x000001f2a097e5e0, 0x000001f2a096a5d0, 0x000001f2a099f050,
0x000001f2a09a1710, 0x000001f2a0a33750, 0x000001f2a0a35eb0
}

Java Threads: ( => current thread )
  0x000001f280866c60 JavaThread "main"                              [_thread_in_vm, id=41384, stack(0x00000058c4500000,0x00000058c4600000) (1024K)]
  0x000001f2a0948520 JavaThread "Reference Handler"          daemon [_thread_blocked, id=34688, stack(0x00000058c4d00000,0x00000058c4e00000) (1024K)]
  0x000001f2a094af90 JavaThread "Finalizer"                  daemon [_thread_blocked, id=8568, stack(0x00000058c4e00000,0x00000058c4f00000) (1024K)]
  0x000001f2a0975390 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=43272, stack(0x00000058c4f00000,0x00000058c5000000) (1024K)]
  0x000001f2a097df70 JavaThread "Attach Listener"            daemon [_thread_blocked, id=42080, stack(0x00000058c5000000,0x00000058c5100000) (1024K)]
  0x000001f2a097e5e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=42312, stack(0x00000058c5100000,0x00000058c5200000) (1024K)]
  0x000001f2a096a5d0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=30908, stack(0x00000058c5200000,0x00000058c5300000) (1024K)]
=>0x000001f2a099f050 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=20172, stack(0x00000058c5300000,0x00000058c5400000) (1024K)]
  0x000001f2a09a1710 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=36200, stack(0x00000058c5400000,0x00000058c5500000) (1024K)]
  0x000001f2a0a33750 JavaThread "Notification Thread"        daemon [_thread_blocked, id=23620, stack(0x00000058c5500000,0x00000058c5600000) (1024K)]
  0x000001f2a0a35eb0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=42120, stack(0x00000058c5600000,0x00000058c5700000) (1024K)]
Total: 11

Other Threads:
  0x000001f2a08c1f90 VMThread "VM Thread"                           [id=7352, stack(0x00000058c4c00000,0x00000058c4d00000) (1024K)]
  0x000001f29dd195e0 WatcherThread "VM Periodic Task Thread"        [id=43928, stack(0x00000058c4b00000,0x00000058c4c00000) (1024K)]
  0x000001f29dbd0980 WorkerThread "GC Thread#0"                     [id=36516, stack(0x00000058c4600000,0x00000058c4700000) (1024K)]
  0x000001f2808dd950 ConcurrentGCThread "G1 Main Marker"            [id=7764, stack(0x00000058c4700000,0x00000058c4800000) (1024K)]
  0x000001f2808de600 WorkerThread "G1 Conc#0"                       [id=37316, stack(0x00000058c4800000,0x00000058c4900000) (1024K)]
  0x000001f29dc5f2e0 ConcurrentGCThread "G1 Refine#0"               [id=21472, stack(0x00000058c4900000,0x00000058c4a00000) (1024K)]
  0x000001f29dc5fe50 ConcurrentGCThread "G1 Service"                [id=8896, stack(0x00000058c4a00000,0x00000058c4b00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0      327  553       4       jdk.internal.org.objectweb.asm.ByteVector::putUTF8 (144 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffea7ee4748] Metaspace_lock - owner thread: 0x000001f280866c60

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 8192K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 0 survivors (0K)
 Metaspace       used 12602K, committed 12864K, reserved 1114112K
  class space    used 1127K, committed 1280K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 125|0x0000000623000000, 0x000000062307b648, 0x0000000623400000| 12%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000001f294c90000,0x000001f295c80000] _byte_map_base: 0x000001f291c72000

Marking Bits: (CMBitMap*) 0x000001f2808cd260
 Bits: [0x000001f295c80000, 0x000001f29db90000)

Polling page: 0x000001f280000000

Metaspace:

Usage:
  Non-class:     11.21 MB used.
      Class:      1.10 MB used.
       Both:     12.31 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.31 MB ( 18%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      12.56 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  4.55 MB
       Class:  14.69 MB
        Both:  19.24 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 134.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 201.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 211.
num_chunk_merges: 0.
num_chunk_splits: 122.
num_chunks_enlarged: 58.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=199Kb max_used=199Kb free=119800Kb
 bounds [0x000001f28c220000, 0x000001f28c490000, 0x000001f293750000]
CodeHeap 'profiled nmethods': size=120000Kb used=1094Kb max_used=1094Kb free=118905Kb
 bounds [0x000001f284750000, 0x000001f2849c0000, 0x000001f28bc80000]
CodeHeap 'non-nmethods': size=5760Kb used=1364Kb max_used=1368Kb free=4395Kb
 bounds [0x000001f28bc80000, 0x000001f28bef0000, 0x000001f28c220000]
 total_blobs=1165 nmethods=697 adapters=373
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.285 Thread 0x000001f2a09a1710 nmethod 551 0x000001f284823a10 code [0x000001f284823be0, 0x000001f284823e20]
Event: 0.285 Thread 0x000001f2a09a1710  552       3       sun.invoke.util.Wrapper::basicTypeChar (18 bytes)
Event: 0.285 Thread 0x000001f2a09a1710 nmethod 552 0x000001f284823f90 code [0x000001f284824140, 0x000001f284824318]
Event: 0.285 Thread 0x000001f2a09a1710  554       3       java.util.Arrays::equals (57 bytes)
Event: 0.285 Thread 0x000001f2a09a1710 nmethod 554 0x000001f284824410 code [0x000001f284824600, 0x000001f284824bb0]
Event: 0.285 Thread 0x000001f2a09a1710  557       3       java.lang.Class::getPackageName (81 bytes)
Event: 0.286 Thread 0x000001f2a09a1710 nmethod 557 0x000001f284824d90 code [0x000001f284825020, 0x000001f284825ab8]
Event: 0.286 Thread 0x000001f2a09a1710  559       3       java.lang.invoke.MemberName::checkForTypeAlias (172 bytes)
Event: 0.287 Thread 0x000001f2a09a1710 nmethod 559 0x000001f284825e90 code [0x000001f2848262e0, 0x000001f284827f80]
Event: 0.287 Thread 0x000001f2a09a1710  558       1       java.lang.invoke.LambdaForm$Name::index (5 bytes)
Event: 0.287 Thread 0x000001f2a09a1710 nmethod 558 0x000001f28c245e10 code [0x000001f28c245fa0, 0x000001f28c246068]
Event: 0.287 Thread 0x000001f2a09a1710  560       3       java.lang.Integer::numberOfLeadingZeros (79 bytes)
Event: 0.287 Thread 0x000001f2a09a1710 nmethod 560 0x000001f284828810 code [0x000001f2848289c0, 0x000001f284828c88]
Event: 0.287 Thread 0x000001f2a09a1710  561       1       java.lang.invoke.LambdaForm$BasicType::basicTypeClass (5 bytes)
Event: 0.287 Thread 0x000001f2a09a1710 nmethod 561 0x000001f28c246410 code [0x000001f28c2465a0, 0x000001f28c246670]
Event: 0.287 Thread 0x000001f2a09a1710  564       3       jdk.internal.org.objectweb.asm.Frame::push (109 bytes)
Event: 0.287 Thread 0x000001f2a09a1710 nmethod 564 0x000001f284828d10 code [0x000001f284828ee0, 0x000001f284829470]
Event: 0.287 Thread 0x000001f2a09a1710  565       3       jdk.internal.org.objectweb.asm.Type::getArgumentsAndReturnSizes (140 bytes)
Event: 0.288 Thread 0x000001f2a09a1710 nmethod 565 0x000001f284829610 code [0x000001f2848299c0, 0x000001f28482b5c0]
Event: 0.288 Thread 0x000001f2a09a1710  571       3       jdk.internal.org.objectweb.asm.Frame::execute (2305 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.014 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (6 events):
Event: 0.242 Thread 0x000001f280866c60 DEOPT PACKING pc=0x000001f2847947a3 sp=0x00000058c45fe0b0
Event: 0.242 Thread 0x000001f280866c60 DEOPT UNPACKING pc=0x000001f28bcd4e42 sp=0x00000058c45fd548 mode 0
Event: 0.257 Thread 0x000001f280866c60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f28c23e430 relative=0x0000000000000090
Event: 0.257 Thread 0x000001f280866c60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f28c23e430 method=jdk.internal.misc.Unsafe.convEndian(ZI)I @ 4 c2
Event: 0.257 Thread 0x000001f280866c60 DEOPT PACKING pc=0x000001f28c23e430 sp=0x00000058c45fe0b0
Event: 0.257 Thread 0x000001f280866c60 DEOPT UNPACKING pc=0x000001f28bcd46a2 sp=0x00000058c45fdfe0 mode 2

Classes loaded (20 events):
Event: 0.286 Loading class java/lang/invoke/MethodHandleImpl$Makers$3
Event: 0.286 Loading class java/lang/invoke/MethodHandleImpl$Makers$3 done
Event: 0.286 Loading class java/lang/ClassValue$ClassValueMap
Event: 0.286 Loading class java/lang/ClassValue$ClassValueMap done
Event: 0.287 Loading class java/lang/invoke/BoundMethodHandle$Species_LLL
Event: 0.287 Loading class java/lang/invoke/BoundMethodHandle$Species_LLL done
Event: 0.287 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccessor
Event: 0.287 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccessor done
Event: 0.287 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccessor$1
Event: 0.287 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccessor$1 done
Event: 0.288 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccess
Event: 0.288 Loading class java/lang/invoke/MethodHandleImpl$ArrayAccess done
Event: 0.289 Loading class java/lang/invoke/BoundMethodHandle$Species_LLLL
Event: 0.289 Loading class java/lang/invoke/BoundMethodHandle$Species_LLLL done
Event: 0.289 Loading class java/lang/invoke/LambdaFormEditor$1
Event: 0.290 Loading class java/lang/invoke/LambdaFormEditor$1 done
Event: 0.290 Loading class java/util/TreeMap$EntrySet
Event: 0.290 Loading class java/util/TreeMap$EntrySet done
Event: 0.290 Loading class java/util/TreeMap$EntryIterator
Event: 0.290 Loading class java/util/TreeMap$EntryIterator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (13 events):
Event: 0.187 Thread 0x000001f280866c60 Exception <a 'sun/nio/fs/WindowsException'{0x0000000623467208}> (0x0000000623467208) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 0.227 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623544990}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x0000000623544990) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.254 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062360df28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x000000062360df28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.266 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236cc1e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000006236cc1e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.267 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236cf6f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int)'> (0x00000006236cf6f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.267 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236d52b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x00000006236d52b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.268 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236d9ee0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006236d9ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.270 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236e89d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006236e89d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.270 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236ee6b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006236ee6b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.271 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236f4048}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006236f4048) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.284 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237281c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237281c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.285 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623731740}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623731740) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.288 Thread 0x000001f280866c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623746cc0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000623746cc0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (6 events):
Event: 0.099 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.099 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.108 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.108 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.236 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.236 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (15 events):
Event: 0.013 Thread 0x000001f280866c60 Thread added: 0x000001f280866c60
Event: 0.055 Thread 0x000001f280866c60 Thread added: 0x000001f2a0948520
Event: 0.055 Thread 0x000001f280866c60 Thread added: 0x000001f2a094af90
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a0975390
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a097df70
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a097e5e0
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a096a5d0
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a099f050
Event: 0.056 Thread 0x000001f280866c60 Thread added: 0x000001f2a09a1710
Event: 0.078 Thread 0x000001f280866c60 Thread added: 0x000001f2a0a33750
Event: 0.081 Thread 0x000001f280866c60 Thread added: 0x000001f2a0a35eb0
Event: 0.088 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.090 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.095 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.149 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff665af0000 - 0x00007ff665afa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007fff2b5d0000 - 0x00007fff2b7e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff2aa50000 - 0x00007fff2ab14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff288a0000 - 0x00007fff28c5a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff28e00000 - 0x00007fff28f11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff14a40000 - 0x00007fff14a58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007fff23f60000 - 0x00007fff23f7b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007fff2b3e0000 - 0x00007fff2b58e000 	C:\WINDOWS\System32\USER32.dll
0x00007fff28fa0000 - 0x00007fff28fc6000 	C:\WINDOWS\System32\win32u.dll
0x00007fff29670000 - 0x00007fff29699000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff28ce0000 - 0x00007fff28dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff29230000 - 0x00007fff292ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffefb160000 - 0x00007ffefb3f2000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007fff29fb0000 - 0x00007fff2a057000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff2ab40000 - 0x00007fff2ab71000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000072030000 - 0x000000007203d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff2abd0000 - 0x00007fff2ac82000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff2a610000 - 0x00007fff2a6b7000 	C:\WINDOWS\System32\sechost.dll
0x00007fff29200000 - 0x00007fff29228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fff2a4e0000 - 0x00007fff2a5f4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffedfa30000 - 0x00007ffedfb35000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff29720000 - 0x00007fff29f98000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff296a0000 - 0x00007fff296fe000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007fff281f0000 - 0x00007fff281fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff24070000 - 0x00007fff2407c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffef9fe0000 - 0x00007ffefa06d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffea7220000 - 0x00007ffea7fd7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007fff292d0000 - 0x00007fff29341000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff28770000 - 0x00007fff287bd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff1a710000 - 0x00007fff1a744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff28750000 - 0x00007fff28763000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff27810000 - 0x00007fff27828000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff24050000 - 0x00007fff2405a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007fff26160000 - 0x00007fff26392000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff2ac90000 - 0x00007fff2b01f000 	C:\WINDOWS\System32\combase.dll
0x00007fff29350000 - 0x00007fff29427000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff12590000 - 0x00007fff125c2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff28f20000 - 0x00007fff28f9b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff14a20000 - 0x00007fff14a3f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007fff14960000 - 0x00007fff14978000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007fff26740000 - 0x00007fff27048000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff26600000 - 0x00007fff2673f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007fff2b020000 - 0x00007fff2b11a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff287d0000 - 0x00007fff287fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff14a10000 - 0x00007fff14a20000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007fff222b0000 - 0x00007fff223e6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff27c90000 - 0x00007fff27cf9000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff145b0000 - 0x00007fff145c6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007fff27ef0000 - 0x00007fff27f0b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff27770000 - 0x00007fff277a5000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff27d80000 - 0x00007fff27da8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff27ee0000 - 0x00007fff27eec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff28200000 - 0x00007fff2822d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff2a600000 - 0x00007fff2a609000 	C:\WINDOWS\System32\NSI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 19, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 60832K (0% of 33293192K total physical memory with 2230760K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
