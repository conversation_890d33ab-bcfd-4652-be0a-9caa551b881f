# 签到成功提示语配置示例
# 此文件应该在 Nacos 配置中心中创建，配置名称建议为：signin-message-config.yaml

# 签到提示语配置
# 格式：activity_type1:message1,activity_type2:message2
signin:
  message:
    # ========== 院外活动配置 ==========
    outside:
      # 小型活动第一次签到
      small:
        first: "高端妈妈班:恭喜您首次签到小型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到小型豪华妈妈班活动！,精品妈妈班:恭喜您首次签到小型精品妈妈班活动！"
        # 小型活动第二次签到
        second: "高端妈妈班:欢迎您再次参加小型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加小型豪华妈妈班活动！,精品妈妈班:欢迎您再次参加小型精品妈妈班活动！"

      # 中型活动第一次签到
      medium:
        first: "高端妈妈班:恭喜您首次签到中型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到中型豪华妈妈班活动！,精品妈妈班:恭喜您首次签到中型精品妈妈班活动！"
        # 中型活动第二次签到
        second: "高端妈妈班:欢迎您再次参加中型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加中型豪华妈妈班活动！,精品妈妈班:欢迎您再次参加中型精品妈妈班活动！"

      # 大型活动第一次签到
      large:
        first: "高端妈妈班:恭喜您首次签到大型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到大型豪华妈妈班活动！,精品妈妈班:恭喜您首次签到大型精品妈妈班活动！"
        # 大型活动第二次签到
        second: "高端妈妈班:欢迎您再次参加大型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加大型豪华妈妈班活动！,精品妈妈班:欢迎您再次参加大型精品妈妈班活动！"

    # ========== 院内活动配置 ==========
    inside:
      # 院内活动第一次签到
      first: "高端妈妈班:恭喜您首次签到院内高端妈妈班活动！,豪华妈妈班:恭喜您首次签到院内豪华妈妈班活动！,精品妈妈班:恭喜您首次签到院内精品妈妈班活动！"
      # 院内活动第二次签到
      second: "高端妈妈班:欢迎您再次参加院内高端妈妈班活动！,豪华妈妈班:欢迎您再次参加院内豪华妈妈班活动！,精品妈妈班:欢迎您再次参加院内精品妈妈班活动！"

    # ========== CS活动配置 ==========
    cs:
      # CS活动第一次签到
      first: "CS活动:恭喜您首次签到CS活动！,专家讲座:恭喜您首次签到专家讲座活动！,健康咨询:恭喜您首次签到健康咨询活动！"
      # CS活动第二次签到
      second: "CS活动:欢迎您再次参加CS活动！,专家讲座:欢迎您再次参加专家讲座活动！,健康咨询:欢迎您再次参加健康咨询活动！"
      # CS活动第三次及以上签到
      third: "CS活动:感谢您的持续参与CS活动！,专家讲座:感谢您的持续参与专家讲座活动！,健康咨询:感谢您的持续参与健康咨询活动！"

# 配置说明：
# 1. 院外活动分为小型、中型、大型，每种都有第一次和第二次签到的不同提示语
# 2. 院内活动有第一次和第二次签到的不同提示语
# 3. CS活动有第一次、第二次、第三次及以上签到的不同提示语
# 4. 配置格式为：activity_type1:message1,activity_type2:message2
# 5. 如果找不到匹配的配置，系统会使用默认提示语："签到成功！"
# 6. 配置支持热更新，修改后无需重启服务
# 7. 系统会根据用户当月签到历史自动判断签到次数

# 使用示例：
# 1. 用户首次签到院外小型高端妈妈班活动时，返回："恭喜您首次签到小型高端妈妈班活动！"
# 2. 用户第二次签到院外小型高端妈妈班活动时，返回："欢迎您再次参加小型高端妈妈班活动！"
# 3. 用户签到过小型活动后，首次签到中型高端妈妈班活动时，返回："恭喜您首次签到中型高端妈妈班活动！"（因为中型活动的签到次数独立计算）
# 4. 用户签到过中型高端妈妈班后，首次签到中型豪华妈妈班活动时，返回："恭喜您首次签到中型豪华妈妈班活动！"（因为不同活动类型的签到次数独立计算）
# 5. 用户第三次签到CS活动时，返回："感谢您的持续参与CS活动！"

# 签到次数计算规则：
# - 院外活动：按具体课程类型分别计算签到次数
#   * 小型活动、大型活动：按课程类型计算（小型活动的签到次数独立于大型活动）
#   * 中型活动：还需要按活动类型分别计算（高端妈妈班、豪华妈妈班、精品妈妈班的签到次数相互独立）
# - 院内活动：按院内活动整体计算签到次数
# - CS活动：按CS活动整体计算签到次数
# - 根据历史记录数量+1（本次签到）来确定是第几次签到

# 配置部署说明：
# 1. 在 Nacos 配置中心创建配置文件
# 2. Data ID: signin-message-config.yaml
# 3. Group: DEFAULT_GROUP（或根据项目需要设置）
# 4. 配置格式: YAML
# 5. 配置内容: 复制上述配置内容
