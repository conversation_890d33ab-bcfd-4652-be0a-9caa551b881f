# 签到成功提示语配置示例
# 此文件应该在 Nacos 配置中心中创建，配置名称建议为：signin-message-config.yaml

# 小型活动签到提示语配置
# 格式：activity_type1:message1,activity_type2:message2
signin:
  message:
    small:
      activity: "高端妈妈班:恭喜您成功签到小型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到小型豪华妈妈班活动！,精品妈妈班:恭喜您成功签到小型精品妈妈班活动！"
    
    # 中型活动签到提示语配置
    medium:
      activity: "高端妈妈班:恭喜您成功签到中型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到中型豪华妈妈班活动！,精品妈妈班:恭喜您成功签到中型精品妈妈班活动！"
    
    # 大型活动签到提示语配置
    large:
      activity: "高端妈妈班:恭喜您成功签到大型高端妈妈班活动！,豪华妈妈班:恭喜您成功签到大型豪华妈妈班活动！,精品妈妈班:恭喜您成功签到大型精品妈妈班活动！"
    
    # CS活动签到提示语配置
    cs:
      activity: "CS活动:恭喜您成功签到CS活动！,专家讲座:恭喜您成功签到专家讲座活动！,健康咨询:恭喜您成功签到健康咨询活动！"

# 配置说明：
# 1. 每个课程类型（小型活动/中型活动/大型活动/CS）都有对应的配置项
# 2. 配置格式为：activity_type1:message1,activity_type2:message2
# 3. 如果找不到匹配的配置，系统会使用默认提示语："签到成功！"
# 4. 配置支持热更新，修改后无需重启服务
# 5. 可以根据实际业务需求添加更多的活动类型和对应的提示语

# 使用示例：
# 当用户签到一个课程类型为"小型活动"，活动类型为"高端妈妈班"的课程时，
# 系统会返回："恭喜您成功签到小型高端妈妈班活动！"

# 配置部署说明：
# 1. 在 Nacos 配置中心创建配置文件
# 2. Data ID: signin-message-config.yaml
# 3. Group: DEFAULT_GROUP（或根据项目需要设置）
# 4. 配置格式: YAML
# 5. 配置内容: 复制上述配置内容
