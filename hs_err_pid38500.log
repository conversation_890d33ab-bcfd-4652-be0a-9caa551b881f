#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 131088 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=38500, tid=24896
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5124)
Time: Fri Apr 25 11:06:49 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5124) elapsed time: 0.516781 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000207a88d2c90):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=24896, stack(0x000000a317c00000,0x000000a317d00000) (1024K)]


Current CompileTask:
C2:    516 1090       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x000000a317c00000,0x000000a317d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0xc6265]
V  [jvm.dll+0x6be6fc]
V  [jvm.dll+0x618f7e]
V  [jvm.dll+0x607c0b]
V  [jvm.dll+0x607835]
V  [jvm.dll+0x82592a]
V  [jvm.dll+0x81d01c]
V  [jvm.dll+0x82884a]
V  [jvm.dll+0x60b692]
V  [jvm.dll+0x258b52]
V  [jvm.dll+0x258f0f]
V  [jvm.dll+0x2517e5]
V  [jvm.dll+0x24f03e]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000207a87f1c70, length=16, elements={
0x00000207873296c0, 0x00000207a7f13680, 0x00000207a7f82ec0, 0x00000207a7f6dc40,
0x00000207a7f6cf20, 0x00000207a7f6d5b0, 0x00000207a7f6e2d0, 0x00000207a7f71ef0,
0x00000207a7f725a0, 0x00000207a7f6e960, 0x00000207a7f703a0, 0x00000207a7f6f680,
0x00000207a7f6fd10, 0x00000207a7f6eff0, 0x00000207a88d25e0, 0x00000207a88d2c90
}

Java Threads: ( => current thread )
  0x00000207873296c0 JavaThread "main"                              [_thread_blocked, id=41612, stack(0x000000a316500000,0x000000a316600000) (1024K)]
  0x00000207a7f13680 JavaThread "Reference Handler"          daemon [_thread_blocked, id=43004, stack(0x000000a316d00000,0x000000a316e00000) (1024K)]
  0x00000207a7f82ec0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=18916, stack(0x000000a316e00000,0x000000a316f00000) (1024K)]
  0x00000207a7f6dc40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16056, stack(0x000000a316f00000,0x000000a317000000) (1024K)]
  0x00000207a7f6cf20 JavaThread "Attach Listener"            daemon [_thread_blocked, id=39044, stack(0x000000a317000000,0x000000a317100000) (1024K)]
  0x00000207a7f6d5b0 JavaThread "Service Thread"             daemon [_thread_blocked, id=40192, stack(0x000000a317100000,0x000000a317200000) (1024K)]
  0x00000207a7f6e2d0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=14696, stack(0x000000a317200000,0x000000a317300000) (1024K)]
  0x00000207a7f71ef0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=34688, stack(0x000000a317300000,0x000000a317400000) (1024K)]
  0x00000207a7f725a0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=9212, stack(0x000000a317400000,0x000000a317500000) (1024K)]
  0x00000207a7f6e960 JavaThread "Notification Thread"        daemon [_thread_blocked, id=19652, stack(0x000000a317500000,0x000000a317600000) (1024K)]
  0x00000207a7f703a0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=41424, stack(0x000000a317600000,0x000000a317700000) (1024K)]
  0x00000207a7f6f680 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=5180, stack(0x000000a317700000,0x000000a317800000) (1024K)]
  0x00000207a7f6fd10 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=532, stack(0x000000a317900000,0x000000a317a00000) (1024K)]
  0x00000207a7f6eff0 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=29532, stack(0x000000a317a00000,0x000000a317b00000) (1024K)]
  0x00000207a88d25e0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=29384, stack(0x000000a317b00000,0x000000a317c00000) (1024K)]
=>0x00000207a88d2c90 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=24896, stack(0x000000a317c00000,0x000000a317d00000) (1024K)]
Total: 16

Other Threads:
  0x00000207a7ef3b40 VMThread "VM Thread"                           [id=29080, stack(0x000000a316c00000,0x000000a316d00000) (1024K)]
  0x00000207a7ed1180 WatcherThread "VM Periodic Task Thread"        [id=37996, stack(0x000000a316b00000,0x000000a316c00000) (1024K)]
  0x00000207a51ef910 WorkerThread "GC Thread#0"                     [id=18296, stack(0x000000a316600000,0x000000a316700000) (1024K)]
  0x00000207a51f06d0 ConcurrentGCThread "G1 Main Marker"            [id=40004, stack(0x000000a316700000,0x000000a316800000) (1024K)]
  0x000002078739f880 WorkerThread "G1 Conc#0"                       [id=15176, stack(0x000000a316800000,0x000000a316900000) (1024K)]
  0x00000207a527f640 ConcurrentGCThread "G1 Refine#0"               [id=32264, stack(0x000000a316900000,0x000000a316a00000) (1024K)]
  0x00000207a5281dd0 ConcurrentGCThread "G1 Service"                [id=37760, stack(0x000000a316a00000,0x000000a316b00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread2      557 1090       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffea4424748] Metaspace_lock - owner thread: 0x00000207a7f6fd10

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 17820K, committed 18112K, reserved 1114112K
  class space    used 1749K, committed 1920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x000000062287b630, 0x0000000622c00000| 12%| E|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 124|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|CS|TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000002079c2b0000,0x000002079d2a0000] _byte_map_base: 0x0000020799292000

Marking Bits: (CMBitMap*) 0x00000207a51efe20
 Bits: [0x000002079d2a0000, 0x00000207a51b0000)

Polling page: 0x0000020785ac0000

Metaspace:

Usage:
  Non-class:     15.69 MB used.
      Class:      1.71 MB used.
       Both:     17.40 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.81 MB ( 25%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.69 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  48.00 KB
       Class:  14.17 MB
        Both:  14.22 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 228.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 283.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 419.
num_chunk_merges: 0.
num_chunk_splits: 229.
num_chunks_enlarged: 95.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=372Kb max_used=372Kb free=119627Kb
 bounds [0x00000207930f0000, 0x0000020793360000, 0x000002079a620000]
CodeHeap 'profiled nmethods': size=120000Kb used=1872Kb max_used=1872Kb free=118127Kb
 bounds [0x000002078b620000, 0x000002078b890000, 0x0000020792b50000]
CodeHeap 'non-nmethods': size=5760Kb used=1434Kb max_used=1459Kb free=4325Kb
 bounds [0x0000020792b50000, 0x0000020792dc0000, 0x00000207930f0000]
 total_blobs=1659 nmethods=1140 adapters=424
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.508 Thread 0x00000207a7f725a0 1131       3       java.lang.Byte::hashCode (8 bytes)
Event: 0.508 Thread 0x00000207a7f725a0 nmethod 1131 0x000002078b7f1110 code [0x000002078b7f12a0, 0x000002078b7f1408]
Event: 0.508 Thread 0x00000207a7f725a0 1132       3       java.lang.Byte::hashCode (2 bytes)
Event: 0.508 Thread 0x00000207a7f725a0 nmethod 1132 0x000002078b7f1490 code [0x000002078b7f1620, 0x000002078b7f1710]
Event: 0.509 Thread 0x00000207a7f725a0 1134       3       sun.security.util.math.intpoly.P256OrderField::carryReduce (577 bytes)
Event: 0.510 Thread 0x00000207a7f725a0 nmethod 1134 0x000002078b7f1790 code [0x000002078b7f1940, 0x000002078b7f1e98]
Event: 0.510 Thread 0x00000207a7f725a0 1135       3       sun.security.util.math.intpoly.P256OrderField::carryReduce0 (2216 bytes)
Event: 0.510 Thread 0x00000207a7f71ef0 1136       4       sun.security.util.math.intpoly.IntegerPolynomial::carryValue (17 bytes)
Event: 0.510 Thread 0x00000207a7f71ef0 nmethod 1136 0x000002079314c990 code [0x000002079314cb00, 0x000002079314cba8]
Event: 0.510 Thread 0x00000207a7f725a0 nmethod 1135 0x000002078b7f2010 code [0x000002078b7f21e0, 0x000002078b7f3450]
Event: 0.512 Thread 0x00000207a7f725a0 1137       3       java.nio.ByteBuffer::wrap (8 bytes)
Event: 0.512 Thread 0x00000207a7f725a0 nmethod 1137 0x000002078b7f3590 code [0x000002078b7f3740, 0x000002078b7f3868]
Event: 0.512 Thread 0x00000207a88d25e0 1138       4       java.util.HashMap::hash (20 bytes)
Event: 0.512 Thread 0x00000207a88d25e0 nmethod 1138 0x000002079314cc90 code [0x000002079314ce20, 0x000002079314cf28]
Event: 0.512 Thread 0x00000207a7f725a0 1139       1       jdk.internal.net.http.common.DebugLogger::on (5 bytes)
Event: 0.512 Thread 0x00000207a7f725a0 nmethod 1139 0x000002079314d010 code [0x000002079314d1a0, 0x000002079314d270]
Event: 0.513 Thread 0x00000207a7f725a0 1140       3       java.nio.ByteBuffer::array (35 bytes)
Event: 0.513 Thread 0x00000207a7f725a0 nmethod 1140 0x000002078b7f3910 code [0x000002078b7f3ae0, 0x000002078b7f3dc0]
Event: 0.513 Thread 0x00000207a7f725a0 1141       3       jdk.internal.misc.Unsafe::convEndian (16 bytes)
Event: 0.513 Thread 0x00000207a7f725a0 nmethod 1141 0x000002078b7f3f10 code [0x000002078b7f40c0, 0x000002078b7f4230]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.011 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.015 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000020793145b7c relative=0x000000000000031c
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000020793145b7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT PACKING pc=0x0000020793145b7c sp=0x000000a3179fe5b0
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT UNPACKING pc=0x0000020792ba46a2 sp=0x000000a3179fe4f0 mode 2
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000207931413c0 relative=0x0000000000000280
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000207931413c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT PACKING pc=0x00000207931413c0 sp=0x000000a3179fe520
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT UNPACKING pc=0x0000020792ba46a2 sp=0x000000a3179fe4f0 mode 2
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000207931413c0 relative=0x0000000000000280
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000207931413c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT PACKING pc=0x00000207931413c0 sp=0x000000a3179fe520
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT UNPACKING pc=0x0000020792ba46a2 sp=0x000000a3179fe4f0 mode 2
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000207931413c0 relative=0x0000000000000280
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000207931413c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT PACKING pc=0x00000207931413c0 sp=0x000000a3179fe520
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT UNPACKING pc=0x0000020792ba46a2 sp=0x000000a3179fe4f0 mode 2
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000207931413c0 relative=0x0000000000000280
Event: 0.490 Thread 0x00000207a7f6fd10 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000207931413c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT PACKING pc=0x00000207931413c0 sp=0x000000a3179fe520
Event: 0.490 Thread 0x00000207a7f6fd10 DEOPT UNPACKING pc=0x0000020792ba46a2 sp=0x000000a3179fe4f0 mode 2

Classes loaded (20 events):
Event: 0.511 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator
Event: 0.511 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator done
Event: 0.511 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator
Event: 0.511 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator done
Event: 0.511 Loading class sun/security/ssl/Finished$1
Event: 0.511 Loading class sun/security/ssl/Finished$1 done
Event: 0.511 Loading class sun/security/ssl/SSLBasicKeyDerivation
Event: 0.511 Loading class sun/security/ssl/SSLBasicKeyDerivation done
Event: 0.511 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec
Event: 0.511 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec done
Event: 0.512 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 0.512 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 0.512 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 0.512 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 0.512 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp
Event: 0.512 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp done
Event: 0.513 Loading class sun/security/ssl/CipherSuite$1
Event: 0.513 Loading class sun/security/ssl/CipherSuite$1 done
Event: 0.513 Loading class java/util/concurrent/CompletionException
Event: 0.513 Loading class java/util/concurrent/CompletionException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.282 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623775238}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623775238) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.282 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062377bdf8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x000000062377bdf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.283 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623782888}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623782888) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.283 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623786f10}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623786f10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.286 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b3cf0}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006237b3cf0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.288 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237c3010}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237c3010) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.291 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237f6948}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237f6948) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.291 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237fd218}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237fd218) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.292 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623000c90}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623000c90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.356 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623270af0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623270af0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.376 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f1b90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233f1b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.376 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f54f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233f54f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.381 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c23c18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c23c18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.390 Thread 0x00000207873296c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ca78d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ca78d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.391 Thread 0x00000207a7f6f680 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623212510}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623212510) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.396 Thread 0x00000207a7f6f680 Exception <a 'java/lang/NoSuchMethodError'{0x000000062324be90}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062324be90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.397 Thread 0x00000207a7f6fd10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1ca98}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1ca98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.402 Thread 0x00000207a7f6fd10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6e950}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6e950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.486 Thread 0x00000207a7f6fd10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622f85ec8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000622f85ec8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.513 Thread 0x00000207a7f6fd10 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ffdd58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ffdd58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.110 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.110 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.234 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.234 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.490 Executing VM operation: ICBufferFull
Event: 0.490 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f82ec0
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6dc40
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6cf20
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6d5b0
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6e2d0
Event: 0.059 Thread 0x00000207873296c0 Thread added: 0x00000207a7f71ef0
Event: 0.060 Thread 0x00000207873296c0 Thread added: 0x00000207a7f725a0
Event: 0.081 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6e960
Event: 0.084 Thread 0x00000207873296c0 Thread added: 0x00000207a7f703a0
Event: 0.091 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.093 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.099 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.152 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.315 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.348 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.353 Thread 0x00000207873296c0 Thread added: 0x00000207a7f6f680
Event: 0.396 Thread 0x00000207a7f6f680 Thread added: 0x00000207a7f6fd10
Event: 0.407 Thread 0x00000207a7f6f680 Thread added: 0x00000207a7f6eff0
Event: 0.437 Thread 0x00000207a7f725a0 Thread added: 0x00000207a88d25e0
Event: 0.437 Thread 0x00000207a7f725a0 Thread added: 0x00000207a88d2c90


Dynamic libraries:
0x00007ff6b9fa0000 - 0x00007ff6b9faa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007fff5cbf0000 - 0x00007fff5ce07000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff5ace0000 - 0x00007fff5ada4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff59e20000 - 0x00007fff5a1f3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff59d00000 - 0x00007fff59e11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff55620000 - 0x00007fff55638000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007fff56a10000 - 0x00007fff56a2b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007fff5c120000 - 0x00007fff5c2d1000 	C:\WINDOWS\System32\USER32.dll
0x00007fff5a4e0000 - 0x00007fff5a506000 	C:\WINDOWS\System32\win32u.dll
0x00007fff5c2e0000 - 0x00007fff5c309000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff5a200000 - 0x00007fff5a31b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff5a510000 - 0x00007fff5a5aa000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff44010000 - 0x00007fff442a8000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d\COMCTL32.dll
0x00007fff5b350000 - 0x00007fff5b3f7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff5c6b0000 - 0x00007fff5c6e1000 	C:\WINDOWS\System32\IMM32.DLL
0x000000005e2b0000 - 0x000000005e2bd000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff5ca90000 - 0x00007fff5cb41000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff5a890000 - 0x00007fff5a938000 	C:\WINDOWS\System32\sechost.dll
0x00007fff5a630000 - 0x00007fff5a658000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fff5b470000 - 0x00007fff5b584000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff47800000 - 0x00007fff47905000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff5b880000 - 0x00007fff5c116000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff5a3a0000 - 0x00007fff5a4df000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff5a940000 - 0x00007fff5acd2000 	C:\WINDOWS\System32\combase.dll
0x00007fff5add0000 - 0x00007fff5ae33000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007fff59640000 - 0x00007fff5964a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff56a80000 - 0x00007fff56a8c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007fff45240000 - 0x00007fff452cd000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffea3760000 - 0x00007ffea4517000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007fff5b6f0000 - 0x00007fff5b761000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff59bd0000 - 0x00007fff59c1d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff4b190000 - 0x00007fff4b1c4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff59bb0000 - 0x00007fff59bc3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff58c60000 - 0x00007fff58c78000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff56320000 - 0x00007fff5632a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007fff57690000 - 0x00007fff578c2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff5c340000 - 0x00007fff5c417000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff3e310000 - 0x00007fff3e342000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff5a320000 - 0x00007fff5a39b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff562b0000 - 0x00007fff562cf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007fff55600000 - 0x00007fff55618000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007fff57b80000 - 0x00007fff5849a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff5c6f0000 - 0x00007fff5c7fa000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff59c30000 - 0x00007fff59c5b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff56300000 - 0x00007fff56310000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007fff540e0000 - 0x00007fff5420c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff590e0000 - 0x00007fff5914a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff54830000 - 0x00007fff54846000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007fff59340000 - 0x00007fff5935b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff58bc0000 - 0x00007fff58bf7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff591d0000 - 0x00007fff591f8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff59330000 - 0x00007fff5933c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff59650000 - 0x00007fff5967d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff5c320000 - 0x00007fff5c329000 	C:\WINDOWS\System32\NSI.dll
0x00007fff537f0000 - 0x00007fff537fe000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007fff5a660000 - 0x00007fff5a7c7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff59600000 - 0x00007fff5962d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff595c0000 - 0x00007fff595f7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff0e400000 - 0x00007fff0e408000 	C:\WINDOWS\system32\wshunix.dll
0x00007fff53790000 - 0x00007fff53799000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5124_none_270e8f4f7386d69d;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\;rogram Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 90740K (0% of 33293192K total physical memory with 6538820K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
