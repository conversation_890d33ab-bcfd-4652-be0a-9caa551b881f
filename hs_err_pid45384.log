#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_UNCAUGHT_CXX_EXCEPTION (0xe06d7363) at pc=0x00007fff288ffb4c, pid=45384, tid=43612
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [KERNELBASE.dll+0x5fb4c]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Thu Feb  6 16:11:41 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.064686 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000027b604281a0):  JavaThread "main"             [_thread_in_vm, id=43612, stack(0x000000eaf8e00000,0x000000eaf8f00000) (1024K)]

Stack: [0x000000eaf8e00000,0x000000eaf8f00000],  sp=0x000000eaf8efd590,  free space=1013k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [KERNELBASE.dll+0x5fb4c]
C  [VCRUNTIME140.dll+0x6480]
C  [jimage.dll+0x32a3]
C  [jimage.dll+0x2a11]
C  [jimage.dll+0x145b]
C  [jimage.dll+0x1e87]
C  [jimage.dll+0x1f1e]
C  [jimage.dll+0x265a]
V  [jvm.dll+0x2255e5]
V  [jvm.dll+0x2243e2]
V  [jvm.dll+0x837a6c]
V  [jvm.dll+0x838b12]
V  [jvm.dll+0x8390d4]
V  [jvm.dll+0x838d68]
V  [jvm.dll+0x27530b]
V  [jvm.dll+0x3e206e]
C  0x0000027b6bc6a994

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.invoke.MethodHandles.lookup()Ljava/lang/invoke/MethodHandles$Lookup;+18 java.base
j  jdk.internal.access.SharedSecrets.ensureClassInitialized(Ljava/lang/Class;)V+0 java.base
j  jdk.internal.access.SharedSecrets.getJavaLangModuleAccess()Ljdk/internal/access/JavaLangModuleAccess;+10 java.base
j  jdk.internal.module.ModuleBootstrap.<clinit>()V+22 java.base
v  ~StubRoutines::call_stub 0x0000027b6bc5100d
j  java.lang.System.initPhase2(ZZ)I+0 java.base
v  ~StubRoutines::call_stub 0x0000027b6bc5100d

siginfo: EXCEPTION_UNCAUGHT_CXX_EXCEPTION (0xe06d7363), ExceptionInformation=0x0000000019930520 0x000000eaf8efd6f0 0x00007fff24055198 0x00007fff24050000 


Registers:
RAX=0x0000027b604281a0, RBX=0x00007fff24055198, RCX=0x0000027b604281a0, RDX=0x0000027b604281a0
RSP=0x000000eaf8efd590, RBP=0x000000eaf8efd820, RSI=0x000000eaf8efd6f0, RDI=0x0000000019930520
R8 =0x0000027b7e345280, R9 =0x0000000000000000, R10=0x000000000000000b, R11=0x000000eaf8efcf00
R12=0x000000000000a3da, R13=0x0000027b604281a0, R14=0x0000027b7f14a3f0, R15=0x0000027b7f14a3f0
RIP=0x00007fff288ffb4c, EFLAGS=0x0000000000000206


Register to memory mapping:

RAX=0x0000027b604281a0 is a thread
RBX=0x00007fff24055198 jimage.dll
RCX=0x0000027b604281a0 is a thread
RDX=0x0000027b604281a0 is a thread
RSP=0x000000eaf8efd590 is pointing into the stack for thread: 0x0000027b604281a0
RBP=0x000000eaf8efd820 is pointing into the stack for thread: 0x0000027b604281a0
RSI=0x000000eaf8efd6f0 is pointing into the stack for thread: 0x0000027b604281a0
RDI=0x0000000019930520 is an unknown value
R8 =0x0000027b7e345280 points into unknown readable memory: 0x0000027b7e347bd0 | d0 7b 34 7e 7b 02 00 00
R9 =0x0 is null
R10=0x000000000000000b is an unknown value
R11=0x000000eaf8efcf00 is pointing into the stack for thread: 0x0000027b604281a0
R12=0x000000000000a3da is an unknown value
R13=0x0000027b604281a0 is a thread
R14=0x0000027b7f14a3f0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R15=0x0000027b7f14a3f0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00

Top of Stack: (sp=0x000000eaf8efd590)
0x000000eaf8efd590:   000000eaf8efc400 00007fff24055198
0x000000eaf8efd5a0:   000000eaf8efd6f0 00007ffea75efdf8
0x000000eaf8efd5b0:   00000081e06d7363 0000000000000000
0x000000eaf8efd5c0:   00007fff288ffb4c 0000000000000004
0x000000eaf8efd5d0:   0000000019930520 000000eaf8efd6f0
0x000000eaf8efd5e0:   00007fff24055198 00007fff24050000
0x000000eaf8efd5f0:   0000027b00000000 0000000000000002
0x000000eaf8efd600:   0000000000000000 00007fff24055198
0x000000eaf8efd610:   0000027b7f14a3f0 00007fff2b646bef
0x000000eaf8efd620:   000000eaf8efd690 00007fff24057000
0x000000eaf8efd630:   000000eaf8efd6f0 0000000019930520
0x000000eaf8efd640:   00007fff24057000 00007fff24050000
0x000000eaf8efd650:   0000979f01cb60fe 00007fff288c92fb
0x000000eaf8efd660:   00007fff24055198 00007fff23f66480
0x000000eaf8efd670:   00003dd908d20b41 00007ffea7805b3a
0x000000eaf8efd680:   0000000000000008 00007fff28e20475
0x000000eaf8efd690:   00007fff24050000 0000000019930520
0x000000eaf8efd6a0:   000000eaf8efd6f0 00007fff24055198
0x000000eaf8efd6b0:   00007fff24050000 00007fff28e7a649
0x000000eaf8efd6c0:   0000027b60ca9782 00007fff240532a3
0x000000eaf8efd6d0:   0000000000000000 0000027b60ca9782
0x000000eaf8efd6e0:   000000000000a3da 0000027b60ca9782
0x000000eaf8efd6f0:   00007fff24054468 00007fff24054478
0x000000eaf8efd700:   0000000000000000 000000eaf8ef0000
0x000000eaf8efd710:   0000027b60ca9782 00007fff24052a11
0x000000eaf8efd720:   000000000000a3da 00007ffea7805a1d
0x000000eaf8efd730:   0000000000000000 0000000000000000
0x000000eaf8efd740:   0000027b60ca9782 00007fff2405145b
0x000000eaf8efd750:   000000eaf8efc400 000000eaf8efc4b0
0x000000eaf8efd760:   0000027b604281a0 00007ffea78080ed
0x000000eaf8efd770:   0000027b604281a0 000000eaf8efc4b0
0x000000eaf8efd780:   00000000cafefafa 0000000000003f85 

Instructions: (pc=0x00007fff288ffb4c)
0x00007fff288ffa4c:   ff 4c 8d 05 8c d9 2d 00 33 d2 b9 00 00 10 00 e8
0x00007fff288ffa5c:   a0 41 fe ff 4c 8b f0 48 89 44 24 58 48 85 c0 0f
0x00007fff288ffa6c:   84 0b fe ff ff 4c 8d 05 b0 d9 2d 00 33 d2 8d 4a
0x00007fff288ffa7c:   02 e8 7e 41 fe ff 48 8b f8 48 89 44 24 50 e9 ed
0x00007fff288ffa8c:   fd ff ff 45 33 c0 49 8b d7 48 83 c9 ff e8 c2 a5
0x00007fff288ffa9c:   01 00 e9 08 ff ff ff 48 8b 0d c6 c4 30 00 e8 f1
0x00007fff288ffaac:   c3 fd ff 48 8b cf e8 89 98 fc ff e9 f8 fe ff ff
0x00007fff288ffabc:   49 8b ce e8 7c 98 fc ff e9 f4 fe ff ff 48 8b ce
0x00007fff288ffacc:   e8 6f 98 fc ff e9 f0 fe ff ff cc cc 71 18 d3 7a
0x00007fff288ffadc:   1a 87 89 f9 48 81 ec d8 00 00 00 48 8b 05 02 95
0x00007fff288ffaec:   30 00 48 33 c4 48 89 84 24 c0 00 00 00 48 83 64
0x00007fff288ffafc:   24 28 00 48 8d 05 da ff ff ff 83 e2 01 89 4c 24
0x00007fff288ffb0c:   20 89 54 24 24 48 89 44 24 30 4d 85 c9 74 4f b8
0x00007fff288ffb1c:   0f 00 00 00 48 8d 4c 24 40 44 3b c0 49 8b d1 41
0x00007fff288ffb2c:   0f 46 c0 44 8b c0 44 89 44 24 38 49 c1 e0 03 e8
0x00007fff288ffb3c:   b7 e0 07 00 48 8d 4c 24 20 48 ff 15 a4 14 21 00
0x00007fff288ffb4c:   0f 1f 44 00 00 48 8b 8c 24 c0 00 00 00 48 33 cc
0x00007fff288ffb5c:   e8 9f 08 05 00 48 81 c4 d8 00 00 00 c3 cc 83 64
0x00007fff288ffb6c:   24 38 00 eb cf cc cc cc cc cc cc cc 48 8b c4 48
0x00007fff288ffb7c:   89 58 08 48 89 70 10 48 89 78 18 4c 89 68 20 55
0x00007fff288ffb8c:   41 56 41 57 48 8d 68 a1 48 81 ec e0 00 00 00 48
0x00007fff288ffb9c:   8b 05 4e 94 30 00 48 33 c4 48 89 45 3f 45 33 ff
0x00007fff288ffbac:   66 c7 45 2b 00 05 48 8d 45 c7 44 89 7d 27 48 89
0x00007fff288ffbbc:   44 24 50 48 8d 4d 27 44 89 7c 24 48 45 33 c9 44
0x00007fff288ffbcc:   89 7c 24 40 45 8d 47 12 44 89 7c 24 38 b2 01 44
0x00007fff288ffbdc:   89 7c 24 30 41 8b df 44 89 7c 24 28 41 8b ff 44
0x00007fff288ffbec:   89 7c 24 20 45 8b f7 44 89 7d 2f 66 c7 45 33 00
0x00007fff288ffbfc:   01 44 89 7d 37 66 c7 45 3b 00 10 4c 89 7d c7 4c
0x00007fff288ffc0c:   89 7d cf 4c 89 7d d7 4c 89 7d df 48 ff 15 f2 14
0x00007fff288ffc1c:   21 00 0f 1f 44 00 00 85 c0 0f 88 a7 02 00 00 48
0x00007fff288ffc2c:   8d 45 cf 41 b9 20 02 00 00 48 89 44 24 50 45 8d
0x00007fff288ffc3c:   6f 02 44 89 7c 24 48 45 8d 47 20 44 89 7c 24 40 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x000000eaf8efc400 is pointing into the stack for thread: 0x0000027b604281a0
stack at sp + 1 slots: 0x00007fff24055198 jimage.dll
stack at sp + 2 slots: 0x000000eaf8efd6f0 is pointing into the stack for thread: 0x0000027b604281a0
stack at sp + 3 slots: 0x00007ffea75efdf8 jvm.dll
stack at sp + 4 slots: 0x00000081e06d7363 is an unknown value
stack at sp + 5 slots: 0x0 is null
stack at sp + 6 slots: 0x00007fff288ffb4c KERNELBASE.dll
stack at sp + 7 slots: 0x0000000000000004 is an unknown value

new  187 new  [0x0000027b6bc6a800, 0x0000027b6bc6aa08]  520 bytes
[MachCode]
  0x0000027b6bc6a800: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x0000027b6bc6a820: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x0000027b6bc6a840: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x0000027b6bc6a860: 4101 0000 | 040f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x0000027b6bc6a880: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x0000027b6bc6a8a0: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x0000027b6bc6a8c0: 0000 0800 | 0000 492b | ca89 4808 | 49ba 77dd | eca7 fe7f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x0000027b6bc6a8e0: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | c0a9 96a7 | fe7f 0000 
  0x0000027b6bc6a900: ffd0 4883 | c408 e90c | 0000 0048 | b8c0 a996 | a7fe 7f00 | 00ff d048 | 83c4 2058 | e9cb 0000 
  0x0000027b6bc6a920: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x0000027b6bc6a940: a800 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 
  0x0000027b6bc6a960: 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b820 2060 | a7fe 7f00 | 00ff d048 
  0x0000027b6bc6a980: 83c4 08e9 | 0c00 0000 | 48b8 2020 | 60a7 fe7f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 
  0x0000027b6bc6a9a0: 0000 0049 | c787 a803 | 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 
  0x0000027b6bc6a9c0: 000f 8405 | 0000 00e9 | 3465 feff | 498b 87f0 | 0300 0049 | c787 f003 | 0000 0000 | 0000 4c8b 
  0x0000027b6bc6a9e0: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 410f b65d | 0349 83c5 | 0349 ba40 | a8ef a7fe | 7f00 0041 
  0x0000027b6bc6aa00: ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000027b7f0153f0, length=9, elements={
0x0000027b604281a0, 0x0000027b7f017a60, 0x0000027b7f018780, 0x0000027b7f086650,
0x0000027b7f0870b0, 0x0000027b7f087b10, 0x0000027b7f0a88c0, 0x0000027b7f072230,
0x0000027b7f0af960
}

Java Threads: ( => current thread )
=>0x0000027b604281a0 JavaThread "main"                              [_thread_in_vm, id=43612, stack(0x000000eaf8e00000,0x000000eaf8f00000) (1024K)]
  0x0000027b7f017a60 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19492, stack(0x000000eaf9600000,0x000000eaf9700000) (1024K)]
  0x0000027b7f018780 JavaThread "Finalizer"                  daemon [_thread_blocked, id=44440, stack(0x000000eaf9700000,0x000000eaf9800000) (1024K)]
  0x0000027b7f086650 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=41740, stack(0x000000eaf9800000,0x000000eaf9900000) (1024K)]
  0x0000027b7f0870b0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=45124, stack(0x000000eaf9900000,0x000000eaf9a00000) (1024K)]
  0x0000027b7f087b10 JavaThread "Service Thread"             daemon [_thread_blocked, id=45892, stack(0x000000eaf9a00000,0x000000eaf9b00000) (1024K)]
  0x0000027b7f0a88c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=2172, stack(0x000000eaf9b00000,0x000000eaf9c00000) (1024K)]
  0x0000027b7f072230 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=9588, stack(0x000000eaf9c00000,0x000000eaf9d00000) (1024K)]
  0x0000027b7f0af960 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=33752, stack(0x000000eaf9d00000,0x000000eaf9e00000) (1024K)]
Total: 9

Other Threads:
  0x0000027b7eff3b10 VMThread "VM Thread"                           [id=30364, stack(0x000000eaf9500000,0x000000eaf9600000) (1024K)]
  0x0000027b7e419820 WatcherThread "VM Periodic Task Thread"        [id=33912, stack(0x000000eaf9400000,0x000000eaf9500000) (1024K)]
  0x0000027b7e2d0980 WorkerThread "GC Thread#0"                     [id=45880, stack(0x000000eaf8f00000,0x000000eaf9000000) (1024K)]
  0x0000027b6049ee90 ConcurrentGCThread "G1 Main Marker"            [id=42420, stack(0x000000eaf9000000,0x000000eaf9100000) (1024K)]
  0x0000027b6049fb40 WorkerThread "G1 Conc#0"                       [id=33772, stack(0x000000eaf9100000,0x000000eaf9200000) (1024K)]
  0x0000027b7e35dff0 ConcurrentGCThread "G1 Refine#0"               [id=44860, stack(0x000000eaf9200000,0x000000eaf9300000) (1024K)]
  0x0000027b7e35ea70 ConcurrentGCThread "G1 Service"                [id=39540, stack(0x000000eaf9300000,0x000000eaf9400000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 0K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 3438K, committed 3456K, reserved 1114112K
  class space    used 245K, committed 256K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 125|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 126|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 127|0x0000000623800000, 0x0000000623970b90, 0x0000000623c00000| 36%| E|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x0000027b75390000,0x0000027b76380000] _byte_map_base: 0x0000027b72372000

Marking Bits: (CMBitMap*) 0x0000027b6048e7a0
 Bits: [0x0000027b76380000, 0x0000027b7e290000)

Polling page: 0x0000027b5eb80000

Metaspace:

Usage:
  Non-class:      3.12 MB used.
      Class:    245.72 KB used.
       Both:      3.36 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       3.12 MB (  5%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     256.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       3.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 54.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 3.
num_chunk_merges: 0.
num_chunk_splits: 2.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x0000027b6c1f0000, 0x0000027b6c460000, 0x0000027b73720000]
CodeHeap 'profiled nmethods': size=120000Kb used=1Kb max_used=1Kb free=119998Kb
 bounds [0x0000027b64720000, 0x0000027b64990000, 0x0000027b6bc50000]
CodeHeap 'non-nmethods': size=5760Kb used=1141Kb max_used=1154Kb free=4618Kb
 bounds [0x0000027b6bc50000, 0x0000027b6bec0000, 0x0000027b6c1f0000]
 total_blobs=283 nmethods=2 adapters=187
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (4 events):
Event: 0.054 Thread 0x0000027b7f0af960    1       3       java.lang.String::isLatin1 (19 bytes)
Event: 0.054 Thread 0x0000027b7f0af960 nmethod 1 0x0000027b64720010 code [0x0000027b647201a0, 0x0000027b64720318]
Event: 0.054 Thread 0x0000027b7f0af960    2       3       java.lang.Object::<init> (1 bytes)
Event: 0.054 Thread 0x0000027b7f0af960 nmethod 2 0x0000027b64720390 code [0x0000027b64720520, 0x0000027b64720628]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.008 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.012 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.053 Loading class java/lang/reflect/ClassFileFormatVersion done
Event: 0.053 Loading class sun/security/action/GetPropertyAction
Event: 0.053 Loading class sun/security/action/GetPropertyAction done
Event: 0.054 Loading class jdk/internal/util/ClassFileDumper
Event: 0.054 Loading class jdk/internal/util/ClassFileDumper done
Event: 0.054 Loading class java/util/HexFormat
Event: 0.054 Loading class java/util/HexFormat done
Event: 0.054 Loading class java/lang/Character$CharacterCache
Event: 0.054 Loading class java/lang/Character$CharacterCache done
Event: 0.054 Loading class java/util/concurrent/atomic/AtomicInteger
Event: 0.054 Loading class java/util/concurrent/atomic/AtomicInteger done
Event: 0.054 Loading class jdk/internal/module/ModuleBootstrap
Event: 0.055 Loading class jdk/internal/module/ModuleBootstrap done
Event: 0.055 Loading class java/lang/module/ModuleDescriptor
Event: 0.055 Loading class java/lang/module/ModuleDescriptor done
Event: 0.055 Loading class java/lang/invoke/MethodHandles
Event: 0.055 Loading class java/lang/invoke/MethodHandles done
Event: 0.056 Loading class java/lang/invoke/MemberName$Factory
Event: 0.056 Loading class java/lang/invoke/MemberName$Factory done
Event: 0.056 Loading class java/lang/invoke/MethodHandles$Lookup

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (9 events):
Event: 0.011 Thread 0x0000027b604281a0 Thread added: 0x0000027b604281a0
Event: 0.052 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f017a60
Event: 0.052 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f018780
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f086650
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f0870b0
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f087b10
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f0a88c0
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f072230
Event: 0.053 Thread 0x0000027b604281a0 Thread added: 0x0000027b7f0af960


Dynamic libraries:
0x00007ff665af0000 - 0x00007ff665afa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007fff2b5d0000 - 0x00007fff2b7e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff2aa50000 - 0x00007fff2ab14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff288a0000 - 0x00007fff28c5a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff28e00000 - 0x00007fff28f11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff14a40000 - 0x00007fff14a58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007fff23f60000 - 0x00007fff23f7b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007fff2b3e0000 - 0x00007fff2b58e000 	C:\WINDOWS\System32\USER32.dll
0x00007fff28fa0000 - 0x00007fff28fc6000 	C:\WINDOWS\System32\win32u.dll
0x00007fff29670000 - 0x00007fff29699000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff28ce0000 - 0x00007fff28dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff29230000 - 0x00007fff292ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffefb160000 - 0x00007ffefb3f2000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007fff29fb0000 - 0x00007fff2a057000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff2ab40000 - 0x00007fff2ab71000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000072030000 - 0x000000007203d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff2abd0000 - 0x00007fff2ac82000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff2a610000 - 0x00007fff2a6b7000 	C:\WINDOWS\System32\sechost.dll
0x00007fff29200000 - 0x00007fff29228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fff2a4e0000 - 0x00007fff2a5f4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffedfa30000 - 0x00007ffedfb35000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff29720000 - 0x00007fff29f98000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff296a0000 - 0x00007fff296fe000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007fff281f0000 - 0x00007fff281fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff24070000 - 0x00007fff2407c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffef9fe0000 - 0x00007ffefa06d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffea7220000 - 0x00007ffea7fd7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007fff292d0000 - 0x00007fff29341000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff28770000 - 0x00007fff287bd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff1a710000 - 0x00007fff1a744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff28750000 - 0x00007fff28763000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff27810000 - 0x00007fff27828000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff24050000 - 0x00007fff2405a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007fff26160000 - 0x00007fff26392000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff2ac90000 - 0x00007fff2b01f000 	C:\WINDOWS\System32\combase.dll
0x00007fff29350000 - 0x00007fff29427000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff12590000 - 0x00007fff125c2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff28f20000 - 0x00007fff28f9b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff14a20000 - 0x00007fff14a3f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007fff14960000 - 0x00007fff14978000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007fff26740000 - 0x00007fff27048000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff26600000 - 0x00007fff2673f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007fff2b020000 - 0x00007fff2b11a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff287d0000 - 0x00007fff287fb000 	C:\WINDOWS\SYSTEM32\profapi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 6, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 30764K (0% of 33293192K total physical memory with 2701392K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 3438K

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 30 days 6:41 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (2638M free)
TotalPageFile size 42752M (AvailPageFile size 608M)
current process WorkingSet (physical memory assigned to process): 30M, peak: 30M
current process commit charge ("private bytes"): 596M, peak: 597M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
