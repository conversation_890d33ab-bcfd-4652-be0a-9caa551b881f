#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 65552 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=39884, tid=41748
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Fri May 30 01:22:39 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.482407 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001a1e1b70550):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=41748, stack(0x00000097d4f00000,0x00000097d5000000) (1024K)]


Current CompileTask:
C2:    482 1085       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x00000097d4f00000,0x00000097d5000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0xc6265]
V  [jvm.dll+0x6be6fc]
V  [jvm.dll+0x618f7e]
V  [jvm.dll+0x607c0b]
V  [jvm.dll+0x607835]
V  [jvm.dll+0x82592a]
V  [jvm.dll+0x81d01c]
V  [jvm.dll+0x82884a]
V  [jvm.dll+0x60b692]
V  [jvm.dll+0x258b52]
V  [jvm.dll+0x258f0f]
V  [jvm.dll+0x2517e5]
V  [jvm.dll+0x24f03e]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001a1e64032b0, length=16, elements={
0x000001a1bf5a31d0, 0x000001a1e1b478a0, 0x000001a1e1b4a310, 0x000001a1e1b83180,
0x000001a1e1b83be0, 0x000001a1e1b84640, 0x000001a1e1b6f7c0, 0x000001a1e1b70550,
0x000001a1e1b4bee0, 0x000001a1e1d17ce0, 0x000001a1e1d21740, 0x000001a1e61084d0,
0x000001a1e62b63a0, 0x000001a1e648acc0, 0x000001a1e650a640, 0x000001a1e653c750
}

Java Threads: ( => current thread )
  0x000001a1bf5a31d0 JavaThread "main"                              [_thread_blocked, id=11956, stack(0x00000097d4100000,0x00000097d4200000) (1024K)]
  0x000001a1e1b478a0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=39720, stack(0x00000097d4900000,0x00000097d4a00000) (1024K)]
  0x000001a1e1b4a310 JavaThread "Finalizer"                  daemon [_thread_blocked, id=40868, stack(0x00000097d4a00000,0x00000097d4b00000) (1024K)]
  0x000001a1e1b83180 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=28068, stack(0x00000097d4b00000,0x00000097d4c00000) (1024K)]
  0x000001a1e1b83be0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=41712, stack(0x00000097d4c00000,0x00000097d4d00000) (1024K)]
  0x000001a1e1b84640 JavaThread "Service Thread"             daemon [_thread_blocked, id=13832, stack(0x00000097d4d00000,0x00000097d4e00000) (1024K)]
  0x000001a1e1b6f7c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=1232, stack(0x00000097d4e00000,0x00000097d4f00000) (1024K)]
=>0x000001a1e1b70550 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=41748, stack(0x00000097d4f00000,0x00000097d5000000) (1024K)]
  0x000001a1e1b4bee0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21636, stack(0x00000097d5000000,0x00000097d5100000) (1024K)]
  0x000001a1e1d17ce0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=41452, stack(0x00000097d5100000,0x00000097d5200000) (1024K)]
  0x000001a1e1d21740 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=34944, stack(0x00000097d5200000,0x00000097d5300000) (1024K)]
  0x000001a1e61084d0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=7620, stack(0x00000097d5300000,0x00000097d5400000) (1024K)]
  0x000001a1e62b63a0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=25732, stack(0x00000097d5400000,0x00000097d5500000) (1024K)]
  0x000001a1e648acc0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=30136, stack(0x00000097d5600000,0x00000097d5700000) (1024K)]
  0x000001a1e650a640 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=37728, stack(0x00000097d5700000,0x00000097d5800000) (1024K)]
  0x000001a1e653c750 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=10864, stack(0x00000097d5800000,0x00000097d5900000) (1024K)]
Total: 16

Other Threads:
  0x000001a1e1ac07a0 VMThread "VM Thread"                           [id=40100, stack(0x00000097d4800000,0x00000097d4900000) (1024K)]
  0x000001a1e10796e0 WatcherThread "VM Periodic Task Thread"        [id=1292, stack(0x00000097d4700000,0x00000097d4800000) (1024K)]
  0x000001a1c19b2fd0 WorkerThread "GC Thread#0"                     [id=6896, stack(0x00000097d4200000,0x00000097d4300000) (1024K)]
  0x000001a1c19c3f70 ConcurrentGCThread "G1 Main Marker"            [id=38080, stack(0x00000097d4300000,0x00000097d4400000) (1024K)]
  0x000001a1c19c4a70 WorkerThread "G1 Conc#0"                       [id=39424, stack(0x00000097d4400000,0x00000097d4500000) (1024K)]
  0x000001a1e0f417f0 ConcurrentGCThread "G1 Refine#0"               [id=34440, stack(0x00000097d4500000,0x00000097d4600000) (1024K)]
  0x000001a1e0f42360 ConcurrentGCThread "G1 Service"                [id=40912, stack(0x00000097d4600000,0x00000097d4700000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0      504 1085       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffad8cd4748] Metaspace_lock - owner thread: 0x000001a1e648acc0

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 18107K, committed 18368K, reserved 1114112K
  class space    used 1785K, committed 1920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622971290, 0x0000000622c00000| 36%| E|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 124|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|CS|TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000001a1d5d60000,0x000001a1d6d50000] _byte_map_base: 0x000001a1d2d42000

Marking Bits: (CMBitMap*) 0x000001a1c19b36d0
 Bits: [0x000001a1d6d50000, 0x000001a1dec60000)

Polling page: 0x000001a1bf840000

Metaspace:

Usage:
  Non-class:     15.94 MB used.
      Class:      1.74 MB used.
       Both:     17.68 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      16.06 MB ( 25%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.94 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.86 MB
       Class:  14.13 MB
        Both:  29.99 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 242.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 287.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 444.
num_chunk_merges: 0.
num_chunk_splits: 245.
num_chunks_enlarged: 107.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=386Kb max_used=386Kb free=119613Kb
 bounds [0x000001a1cd2f0000, 0x000001a1cd560000, 0x000001a1d4820000]
CodeHeap 'profiled nmethods': size=120000Kb used=1962Kb max_used=1962Kb free=118037Kb
 bounds [0x000001a1c5820000, 0x000001a1c5a90000, 0x000001a1ccd50000]
CodeHeap 'non-nmethods': size=5760Kb used=1433Kb max_used=1450Kb free=4326Kb
 bounds [0x000001a1ccd50000, 0x000001a1ccfc0000, 0x000001a1cd2f0000]
 total_blobs=1682 nmethods=1163 adapters=424
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.472 Thread 0x000001a1e653c750 1125       4       sun.security.util.math.intpoly.IntegerPolynomial::carryValue (17 bytes)
Event: 0.472 Thread 0x000001a1e653c750 nmethod 1125 0x000001a1cd34cd10 code [0x000001a1cd34ce80, 0x000001a1cd34cf28]
Event: 0.472 Thread 0x000001a1e1b4bee0 nmethod 1124 0x000001a1c59f4690 code [0x000001a1c59f4860, 0x000001a1c59f5ad0]
Event: 0.473 Thread 0x000001a1e1b4bee0 1126       3       java.nio.ByteBuffer::wrap (8 bytes)
Event: 0.473 Thread 0x000001a1e1b4bee0 nmethod 1126 0x000001a1c59f5c10 code [0x000001a1c59f5dc0, 0x000001a1c59f5ee8]
Event: 0.474 Thread 0x000001a1e1b4bee0 1127       1       jdk.internal.net.http.common.DebugLogger::on (5 bytes)
Event: 0.474 Thread 0x000001a1e1b4bee0 nmethod 1127 0x000001a1cd34d010 code [0x000001a1cd34d1a0, 0x000001a1cd34d270]
Event: 0.474 Thread 0x000001a1e1b4bee0 1128       3       java.nio.ByteBuffer::array (35 bytes)
Event: 0.474 Thread 0x000001a1e1b4bee0 nmethod 1128 0x000001a1c59f5f90 code [0x000001a1c59f6160, 0x000001a1c59f6440]
Event: 0.477 Thread 0x000001a1e61084d0 1129   !   4       java.util.zip.Inflater::finished (19 bytes)
Event: 0.477 Thread 0x000001a1e1b4bee0 1131       3       jdk.internal.misc.Unsafe::checkSize (32 bytes)
Event: 0.477 Thread 0x000001a1e1b4bee0 nmethod 1131 0x000001a1c59f6590 code [0x000001a1c59f6740, 0x000001a1c59f69b8]
Event: 0.477 Thread 0x000001a1e61084d0 nmethod 1129 0x000001a1cd34d310 code [0x000001a1cd34d4a0, 0x000001a1cd34d6a0]
Event: 0.477 Thread 0x000001a1e1b4bee0 1130   !   3       jdk.internal.loader.BuiltinClassLoader::defineClass (162 bytes)
Event: 0.478 Thread 0x000001a1e1b4bee0 nmethod 1130 0x000001a1c59f6a90 code [0x000001a1c59f6f80, 0x000001a1c59f9790]
Event: 0.478 Thread 0x000001a1e1b4bee0 1133       3       java.lang.invoke.AbstractValidatingLambdaMetafactory::isAdaptableTo (126 bytes)
Event: 0.479 Thread 0x000001a1e1b4bee0 nmethod 1133 0x000001a1c59faa90 code [0x000001a1c59fad20, 0x000001a1c59fb6c8]
Event: 0.479 Thread 0x000001a1e1b4bee0 1132       3       java.nio.ByteBuffer::hasArray (20 bytes)
Event: 0.479 Thread 0x000001a1e1b4bee0 nmethod 1132 0x000001a1c59fba90 code [0x000001a1c59fbc20, 0x000001a1c59fbdc8]
Event: 0.479 Thread 0x000001a1e1b4bee0 1134       3       java.lang.invoke.MemberName::<init> (139 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.006 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: trap_request=0xffffff66 fr.pc=0x000001a1cd3461fc relative=0x000000000000031c
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x000001a1cd3461fc method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT PACKING pc=0x000001a1cd3461fc sp=0x00000097d56fe3a0
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT UNPACKING pc=0x000001a1ccda46a2 sp=0x00000097d56fe2e0 mode 2
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001a1cd3414c0 relative=0x0000000000000280
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001a1cd3414c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT PACKING pc=0x000001a1cd3414c0 sp=0x00000097d56fe310
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT UNPACKING pc=0x000001a1ccda46a2 sp=0x00000097d56fe2e0 mode 2
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001a1cd3414c0 relative=0x0000000000000280
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001a1cd3414c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT PACKING pc=0x000001a1cd3414c0 sp=0x00000097d56fe310
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT UNPACKING pc=0x000001a1ccda46a2 sp=0x00000097d56fe2e0 mode 2
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001a1cd3414c0 relative=0x0000000000000280
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001a1cd3414c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT PACKING pc=0x000001a1cd3414c0 sp=0x00000097d56fe310
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT UNPACKING pc=0x000001a1ccda46a2 sp=0x00000097d56fe2e0 mode 2
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001a1cd3414c0 relative=0x0000000000000280
Event: 0.455 Thread 0x000001a1e648acc0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001a1cd3414c0 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT PACKING pc=0x000001a1cd3414c0 sp=0x00000097d56fe310
Event: 0.455 Thread 0x000001a1e648acc0 DEOPT UNPACKING pc=0x000001a1ccda46a2 sp=0x00000097d56fe2e0 mode 2

Classes loaded (20 events):
Event: 0.473 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator
Event: 0.473 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator done
Event: 0.473 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator
Event: 0.473 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator done
Event: 0.473 Loading class sun/security/ssl/Finished$1
Event: 0.473 Loading class sun/security/ssl/Finished$1 done
Event: 0.473 Loading class sun/security/ssl/SSLBasicKeyDerivation
Event: 0.473 Loading class sun/security/ssl/SSLBasicKeyDerivation done
Event: 0.473 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec
Event: 0.473 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec done
Event: 0.474 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 0.474 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 0.474 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 0.474 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 0.474 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp
Event: 0.474 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp done
Event: 0.474 Loading class sun/security/ssl/CipherSuite$1
Event: 0.474 Loading class sun/security/ssl/CipherSuite$1 done
Event: 0.475 Loading class java/util/concurrent/CompletionException
Event: 0.475 Loading class java/util/concurrent/CompletionException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.271 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623779030}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623779030) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.272 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062377fbf0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x000000062377fbf0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.272 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623786680}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623786680) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.272 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062378ad08}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x000000062378ad08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.276 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b7c90}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006237b7c90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.277 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237c6fb0}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237c6fb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.280 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237f9028}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237f9028) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.280 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237ff8f8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237ff8f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.280 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230031b0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230031b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.338 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006232724b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006232724b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.357 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f2740}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233f2740) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.357 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f60a8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233f60a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.361 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c248a8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c248a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.370 Thread 0x000001a1e62b63a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623212e68}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623212e68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.370 Thread 0x000001a1bf5a31d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ca7998}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ca7998) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.376 Thread 0x000001a1e62b63a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062324cba8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062324cba8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.377 Thread 0x000001a1e648acc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1cb60}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1cb60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.382 Thread 0x000001a1e648acc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6ea18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6ea18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.450 Thread 0x000001a1e648acc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622f86068}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000622f86068) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.475 Thread 0x000001a1e648acc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ffdde8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ffdde8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (10 events):
Event: 0.088 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.088 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.097 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.097 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.225 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.225 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.372 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.372 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.412 Executing VM operation: ICBufferFull
Event: 0.412 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.048 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b4a310
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b83180
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b83be0
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b84640
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b6f7c0
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b70550
Event: 0.049 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1b4bee0
Event: 0.069 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1d17ce0
Event: 0.072 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e1d21740
Event: 0.078 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.079 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.084 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.146 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.241 Thread 0x000001a1e1b4bee0 Thread added: 0x000001a1e61084d0
Event: 0.302 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.331 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.335 Thread 0x000001a1bf5a31d0 Thread added: 0x000001a1e62b63a0
Event: 0.375 Thread 0x000001a1e62b63a0 Thread added: 0x000001a1e648acc0
Event: 0.386 Thread 0x000001a1e62b63a0 Thread added: 0x000001a1e650a640
Event: 0.416 Thread 0x000001a1e1b4bee0 Thread added: 0x000001a1e653c750


Dynamic libraries:
0x00007ff70eca0000 - 0x00007ff70ecaa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffb27cf0000 - 0x00007ffb27f07000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb27080000 - 0x00007ffb27144000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb24ed0000 - 0x00007ffb252a3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb255f0000 - 0x00007ffb25701000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffadd490000 - 0x00007ffadd4ab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffadc0f0000 - 0x00007ffadc108000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffb27540000 - 0x00007ffb276f1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb24de0000 - 0x00007ffb24e06000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb27780000 - 0x00007ffb277a9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb254c0000 - 0x00007ffb255e2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb09bc0000 - 0x00007ffb09e5b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffb25420000 - 0x00007ffb254ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb27820000 - 0x00007ffb278c7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb26fb0000 - 0x00007ffb26fe1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffaed160000 - 0x00007ffaed16c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffaf5db0000 - 0x00007ffaf5e3d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffad8010000 - 0x00007ffad8dc7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffb26ee0000 - 0x00007ffb26f91000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb272b0000 - 0x00007ffb27357000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb252b0000 - 0x00007ffb252d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb27360000 - 0x00007ffb27474000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb27170000 - 0x00007ffb271e1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb24cb0000 - 0x00007ffb24cfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb17970000 - 0x00007ffb179a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb1cbc0000 - 0x00007ffb1cbca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb24c90000 - 0x00007ffb24ca3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb23e00000 - 0x00007ffb23e18000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffaea2c0000 - 0x00007ffaea2ca000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffb225e0000 - 0x00007ffb22812000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb25980000 - 0x00007ffb25d13000 	C:\WINDOWS\System32\combase.dll
0x00007ffb278d0000 - 0x00007ffb279a7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb0f2b0000 - 0x00007ffb0f2e2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb25790000 - 0x00007ffb2580b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffadc110000 - 0x00007ffadc12f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ffadc0d0000 - 0x00007ffadc0e8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffb264e0000 - 0x00007ffb26d7d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb252e0000 - 0x00007ffb2541f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb22d00000 - 0x00007ffb2361d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb25d80000 - 0x00007ffb25e8b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb277b0000 - 0x00007ffb27816000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb24d10000 - 0x00007ffb24d3b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffaea200000 - 0x00007ffaea210000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffb1f930000 - 0x00007ffb1fa5c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb24280000 - 0x00007ffb242ea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffadc0b0000 - 0x00007ffadc0c6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ffb244e0000 - 0x00007ffb244fb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb23d60000 - 0x00007ffb23d97000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb24370000 - 0x00007ffb24398000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb244d0000 - 0x00007ffb244dc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb23910000 - 0x00007ffb2393d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb27150000 - 0x00007ffb27159000 	C:\WINDOWS\System32\NSI.dll
0x00007ffaec230000 - 0x00007ffaec23e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ffb25810000 - 0x00007ffb25977000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffb246b0000 - 0x00007ffb246dd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffb24670000 - 0x00007ffb246a7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa75630000 - 0x00007ffa75638000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffaec210000 - 0x00007ffaec219000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\;rogram Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 90628K (0% of 33293192K total physical memory with 1032348K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
