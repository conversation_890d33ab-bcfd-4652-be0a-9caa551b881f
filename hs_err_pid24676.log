#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 534773760 bytes for Failed to commit area from 0x0000000603e00000 to 0x0000000623c00000 of length 534773760.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (c:/BuildAgent/work/1eae4dac1d648407/src/hotspot/os/windows/os_windows.cpp:3296), pid=24676, tid=33048
#
# JRE version:  (11.0.6+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 22621 (10.0.22621.1778)
Time: Thu Jun 29 10:34:56 2023 �й���׼ʱ�� elapsed time: 0 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001e02d79a800):  JavaThread "Unknown thread" [_thread_in_vm, id=33048, stack(0x0000003225a00000,0x0000003225b00000)]

Stack: [0x0000003225a00000,0x0000003225b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5de3ca]
V  [jvm.dll+0x710ae5]
V  [jvm.dll+0x712005]
V  [jvm.dll+0x7126b3]
V  [jvm.dll+0x23e708]
V  [jvm.dll+0x5db874]
V  [jvm.dll+0x5d0aa5]
V  [jvm.dll+0x2fb0fb]
V  [jvm.dll+0x2fb06a]
V  [jvm.dll+0x2faf42]
V  [jvm.dll+0x2ffe16]
V  [jvm.dll+0x348723]
V  [jvm.dll+0x348e26]
V  [jvm.dll+0x348823]
V  [jvm.dll+0x2d58a8]
V  [jvm.dll+0x2d6a47]
V  [jvm.dll+0x6ef917]
V  [jvm.dll+0x6f110c]
V  [jvm.dll+0x355d69]
V  [jvm.dll+0x6d4afe]
V  [jvm.dll+0x3bf3d3]
V  [jvm.dll+0x3c1921]
C  [jli.dll+0x5363]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x126ad]
C  [ntdll.dll+0x5a9f8]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e02bda67b0, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001e02d7b4800 GCTaskThread "GC Thread#0" [stack: 0x0000003225b00000,0x0000003225c00000] [id=2148]
  0x000001e02d834000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000003225c00000,0x0000003225d00000] [id=20140]
  0x000001e02d836000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000003225d00000,0x0000003225e00000] [id=13236]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8d8f46227]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001e02d796240] Heap_lock - owner thread: 0x000001e02d79a800

Heap address: 0x0000000603e00000, size: 8130 MB, Compressed Oops mode: Non-zero based: 0x0000000603e00000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (2 events):
Event: 0.014 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
Event: 0.015 Loaded shared library D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff7c2060000 - 0x00007ff7c206a000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.exe
0x00007ff93d350000 - 0x00007ff93d564000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff93c1c0000 - 0x00007ff93c282000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff93ad80000 - 0x00007ff93b123000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff93a870000 - 0x00007ff93a981000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8e41b0000 - 0x00007ff8e41c9000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jli.dll
0x00007ff9376d0000 - 0x00007ff9376e7000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\VCRUNTIME140.dll
0x00007ff93c3a0000 - 0x00007ff93c54b000 	C:\WINDOWS\System32\USER32.dll
0x00007ff93ab70000 - 0x00007ff93ab96000 	C:\WINDOWS\System32\win32u.dll
0x00007ff93c2f0000 - 0x00007ff93c319000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff93a750000 - 0x00007ff93a869000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff93aba0000 - 0x00007ff93ac3a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff90ad40000 - 0x00007ff90afce000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.1635_none_270f70857386168e\COMCTL32.dll
0x00007ff93bbc0000 - 0x00007ff93bc67000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff93b210000 - 0x00007ff93b241000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000064f00000 - 0x0000000064f0c000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ff93b5b0000 - 0x00007ff93b65e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff93b500000 - 0x00007ff93b5a4000 	C:\WINDOWS\System32\sechost.dll
0x00007ff93bea0000 - 0x00007ff93bfb7000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff924720000 - 0x00007ff9247cc000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ff93c6f0000 - 0x00007ff93ceea000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff93c290000 - 0x00007ff93c2ee000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ff93a130000 - 0x00007ff93a13a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff937630000 - 0x00007ff9376cd000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\msvcp140.dll
0x00007ff8d8c60000 - 0x00007ff8d971d000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server\jvm.dll
0x00007ff93bb00000 - 0x00007ff93bb08000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff92a120000 - 0x00007ff92a129000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff92cc60000 - 0x00007ff92cc94000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff93c320000 - 0x00007ff93c391000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff9396d0000 - 0x00007ff9396e8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8d8c40000 - 0x00007ff8d8c51000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\verify.dll
0x00007ff9380a0000 - 0x00007ff9382ce000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff93cf80000 - 0x00007ff93d309000 	C:\WINDOWS\System32\combase.dll
0x00007ff93b130000 - 0x00007ff93b207000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff91b060000 - 0x00007ff91b092000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff93ad00000 - 0x00007ff93ad7b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8d8c10000 - 0x00007ff8d8c39000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\java.dll
0x00007ff8d8bf0000 - 0x00007ff8d8c08000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\zip.dll
0x00007ff8d8be0000 - 0x00007ff8d8beb000 	D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\jimage.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.1635_none_270f70857386168e;C:\Program Files (x86)\360\360Safe\safemon;D:\software\IntelliJ IDEA Community Edition 2020.1\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/software/IntelliJ IDEA Community Edition 2020.1/plugins/git4idea/lib/git4idea-rt.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/xmlrpc-2.0.1.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/commons-codec-1.14.jar;D:/software/IntelliJ IDEA Community Edition 2020.1/lib/util.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 534773760                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8524922880                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;D:\software\jdk-17.0.2\bin;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=EDY
DISPLAY=:0.0
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22621 (10.0.22621.1778)
HyperV virtualization detected

CPU:total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 32512M (10896M free)
TotalPageFile size 45312M (AvailPageFile size 434M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 65M, peak: 575M

vm_info: OpenJDK 64-Bit Server VM (11.0.6+8-b765.25) for windows-amd64 JRE (11.0.6+8-b765.25), built on Mar 30 2020 12:57:22 by "" with MS VC++ 14.0 (VS2015)

END.
