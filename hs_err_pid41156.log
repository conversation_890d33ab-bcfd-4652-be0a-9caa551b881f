#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3828), pid=41156, tid=29796
#
# JRE version:  (21.0.4+13) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
Time: Thu May 29 22:42:47 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5262) elapsed time: 0.012516 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001bd38576440):  JavaThread "Unknown thread" [_thread_in_vm, id=29796, stack(0x0000002d8b900000,0x0000002d8ba00000) (1024K)]

Stack: [0x0000002d8b900000,0x0000002d8ba00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x6dfe65]
V  [jvm.dll+0x6d433a]
V  [jvm.dll+0x36209b]
V  [jvm.dll+0x369c46]
V  [jvm.dll+0x3bbd76]
V  [jvm.dll+0x3bc048]
V  [jvm.dll+0x33485c]
V  [jvm.dll+0x33554b]
V  [jvm.dll+0x888039]
V  [jvm.dll+0x3c8ea8]
V  [jvm.dll+0x8711d8]
V  [jvm.dll+0x45d85e]
V  [jvm.dll+0x45f541]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffad8c62108, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001bd3a824790 WorkerThread "GC Thread#0"                     [id=24160, stack(0x0000002d8ba00000,0x0000002d8bb00000) (1024K)]
  0x000001bd3a835730 ConcurrentGCThread "G1 Main Marker"            [id=36084, stack(0x0000002d8bb00000,0x0000002d8bc00000) (1024K)]
  0x000001bd3a836230 WorkerThread "G1 Conc#0"                       [id=28428, stack(0x0000002d8bc00000,0x0000002d8bd00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffad8357917]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffad8cd6a48] Heap_lock - owner thread: 0x000001bd38576440

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001bd4ebd0000,0x000001bd4fbc0000] _byte_map_base: 0x000001bd4bbb2000

Marking Bits: (CMBitMap*) 0x000001bd3a824e90
 Bits: [0x000001bd4fbc0000, 0x000001bd57ad0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff70eca0000 - 0x00007ff70ecaa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffb27cf0000 - 0x00007ffb27f07000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb27080000 - 0x00007ffb27144000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb24ed0000 - 0x00007ffb252a3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb255f0000 - 0x00007ffb25701000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffadc0f0000 - 0x00007ffadc108000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffadd490000 - 0x00007ffadd4ab000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffb27540000 - 0x00007ffb276f1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb24de0000 - 0x00007ffb24e06000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb27780000 - 0x00007ffb277a9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb254c0000 - 0x00007ffb255e2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb25420000 - 0x00007ffb254ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb09bc0000 - 0x00007ffb09e5b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffb27820000 - 0x00007ffb278c7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb26fb0000 - 0x00007ffb26fe1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffaed160000 - 0x00007ffaed16c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffaed690000 - 0x00007ffaed71d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffad8010000 - 0x00007ffad8dc7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffb26ee0000 - 0x00007ffb26f91000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb272b0000 - 0x00007ffb27357000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb252b0000 - 0x00007ffb252d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb27360000 - 0x00007ffb27474000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb27170000 - 0x00007ffb271e1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb24cb0000 - 0x00007ffb24cfd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb17970000 - 0x00007ffb179a4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb1cbc0000 - 0x00007ffb1cbca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb24c90000 - 0x00007ffb24ca3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb23e00000 - 0x00007ffb23e18000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffaea2c0000 - 0x00007ffaea2ca000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffb225e0000 - 0x00007ffb22812000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb25980000 - 0x00007ffb25d13000 	C:\WINDOWS\System32\combase.dll
0x00007ffb278d0000 - 0x00007ffb279a7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb0f2b0000 - 0x00007ffb0f2e2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb25790000 - 0x00007ffb2580b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffadc110000 - 0x00007ffadc12f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\;rogram Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12120K (0% of 33293192K total physical memory with 603756K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5262)
OS uptime: 10 days 4:43 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (589M free)
TotalPageFile size 42734M (AvailPageFile size 271M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 65M, peak: 576M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
