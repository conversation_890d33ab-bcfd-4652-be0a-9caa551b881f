#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1441456 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=10676, tid=32820
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\de678b1f6bdd3a892fc5d71c674383a0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-a8929cdab9337046cf1ca5c2f7aaaf07-sock

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Wed Apr  2 15:07:51 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4974) elapsed time: 5.239322 seconds (0d 0h 0m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000002166b4a7db0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=32820, stack(0x0000006a5a400000,0x0000006a5a500000) (1024K)]


Current CompileTask:
C2:5239 5671       4       lombok.core.AST::buildWithField (21 bytes)

Stack: [0x0000006a5a400000,0x0000006a5a500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0x3b678c]
V  [jvm.dll+0x1dfe13]
V  [jvm.dll+0x247b42]
V  [jvm.dll+0x246fcf]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002167bc36ca0, length=54, elements={
0x00000216553ec1b0, 0x000002166a22bfe0, 0x000002166a22d5e0, 0x000002166b498f90,
0x000002166b499ea0, 0x000002166b49edc0, 0x000002166b4a1390, 0x000002166b4a7db0,
0x000002166b4a9090, 0x000002166b5460f0, 0x000002166c6a76a0, 0x000002166cd05400,
0x00000216712647d0, 0x00000216712713c0, 0x00000216711ae450, 0x00000216713081c0,
0x00000216713b0610, 0x00000216715a79b0, 0x00000216713e3970, 0x0000021671499590,
0x0000021671499c20, 0x0000021671496e30, 0x00000216714981e0, 0x0000021671498870,
0x00000216714967a0, 0x0000021671498f00, 0x00000216714974c0, 0x0000021671497b50,
0x00000216736af710, 0x00000216736afda0, 0x00000216736b0430, 0x00000216736b0ac0,
0x00000216736ae360, 0x00000216736ad640, 0x00000216736adcd0, 0x00000216736ae9f0,
0x00000216736af080, 0x0000021672b4c510, 0x0000021672b4d230, 0x0000021672b4d8c0,
0x0000021672b4df50, 0x0000021672b4cba0, 0x0000021672b4e5e0, 0x0000021672b4b160,
0x0000021672b4b7f0, 0x0000021672b4be80, 0x0000021673ebf240, 0x0000021673ebbdc0,
0x0000021673ebff60, 0x0000021673ebc450, 0x0000021673ec1310, 0x000002166b68ae30,
0x00000216720308a0, 0x0000021673ec2030
}

Java Threads: ( => current thread )
  0x00000216553ec1b0 JavaThread "main"                              [_thread_blocked, id=28212, stack(0x0000006a59a00000,0x0000006a59b00000) (1024K)]
  0x000002166a22bfe0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=12120, stack(0x0000006a59e00000,0x0000006a59f00000) (1024K)]
  0x000002166a22d5e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=19236, stack(0x0000006a59f00000,0x0000006a5a000000) (1024K)]
  0x000002166b498f90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=31556, stack(0x0000006a5a000000,0x0000006a5a100000) (1024K)]
  0x000002166b499ea0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=45008, stack(0x0000006a5a100000,0x0000006a5a200000) (1024K)]
  0x000002166b49edc0 JavaThread "Service Thread"             daemon [_thread_blocked, id=9960, stack(0x0000006a5a200000,0x0000006a5a300000) (1024K)]
  0x000002166b4a1390 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=47932, stack(0x0000006a5a300000,0x0000006a5a400000) (1024K)]
=>0x000002166b4a7db0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=32820, stack(0x0000006a5a400000,0x0000006a5a500000) (1024K)]
  0x000002166b4a9090 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=37668, stack(0x0000006a5a500000,0x0000006a5a600000) (1024K)]
  0x000002166b5460f0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=35956, stack(0x0000006a5a600000,0x0000006a5a700000) (1024K)]
  0x000002166c6a76a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=37556, stack(0x0000006a5a800000,0x0000006a5a900000) (1024K)]
  0x000002166cd05400 JavaThread "Active Thread: Equinox Container: 4d2c98e7-0335-44b8-82ef-d9bab71c09de"        [_thread_blocked, id=47484, stack(0x0000006a5b000000,0x0000006a5b100000) (1024K)]
  0x00000216712647d0 JavaThread "Framework Event Dispatcher: Equinox Container: 4d2c98e7-0335-44b8-82ef-d9bab71c09de" daemon [_thread_blocked, id=29244, stack(0x0000006a5b100000,0x0000006a5b200000) (1024K)]
  0x00000216712713c0 JavaThread "Start Level: Equinox Container: 4d2c98e7-0335-44b8-82ef-d9bab71c09de" daemon [_thread_blocked, id=15888, stack(0x0000006a5b200000,0x0000006a5b300000) (1024K)]
  0x00000216711ae450 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=41468, stack(0x0000006a5b300000,0x0000006a5b400000) (1024K)]
  0x00000216713081c0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=14060, stack(0x0000006a5b500000,0x0000006a5b600000) (1024K)]
  0x00000216713b0610 JavaThread "Worker-JM"                         [_thread_blocked, id=47216, stack(0x0000006a5b600000,0x0000006a5b700000) (1024K)]
  0x00000216715a79b0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=30056, stack(0x0000006a5b900000,0x0000006a5ba00000) (1024K)]
  0x00000216713e3970 JavaThread "Worker-0: Refreshing workspace"        [_thread_blocked, id=34752, stack(0x0000006a5ba00000,0x0000006a5bb00000) (1024K)]
  0x0000021671499590 JavaThread "Worker-1"                          [_thread_blocked, id=8632, stack(0x0000006a5bb00000,0x0000006a5bc00000) (1024K)]
  0x0000021671499c20 JavaThread "Worker-2: Building"                [_thread_in_native, id=6792, stack(0x0000006a5bc00000,0x0000006a5bd00000) (1024K)]
  0x0000021671496e30 JavaThread "Thread-2"                   daemon [_thread_in_native, id=12204, stack(0x0000006a5bd00000,0x0000006a5be00000) (1024K)]
  0x00000216714981e0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=25036, stack(0x0000006a5be00000,0x0000006a5bf00000) (1024K)]
  0x0000021671498870 JavaThread "Thread-4"                   daemon [_thread_in_native, id=7816, stack(0x0000006a5bf00000,0x0000006a5c000000) (1024K)]
  0x00000216714967a0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=38836, stack(0x0000006a5c000000,0x0000006a5c100000) (1024K)]
  0x0000021671498f00 JavaThread "Thread-6"                   daemon [_thread_in_native, id=21116, stack(0x0000006a5c100000,0x0000006a5c200000) (1024K)]
  0x00000216714974c0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=5796, stack(0x0000006a5c200000,0x0000006a5c300000) (1024K)]
  0x0000021671497b50 JavaThread "Thread-8"                   daemon [_thread_in_native, id=23328, stack(0x0000006a5c300000,0x0000006a5c400000) (1024K)]
  0x00000216736af710 JavaThread "Thread-9"                   daemon [_thread_in_native, id=3940, stack(0x0000006a5c400000,0x0000006a5c500000) (1024K)]
  0x00000216736afda0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=28984, stack(0x0000006a5c500000,0x0000006a5c600000) (1024K)]
  0x00000216736b0430 JavaThread "Thread-11"                  daemon [_thread_in_native, id=31224, stack(0x0000006a5c600000,0x0000006a5c700000) (1024K)]
  0x00000216736b0ac0 JavaThread "Thread-12"                  daemon [_thread_in_native, id=38340, stack(0x0000006a5c700000,0x0000006a5c800000) (1024K)]
  0x00000216736ae360 JavaThread "Thread-13"                  daemon [_thread_in_native, id=17488, stack(0x0000006a5c800000,0x0000006a5c900000) (1024K)]
  0x00000216736ad640 JavaThread "Thread-14"                  daemon [_thread_in_native, id=29640, stack(0x0000006a5c900000,0x0000006a5ca00000) (1024K)]
  0x00000216736adcd0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=5348, stack(0x0000006a5ca00000,0x0000006a5cb00000) (1024K)]
  0x00000216736ae9f0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=47848, stack(0x0000006a5cb00000,0x0000006a5cc00000) (1024K)]
  0x00000216736af080 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=3292, stack(0x0000006a5cc00000,0x0000006a5cd00000) (1024K)]
  0x0000021672b4c510 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=40972, stack(0x0000006a5a900000,0x0000006a5aa00000) (1024K)]
  0x0000021672b4d230 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=33712, stack(0x0000006a5cd00000,0x0000006a5ce00000) (1024K)]
  0x0000021672b4d8c0 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=16520, stack(0x0000006a5ce00000,0x0000006a5cf00000) (1024K)]
  0x0000021672b4df50 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=12840, stack(0x0000006a5cf00000,0x0000006a5d000000) (1024K)]
  0x0000021672b4cba0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=32112, stack(0x0000006a5d100000,0x0000006a5d200000) (1024K)]
  0x0000021672b4e5e0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=20540, stack(0x0000006a5d200000,0x0000006a5d300000) (1024K)]
  0x0000021672b4b160 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=37060, stack(0x0000006a5d300000,0x0000006a5d400000) (1024K)]
  0x0000021672b4b7f0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=26988, stack(0x0000006a5d400000,0x0000006a5d500000) (1024K)]
  0x0000021672b4be80 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=15652, stack(0x0000006a5d500000,0x0000006a5d600000) (1024K)]
  0x0000021673ebf240 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=12024, stack(0x0000006a5d600000,0x0000006a5d700000) (1024K)]
  0x0000021673ebbdc0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=40796, stack(0x0000006a5d700000,0x0000006a5d800000) (1024K)]
  0x0000021673ebff60 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=19992, stack(0x0000006a5d800000,0x0000006a5d900000) (1024K)]
  0x0000021673ebc450 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=8672, stack(0x0000006a5d900000,0x0000006a5da00000) (1024K)]
  0x0000021673ec1310 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=29860, stack(0x0000006a5da00000,0x0000006a5db00000) (1024K)]
  0x000002166b68ae30 JavaThread "C2 CompilerThread1"         daemon [_thread_in_vm, id=36268, stack(0x0000006a5a700000,0x0000006a5a800000) (1024K)]
  0x00000216720308a0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=35932, stack(0x0000006a5d000000,0x0000006a5d100000) (1024K)]
  0x0000021673ec2030 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=36436, stack(0x0000006a5db00000,0x0000006a5dc00000) (1024K)]
Total: 54

Other Threads:
  0x000002165546a850 VMThread "VM Thread"                           [id=36828, stack(0x0000006a59d00000,0x0000006a59e00000) (1024K)]
  0x000002166a11ca00 WatcherThread "VM Periodic Task Thread"        [id=34312, stack(0x0000006a59c00000,0x0000006a59d00000) (1024K)]
  0x000002165540a340 WorkerThread "GC Thread#0"                     [id=14772, stack(0x0000006a59b00000,0x0000006a59c00000) (1024K)]
  0x000002166ce2d810 WorkerThread "GC Thread#1"                     [id=41936, stack(0x0000006a5aa00000,0x0000006a5ab00000) (1024K)]
  0x000002166cd15c70 WorkerThread "GC Thread#2"                     [id=12980, stack(0x0000006a5ab00000,0x0000006a5ac00000) (1024K)]
  0x000002166cd16010 WorkerThread "GC Thread#3"                     [id=36964, stack(0x0000006a5ac00000,0x0000006a5ad00000) (1024K)]
  0x000002166c8dd100 WorkerThread "GC Thread#4"                     [id=45672, stack(0x0000006a5ad00000,0x0000006a5ae00000) (1024K)]
  0x000002166c8dd8b0 WorkerThread "GC Thread#5"                     [id=20260, stack(0x0000006a5ae00000,0x0000006a5af00000) (1024K)]
  0x000002166cc937a0 WorkerThread "GC Thread#6"                     [id=28748, stack(0x0000006a5af00000,0x0000006a5b000000) (1024K)]
  0x00000216712cc060 WorkerThread "GC Thread#7"                     [id=43628, stack(0x0000006a5b400000,0x0000006a5b500000) (1024K)]
  0x00000216710613e0 WorkerThread "GC Thread#8"                     [id=12916, stack(0x0000006a5b700000,0x0000006a5b800000) (1024K)]
  0x0000021671061780 WorkerThread "GC Thread#9"                     [id=10540, stack(0x0000006a5b800000,0x0000006a5b900000) (1024K)]
Total: 12

Threads with active compile tasks:
C2 CompilerThread0  5309 5671       4       lombok.core.AST::buildWithField (21 bytes)
C2 CompilerThread1  5309 5672   !   4       lombok.core.AST::buildWithField0 (121 bytes)
C2 CompilerThread2  5309 5702       4       lombok.eclipse.EclipseAST::buildStatement (57 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffc811cea00] SystemDictionary_lock - owner thread: 0x0000000000000000

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000021600000000-0x0000021600ba0000-0x0000021600ba0000), size 12189696, SharedBaseAddress: 0x0000021600000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000021601000000-0x0000021641000000, reserved size: 1073741824
Narrow klass base: 0x0000021600000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 10

Heap:
 PSYoungGen      total 20992K, used 5886K [0x00000000eab00000, 0x00000000ec100000, 0x0000000100000000)
  eden space 19456K, 22% used [0x00000000eab00000,0x00000000eaf45410,0x00000000ebe00000)
  from space 1536K, 98% used [0x00000000ebf80000,0x00000000ec0fa4d0,0x00000000ec100000)
  to   space 1024K, 0% used [0x00000000ebe00000,0x00000000ebe00000,0x00000000ebf00000)
 ParOldGen       total 68608K, used 37641K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 54% used [0x00000000c0000000,0x00000000c24c2450,0x00000000c4300000)
 Metaspace       used 41489K, committed 42560K, reserved 1114112K
  class space    used 4125K, committed 4608K, reserved 1048576K

Card table byte_map: [0x0000021667ad0000,0x0000021667ce0000] _byte_map_base: 0x00000216674d0000

Marking Bits: (ParMarkBitMap*) 0x00007ffc811d3260
 Begin Bits: [0x0000021667e40000, 0x0000021668e40000)
 End Bits:   [0x0000021668e40000, 0x0000021669e40000)

Polling page: 0x0000021653a50000

Metaspace:

Usage:
  Non-class:     36.49 MB used.
      Class:      4.03 MB used.
       Both:     40.52 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      37.06 MB ( 58%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      41.56 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  10.36 MB
       Class:  11.51 MB
        Both:  21.87 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1078.
num_arena_deaths: 212.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 665.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 308.
num_chunks_taken_from_freelist: 3190.
num_chunk_merges: 115.
num_chunk_splits: 1834.
num_chunks_enlarged: 943.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3524Kb max_used=3524Kb free=116475Kb
 bounds [0x00000216603c0000, 0x0000021660740000, 0x00000216678f0000]
CodeHeap 'profiled nmethods': size=120000Kb used=11972Kb max_used=11972Kb free=108027Kb
 bounds [0x00000216588f0000, 0x00000216594b0000, 0x000002165fe20000]
CodeHeap 'non-nmethods': size=5760Kb used=1394Kb max_used=1462Kb free=4365Kb
 bounds [0x000002165fe20000, 0x0000021660090000, 0x00000216603c0000]
 total_blobs=6308 nmethods=5606 adapters=607
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.221 Thread 0x000002166b4a9090 5929       3       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::getModifiers (17 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5929 0x000002165945dc90 code [0x000002165945de40, 0x000002165945dfc0]
Event: 5.221 Thread 0x000002166b4a9090 5943       3       org.eclipse.jdt.internal.compiler.lookup.TypeBinding::notEquals (76 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5943 0x000002165945e090 code [0x000002165945e280, 0x000002165945e9c8]
Event: 5.221 Thread 0x000002166b4a9090 5944       3       org.eclipse.jdt.internal.compiler.lookup.PackageBinding::subsumes (9 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5944 0x000002165945eb10 code [0x000002165945ecc0, 0x000002165945ee30]
Event: 5.221 Thread 0x000002166b4a9090 5941       1       org.eclipse.jdt.internal.compiler.classfmt.FieldInfo::getAnnotations (2 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5941 0x000002166071bf90 code [0x000002166071c120, 0x000002166071c1f0]
Event: 5.221 Thread 0x000002166b4a9090 5927       3       org.eclipse.jdt.core.compiler.CharOperation::arrayConcat (43 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5927 0x000002165945ef10 code [0x000002165945f100, 0x000002165945f728]
Event: 5.221 Thread 0x000002166b4a9090 5926       1       org.eclipse.jdt.internal.compiler.lookup.ModuleBinding::isUnnamed (2 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5926 0x000002166071c290 code [0x000002166071c420, 0x000002166071c4e8]
Event: 5.221 Thread 0x000002166b4a9090 5933       1       org.eclipse.jdt.internal.compiler.lookup.UnresolvedReferenceBinding::isUnresolvedType (2 bytes)
Event: 5.221 Thread 0x000002166b4a9090 nmethod 5933 0x000002166071c590 code [0x000002166071c720, 0x000002166071c7e8]
Event: 5.225 Thread 0x000002166b4a9090 5945       3       org.eclipse.osgi.storage.url.bundleresource.Handler::<init> (7 bytes)
Event: 5.225 Thread 0x000002166b4a9090 nmethod 5945 0x000002165945f910 code [0x000002165945fac0, 0x000002165945fd10]
Event: 5.227 Thread 0x000002166b4a9090 5946       3       java.lang.invoke.LambdaForm$Name::withConstraint (20 bytes)
Event: 5.227 Thread 0x000002166b4a9090 nmethod 5946 0x000002165945fe10 code [0x000002165945ffc0, 0x00000216594601d8]
Event: 5.227 Thread 0x000002166b4a9090 5947       3       java.nio.file.Files::newByteChannel (11 bytes)
Event: 5.228 Thread 0x000002166b4a9090 nmethod 5947 0x0000021659460290 code [0x0000021659460460, 0x0000021659460820]

GC Heap History (20 events):
Event: 3.769 GC heap after
{Heap after GC invocations=16 (full 1):
 PSYoungGen      total 23040K, used 2232K [0x00000000eab00000, 0x00000000ec600000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2560K, 87% used [0x00000000ebf00000,0x00000000ec12e200,0x00000000ec180000)
  to   space 3584K, 0% used [0x00000000ec280000,0x00000000ec280000,0x00000000ec600000)
 ParOldGen       total 68608K, used 31631K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 46% used [0x00000000c0000000,0x00000000c1ee3e78,0x00000000c4300000)
 Metaspace       used 32150K, committed 33280K, reserved 1114112K
  class space    used 3189K, committed 3648K, reserved 1048576K
}
Event: 3.945 GC heap before
{Heap before GC invocations=17 (full 1):
 PSYoungGen      total 23040K, used 22661K [0x00000000eab00000, 0x00000000ec600000, 0x0000000100000000)
  eden space 20480K, 99% used [0x00000000eab00000,0x00000000ebef3420,0x00000000ebf00000)
  from space 2560K, 87% used [0x00000000ebf00000,0x00000000ec12e200,0x00000000ec180000)
  to   space 3584K, 0% used [0x00000000ec280000,0x00000000ec280000,0x00000000ec600000)
 ParOldGen       total 68608K, used 31631K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 46% used [0x00000000c0000000,0x00000000c1ee3e78,0x00000000c4300000)
 Metaspace       used 34179K, committed 35200K, reserved 1114112K
  class space    used 3437K, committed 3904K, reserved 1048576K
}
Event: 3.946 GC heap after
{Heap after GC invocations=17 (full 1):
 PSYoungGen      total 23040K, used 2195K [0x00000000eab00000, 0x00000000ec500000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2560K, 85% used [0x00000000ec280000,0x00000000ec4a4d08,0x00000000ec500000)
  to   space 2560K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec180000)
 ParOldGen       total 68608K, used 33692K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 49% used [0x00000000c0000000,0x00000000c20e73d0,0x00000000c4300000)
 Metaspace       used 34179K, committed 35200K, reserved 1114112K
  class space    used 3437K, committed 3904K, reserved 1048576K
}
Event: 4.024 GC heap before
{Heap before GC invocations=18 (full 1):
 PSYoungGen      total 23040K, used 5235K [0x00000000eab00000, 0x00000000ec500000, 0x0000000100000000)
  eden space 20480K, 14% used [0x00000000eab00000,0x00000000eadf81a8,0x00000000ebf00000)
  from space 2560K, 85% used [0x00000000ec280000,0x00000000ec4a4d08,0x00000000ec500000)
  to   space 2560K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec180000)
 ParOldGen       total 68608K, used 33692K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 49% used [0x00000000c0000000,0x00000000c20e73d0,0x00000000c4300000)
 Metaspace       used 34754K, committed 35840K, reserved 1114112K
  class space    used 3467K, committed 3968K, reserved 1048576K
}
Event: 4.025 GC heap after
{Heap after GC invocations=18 (full 1):
 PSYoungGen      total 23040K, used 419K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2560K, 16% used [0x00000000ebf00000,0x00000000ebf68c30,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ec300000)
 ParOldGen       total 68608K, used 35682K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 52% used [0x00000000c0000000,0x00000000c22d8b48,0x00000000c4300000)
 Metaspace       used 34754K, committed 35840K, reserved 1114112K
  class space    used 3467K, committed 3968K, reserved 1048576K
}
Event: 4.025 GC heap before
{Heap before GC invocations=19 (full 2):
 PSYoungGen      total 23040K, used 419K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2560K, 16% used [0x00000000ebf00000,0x00000000ebf68c30,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ec300000)
 ParOldGen       total 68608K, used 35682K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 52% used [0x00000000c0000000,0x00000000c22d8b48,0x00000000c4300000)
 Metaspace       used 34754K, committed 35840K, reserved 1114112K
  class space    used 3467K, committed 3968K, reserved 1048576K
}
Event: 4.059 GC heap after
{Heap after GC invocations=19 (full 2):
 PSYoungGen      total 23040K, used 0K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2560K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ec300000)
 ParOldGen       total 68608K, used 33212K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c206f2f8,0x00000000c4300000)
 Metaspace       used 34581K, committed 35840K, reserved 1114112K
  class space    used 3419K, committed 3968K, reserved 1048576K
}
Event: 4.123 GC heap before
{Heap before GC invocations=20 (full 2):
 PSYoungGen      total 23040K, used 20480K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000eab00000,0x00000000ebf00000,0x00000000ebf00000)
  from space 2560K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec180000)
  to   space 1536K, 0% used [0x00000000ec180000,0x00000000ec180000,0x00000000ec300000)
 ParOldGen       total 68608K, used 33212K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c206f2f8,0x00000000c4300000)
 Metaspace       used 34621K, committed 35840K, reserved 1114112K
  class space    used 3420K, committed 3968K, reserved 1048576K
}
Event: 4.124 GC heap after
{Heap after GC invocations=20 (full 2):
 PSYoungGen      total 22016K, used 1453K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 1536K, 94% used [0x00000000ec180000,0x00000000ec2eb5a8,0x00000000ec300000)
  to   space 2048K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec100000)
 ParOldGen       total 68608K, used 33220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20712f8,0x00000000c4300000)
 Metaspace       used 34621K, committed 35840K, reserved 1114112K
  class space    used 3420K, committed 3968K, reserved 1048576K
}
Event: 4.215 GC heap before
{Heap before GC invocations=21 (full 2):
 PSYoungGen      total 22016K, used 21913K [0x00000000eab00000, 0x00000000ec300000, 0x0000000100000000)
  eden space 20480K, 99% used [0x00000000eab00000,0x00000000ebefb188,0x00000000ebf00000)
  from space 1536K, 94% used [0x00000000ec180000,0x00000000ec2eb5a8,0x00000000ec300000)
  to   space 2048K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec100000)
 ParOldGen       total 68608K, used 33220K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20712f8,0x00000000c4300000)
 Metaspace       used 35797K, committed 36800K, reserved 1114112K
  class space    used 3550K, committed 3968K, reserved 1048576K
}
Event: 4.216 GC heap after
{Heap after GC invocations=21 (full 2):
 PSYoungGen      total 22528K, used 320K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 2048K, 15% used [0x00000000ebf00000,0x00000000ebf50000,0x00000000ec100000)
  to   space 1024K, 0% used [0x00000000ec100000,0x00000000ec100000,0x00000000ec200000)
 ParOldGen       total 68608K, used 33284K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20812f8,0x00000000c4300000)
 Metaspace       used 35797K, committed 36800K, reserved 1114112K
  class space    used 3550K, committed 3968K, reserved 1048576K
}
Event: 4.539 GC heap before
{Heap before GC invocations=22 (full 2):
 PSYoungGen      total 22528K, used 20800K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000eab00000,0x00000000ebf00000,0x00000000ebf00000)
  from space 2048K, 15% used [0x00000000ebf00000,0x00000000ebf50000,0x00000000ec100000)
  to   space 1024K, 0% used [0x00000000ec100000,0x00000000ec100000,0x00000000ec200000)
 ParOldGen       total 68608K, used 33284K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20812f8,0x00000000c4300000)
 Metaspace       used 37336K, committed 38336K, reserved 1114112K
  class space    used 3686K, committed 4160K, reserved 1048576K
}
Event: 4.540 GC heap after
{Heap after GC invocations=22 (full 2):
 PSYoungGen      total 21504K, used 1011K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 1024K, 98% used [0x00000000ec100000,0x00000000ec1fcf38,0x00000000ec200000)
  to   space 1536K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec080000)
 ParOldGen       total 68608K, used 33484K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20b32f8,0x00000000c4300000)
 Metaspace       used 37336K, committed 38336K, reserved 1114112K
  class space    used 3686K, committed 4160K, reserved 1048576K
}
Event: 4.846 GC heap before
{Heap before GC invocations=23 (full 2):
 PSYoungGen      total 21504K, used 21488K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 99% used [0x00000000eab00000,0x00000000ebeff100,0x00000000ebf00000)
  from space 1024K, 98% used [0x00000000ec100000,0x00000000ec1fcf38,0x00000000ec200000)
  to   space 1536K, 0% used [0x00000000ebf00000,0x00000000ebf00000,0x00000000ec080000)
 ParOldGen       total 68608K, used 33548K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 48% used [0x00000000c0000000,0x00000000c20c3308,0x00000000c4300000)
 Metaspace       used 38823K, committed 39808K, reserved 1114112K
  class space    used 3841K, committed 4288K, reserved 1048576K
}
Event: 4.846 GC heap after
{Heap after GC invocations=23 (full 2):
 PSYoungGen      total 22016K, used 928K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebf00000)
  from space 1536K, 60% used [0x00000000ebf00000,0x00000000ebfe8010,0x00000000ec080000)
  to   space 1536K, 0% used [0x00000000ec080000,0x00000000ec080000,0x00000000ec200000)
 ParOldGen       total 68608K, used 34521K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 50% used [0x00000000c0000000,0x00000000c21b65c8,0x00000000c4300000)
 Metaspace       used 38823K, committed 39808K, reserved 1114112K
  class space    used 3841K, committed 4288K, reserved 1048576K
}
Event: 4.991 GC heap before
{Heap before GC invocations=24 (full 2):
 PSYoungGen      total 22016K, used 21408K [0x00000000eab00000, 0x00000000ec200000, 0x0000000100000000)
  eden space 20480K, 100% used [0x00000000eab00000,0x00000000ebf00000,0x00000000ebf00000)
  from space 1536K, 60% used [0x00000000ebf00000,0x00000000ebfe8010,0x00000000ec080000)
  to   space 1536K, 0% used [0x00000000ec080000,0x00000000ec080000,0x00000000ec200000)
 ParOldGen       total 68608K, used 34522K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 50% used [0x00000000c0000000,0x00000000c21b6bb0,0x00000000c4300000)
 Metaspace       used 39826K, committed 40832K, reserved 1114112K
  class space    used 3957K, committed 4416K, reserved 1048576K
}
Event: 4.993 GC heap after
{Heap after GC invocations=24 (full 2):
 PSYoungGen      total 20992K, used 1532K [0x00000000eab00000, 0x00000000ec400000, 0x0000000100000000)
  eden space 19456K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe00000)
  from space 1536K, 99% used [0x00000000ec080000,0x00000000ec1ff078,0x00000000ec200000)
  to   space 2560K, 0% used [0x00000000ebe00000,0x00000000ebe00000,0x00000000ec080000)
 ParOldGen       total 68608K, used 35203K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 51% used [0x00000000c0000000,0x00000000c2260c40,0x00000000c4300000)
 Metaspace       used 39826K, committed 40832K, reserved 1114112K
  class space    used 3957K, committed 4416K, reserved 1048576K
}
Event: 5.036 GC heap before
{Heap before GC invocations=25 (full 2):
 PSYoungGen      total 20992K, used 20988K [0x00000000eab00000, 0x00000000ec400000, 0x0000000100000000)
  eden space 19456K, 100% used [0x00000000eab00000,0x00000000ebe00000,0x00000000ebe00000)
  from space 1536K, 99% used [0x00000000ec080000,0x00000000ec1ff078,0x00000000ec200000)
  to   space 2560K, 0% used [0x00000000ebe00000,0x00000000ebe00000,0x00000000ec080000)
 ParOldGen       total 68608K, used 35203K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 51% used [0x00000000c0000000,0x00000000c2260c40,0x00000000c4300000)
 Metaspace       used 39950K, committed 41088K, reserved 1114112K
  class space    used 3967K, committed 4480K, reserved 1048576K
}
Event: 5.037 GC heap after
{Heap after GC invocations=25 (full 2):
 PSYoungGen      total 20480K, used 912K [0x00000000eab00000, 0x00000000ec100000, 0x0000000100000000)
  eden space 19456K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ebe00000)
  from space 1024K, 89% used [0x00000000ebe00000,0x00000000ebee4030,0x00000000ebf00000)
  to   space 1536K, 0% used [0x00000000ebf80000,0x00000000ebf80000,0x00000000ec100000)
 ParOldGen       total 68608K, used 36486K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 53% used [0x00000000c0000000,0x00000000c23a1bd0,0x00000000c4300000)
 Metaspace       used 39950K, committed 41088K, reserved 1114112K
  class space    used 3967K, committed 4480K, reserved 1048576K
}
Event: 5.237 GC heap before
{Heap before GC invocations=26 (full 2):
 PSYoungGen      total 20480K, used 20333K [0x00000000eab00000, 0x00000000ec100000, 0x0000000100000000)
  eden space 19456K, 99% used [0x00000000eab00000,0x00000000ebdf74d0,0x00000000ebe00000)
  from space 1024K, 89% used [0x00000000ebe00000,0x00000000ebee4030,0x00000000ebf00000)
  to   space 1536K, 0% used [0x00000000ebf80000,0x00000000ebf80000,0x00000000ec100000)
 ParOldGen       total 68608K, used 36486K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 53% used [0x00000000c0000000,0x00000000c23a1bd0,0x00000000c4300000)
 Metaspace       used 40896K, committed 41984K, reserved 1114112K
  class space    used 4058K, committed 4544K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.036 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.087 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.139 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.147 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.152 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.156 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.174 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.254 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 1.175 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927\eclipse_11909.dll
Event: 1.851 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-68506\jna6688816465761627525.dll

Deoptimization events (20 events):
Event: 5.184 Thread 0x0000021671499c20 DEOPT PACKING pc=0x000002166071737c sp=0x0000006a5bcfd940
Event: 5.184 Thread 0x0000021671499c20 DEOPT UNPACKING pc=0x000002165fe73aa2 sp=0x0000006a5bcfd8d8 mode 2
Event: 5.196 Thread 0x0000021671499c20 DEOPT PACKING pc=0x00000216591bc743 sp=0x0000006a5bcfdc20
Event: 5.196 Thread 0x0000021671499c20 DEOPT UNPACKING pc=0x000002165fe74242 sp=0x0000006a5bcfd098 mode 0
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021660708fdc relative=0x00000000000006bc
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021660708fdc method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 5937 c2
Event: 5.207 Thread 0x0000021673ec2030 DEOPT PACKING pc=0x0000021660708fdc sp=0x0000006a5dbfecc0
Event: 5.207 Thread 0x0000021673ec2030 DEOPT UNPACKING pc=0x000002165fe73aa2 sp=0x0000006a5dbfec50 mode 2
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021660708fdc relative=0x00000000000006bc
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021660708fdc method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 5937 c2
Event: 5.207 Thread 0x0000021673ec2030 DEOPT PACKING pc=0x0000021660708fdc sp=0x0000006a5dbfecc0
Event: 5.207 Thread 0x0000021673ec2030 DEOPT UNPACKING pc=0x000002165fe73aa2 sp=0x0000006a5dbfec50 mode 2
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021660708fdc relative=0x00000000000006bc
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021660708fdc method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 5937 c2
Event: 5.207 Thread 0x0000021673ec2030 DEOPT PACKING pc=0x0000021660708fdc sp=0x0000006a5dbfecc0
Event: 5.207 Thread 0x0000021673ec2030 DEOPT UNPACKING pc=0x000002165fe73aa2 sp=0x0000006a5dbfec50 mode 2
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021660708fdc relative=0x00000000000006bc
Event: 5.207 Thread 0x0000021673ec2030 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021660708fdc method=org.eclipse.jdt.internal.compiler.parser.Parser.consumeRule(I)V @ 5937 c2
Event: 5.207 Thread 0x0000021673ec2030 DEOPT PACKING pc=0x0000021660708fdc sp=0x0000006a5dbfecc0
Event: 5.207 Thread 0x0000021673ec2030 DEOPT UNPACKING pc=0x000002165fe73aa2 sp=0x0000006a5dbfec50 mode 2

Classes loaded (20 events):
Event: 4.973 Loading class java/util/regex/PatternSyntaxException
Event: 4.973 Loading class java/util/regex/PatternSyntaxException done
Event: 5.104 Loading class java/util/stream/ReduceOps$4
Event: 5.104 Loading class java/util/stream/ReduceOps$4 done
Event: 5.104 Loading class java/util/stream/ReduceOps$4ReducingSink
Event: 5.104 Loading class java/util/stream/ReduceOps$4ReducingSink done
Event: 5.105 Loading class java/nio/file/Files$2
Event: 5.105 Loading class java/nio/file/Files$2 done
Event: 5.106 Loading class java/util/stream/MatchOps$MatchKind
Event: 5.106 Loading class java/util/stream/MatchOps$MatchKind done
Event: 5.106 Loading class java/util/stream/MatchOps
Event: 5.106 Loading class java/util/stream/MatchOps done
Event: 5.106 Loading class java/util/stream/MatchOps$MatchOp
Event: 5.106 Loading class java/util/stream/MatchOps$MatchOp done
Event: 5.106 Loading class java/util/stream/MatchOps$BooleanTerminalSink
Event: 5.106 Loading class java/util/stream/MatchOps$BooleanTerminalSink done
Event: 5.106 Loading class java/util/stream/MatchOps$1MatchSink
Event: 5.106 Loading class java/util/stream/MatchOps$1MatchSink done
Event: 5.206 Loading class java/util/concurrent/ArrayBlockingQueue
Event: 5.206 Loading class java/util/concurrent/ArrayBlockingQueue done

Classes unloaded (20 events):
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601361c00 'java/lang/invoke/LambdaForm$MH+0x0000021601361c00'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601361800 'java/lang/invoke/LambdaForm$MH+0x0000021601361800'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601361400 'java/lang/invoke/LambdaForm$MH+0x0000021601361400'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601361000 'java/lang/invoke/LambdaForm$MH+0x0000021601361000'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601360c00 'java/lang/invoke/LambdaForm$MH+0x0000021601360c00'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601360400 'java/lang/invoke/LambdaForm$MH+0x0000021601360400'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x0000021601360000 'java/lang/invoke/LambdaForm$MH+0x0000021601360000'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135fc00 'java/lang/invoke/LambdaForm$MH+0x000002160135fc00'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135f400 'java/lang/invoke/LambdaForm$MH+0x000002160135f400'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135ec00 'java/lang/invoke/LambdaForm$MH+0x000002160135ec00'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135e800 'java/lang/invoke/LambdaForm$MH+0x000002160135e800'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135f000 'java/lang/invoke/LambdaForm$MH+0x000002160135f000'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135e400 'java/lang/invoke/LambdaForm$MH+0x000002160135e400'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135e000 'java/lang/invoke/LambdaForm$MH+0x000002160135e000'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135d800 'java/lang/invoke/LambdaForm$MH+0x000002160135d800'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135d400 'java/lang/invoke/LambdaForm$MH+0x000002160135d400'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135d000 'java/lang/invoke/LambdaForm$DMH+0x000002160135d000'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135cc00 'java/lang/invoke/LambdaForm$DMH+0x000002160135cc00'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135c800 'java/lang/invoke/LambdaForm$DMH+0x000002160135c800'
Event: 4.034 Thread 0x000002165546a850 Unloading class 0x000002160135c400 'java/lang/invoke/LambdaForm$DMH+0x000002160135c400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 4.242 Thread 0x0000021671499c20 Exception <a 'java/io/FileNotFoundException'{0x00000000ead21148}> (0x00000000ead21148) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.242 Thread 0x0000021671499c20 Exception <a 'java/io/FileNotFoundException'{0x00000000ead22a08}> (0x00000000ead22a08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.243 Thread 0x0000021671499c20 Exception <a 'java/io/FileNotFoundException'{0x00000000ead23ff0}> (0x00000000ead23ff0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.243 Thread 0x0000021671499c20 Exception <a 'java/io/FileNotFoundException'{0x00000000ead25470}> (0x00000000ead25470) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.821 Thread 0x00000216736af080 Exception <a 'java/lang/LinkageError'{0x00000000ebe23c08}: loader lombok.launch.ShadowClassLoader @33662a1a attempted duplicate class definition for lombok.launch.PatchFixesHider$FieldInitializer. (lombok.launch.PatchFixesHider$FieldInitializer is in unnamed module of loader lombok.launch.ShadowClassLoader @33662a1a, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @3aae24b2)> (0x00000000ebe23c08) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 4.821 Thread 0x0000021672b4d8c0 Exception <a 'java/lang/LinkageError'{0x00000000ebe117a8}: loader lombok.launch.ShadowClassLoader @33662a1a attempted duplicate class definition for lombok.launch.PatchFixesHider$FieldInitializer. (lombok.launch.PatchFixesHider$FieldInitializer is in unnamed module of loader lombok.launch.ShadowClassLoader @33662a1a, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @3aae24b2)> (0x00000000ebe117a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 4.849 Thread 0x0000021672b4d230 Exception <a 'java/lang/LinkageError'{0x00000000eab75178}: loader lombok.launch.ShadowClassLoader @33662a1a attempted duplicate class definition for lombok.launch.PatchFixesHider$PatchFixes. (lombok.launch.PatchFixesHider$PatchFixes is in unnamed module of loader lombok.launch.ShadowClassLoader @33662a1a, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @3aae24b2)> (0x00000000eab75178) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 4.850 Thread 0x0000021672b4d8c0 Exception <a 'java/lang/LinkageError'{0x00000000eab57870}: loader lombok.launch.ShadowClassLoader @33662a1a attempted duplicate class definition for lombok.launch.PatchFixesHider$PatchFixes. (lombok.launch.PatchFixesHider$PatchFixes is in unnamed module of loader lombok.launch.ShadowClassLoader @33662a1a, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @3aae24b2)> (0x00000000eab57870) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 4.860 Thread 0x0000021672b4d230 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabb6ea8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eabb6ea8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.860 Thread 0x0000021672b4d8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eab8bec8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eab8bec8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.860 Thread 0x0000021672b4d230 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabbafb8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eabbafb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.860 Thread 0x0000021672b4d8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabbfcb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eabbfcb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.860 Thread 0x0000021672b4d8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabc3480}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eabc3480) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.860 Thread 0x0000021672b4d230 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabccda0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eabccda0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.924 Thread 0x0000021671499c20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb346498}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, int, int)'> (0x00000000eb346498) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.934 Thread 0x0000021671499c20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb3e1020}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x00000000eb3e1020) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.934 Thread 0x0000021671499c20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb3e5330}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, int)'> (0x00000000eb3e5330) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.047 Thread 0x00000216736af080 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eae42cb0}: Found class java.lang.Object, but interface was expected> (0x00000000eae42cb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.051 Thread 0x00000216736af080 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eae5aa10}: Found class java.lang.Object, but interface was expected> (0x00000000eae5aa10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.107 Thread 0x0000021671499c20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb852d78}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb852d78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 4.124 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.215 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.216 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.231 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.231 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.539 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.541 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.763 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.763 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.799 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.799 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 4.845 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.846 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.846 Executing VM operation: ParallelGCSystemGC (GCLocker Initiated GC)
Event: 4.846 Executing VM operation: ParallelGCSystemGC (GCLocker Initiated GC) done
Event: 4.991 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.993 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.036 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 5.037 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.237 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658d92690
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658d93010
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dc2910
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dc2d10
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dc3710
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dc9910
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658ddf010
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dec110
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658ded410
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dee210
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658defa90
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658df0090
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658df5910
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658df5e10
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dfbe90
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dfd490
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dfea10
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658dfef10
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658e03790
Event: 4.040 Thread 0x000002165546a850 flushing  nmethod 0x0000021658e03b10

Events (20 events):
Event: 3.322 Thread 0x00000216736af080 Thread added: 0x0000021672b4c510
Event: 3.323 Thread 0x00000216736af080 Thread added: 0x0000021672b4d230
Event: 3.325 Thread 0x00000216736af080 Thread added: 0x0000021672b4d8c0
Event: 3.326 Thread 0x00000216736af080 Thread added: 0x0000021672b4df50
Event: 3.500 Thread 0x000002166b4a9090 Thread added: 0x0000021673b73cd0
Event: 4.314 Thread 0x0000021671499c20 Thread added: 0x0000021672b4cba0
Event: 4.314 Thread 0x0000021671499c20 Thread added: 0x0000021672b4e5e0
Event: 4.314 Thread 0x0000021671499c20 Thread added: 0x0000021672b4b160
Event: 4.314 Thread 0x0000021672b4cba0 Thread added: 0x0000021672b4b7f0
Event: 4.314 Thread 0x0000021672b4e5e0 Thread added: 0x0000021672b4be80
Event: 4.314 Thread 0x0000021672b4b160 Thread added: 0x0000021673ebf240
Event: 4.314 Thread 0x0000021672b4be80 Thread added: 0x0000021673ebbdc0
Event: 4.314 Thread 0x0000021673ebf240 Thread added: 0x0000021673ebff60
Event: 4.314 Thread 0x0000021671499c20 Thread added: 0x0000021673ebc450
Event: 4.315 Thread 0x0000021671499c20 Thread added: 0x0000021673ec1310
Event: 4.549 Thread 0x0000021673b73cd0 Thread exited: 0x0000021673b73cd0
Event: 4.549 Thread 0x000002166b68ae30 Thread exited: 0x000002166b68ae30
Event: 4.669 Thread 0x000002166b4a9090 Thread added: 0x000002166b68ae30
Event: 4.976 Thread 0x000002166b4a9090 Thread added: 0x00000216720308a0
Event: 5.207 Thread 0x0000021671499c20 Thread added: 0x0000021673ec2030


Dynamic libraries:
0x00007ff7c8a20000 - 0x00007ff7c8a2e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffccc0b0000 - 0x00007ffccc2c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffccad00000 - 0x00007ffccadc4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffcc9310000 - 0x00007ffcc96e1000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffcc91f0000 - 0x00007ffcc9301000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffcbcec0000 - 0x00007ffcbcede000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffcbcea0000 - 0x00007ffcbceb8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffcca720000 - 0x00007ffcca8d1000 	C:\WINDOWS\System32\USER32.dll
0x00007ffcc9cd0000 - 0x00007ffcc9cf6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffcca270000 - 0x00007ffcca299000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffcc9b30000 - 0x00007ffcc9c4b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffcc9950000 - 0x00007ffcc99ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffc9fc90000 - 0x00007ffc9ff22000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffcc9ee0000 - 0x00007ffcc9f87000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffccb490000 - 0x00007ffccb4c1000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000055400000 - 0x000000005540d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffcca1b0000 - 0x00007ffcca261000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffcca8e0000 - 0x00007ffcca987000 	C:\WINDOWS\System32\sechost.dll
0x00007ffcc97b0000 - 0x00007ffcc97d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffcca990000 - 0x00007ffccaaa4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffcb70d0000 - 0x00007ffcb71d5000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffccb680000 - 0x00007ffccbf08000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffcc99f0000 - 0x00007ffcc9b2f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffccb070000 - 0x00007ffccb400000 	C:\WINDOWS\System32\combase.dll
0x00007ffcca150000 - 0x00007ffcca1ae000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffcc8b40000 - 0x00007ffcc8b4a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffcc5bb0000 - 0x00007ffcc5bbc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffcbcbf0000 - 0x00007ffcbcc7d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ffc80520000 - 0x00007ffc812b0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffcc9d80000 - 0x00007ffcc9df1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffcc90c0000 - 0x00007ffcc910d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffcbb640000 - 0x00007ffcbb674000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffcc90a0000 - 0x00007ffcc90b3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffcc8170000 - 0x00007ffcc8188000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffcc0dc0000 - 0x00007ffcc0dca000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffcc6be0000 - 0x00007ffcc6e12000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffcc9e00000 - 0x00007ffcc9ed7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcb9580000 - 0x00007ffcb95b2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffcc9d00000 - 0x00007ffcc9d7b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffcbd520000 - 0x00007ffcbd52f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffcbce60000 - 0x00007ffcbce7f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffcc70b0000 - 0x00007ffcc79bd000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffccb4e0000 - 0x00007ffccb5ea000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffcc9120000 - 0x00007ffcc914b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffcbcbd0000 - 0x00007ffcbcbe8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffcbce50000 - 0x00007ffcbce60000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffcc3bf0000 - 0x00007ffcc3d1c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffcc85e0000 - 0x00007ffcc864a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcae950000 - 0x00007ffcae966000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffcbb570000 - 0x00007ffcbb580000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffcae900000 - 0x00007ffcae945000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927\eclipse_11909.dll
0x00007ffccaab0000 - 0x00007ffccac51000 	C:\WINDOWS\System32\ole32.dll
0x00007ffcc8830000 - 0x00007ffcc884b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffcc80d0000 - 0x00007ffcc8107000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffcc86d0000 - 0x00007ffcc86f8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffcc8a80000 - 0x00007ffcc8a8c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffcc8b50000 - 0x00007ffcc8b7d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffccb5f0000 - 0x00007ffccb5f9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffc9b1b0000 - 0x00007ffc9b1f9000 	C:\Users\<USER>\AppData\Local\Temp\jna-68506\jna6688816465761627525.dll
0x00007ffccb4d0000 - 0x00007ffccb4d8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffcc3bd0000 - 0x00007ffcc3be9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffcc3bb0000 - 0x00007ffcc3bcf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files (x86)\360\360Safe\safemon;c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1200.v20250212-0927;C:\Users\<USER>\AppData\Local\Temp\jna-68506

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.40.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\de678b1f6bdd3a892fc5d71c674383a0\redhat.java\ss_ws --pipe=\\.\pipe\lsp-a8929cdab9337046cf1ca5c2f7aaaf07-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.40.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.6.1000.v20250131-0606.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts\;D:\Program Files\Python311\;C:\Python310\Scripts\;C:\Python310\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet\;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;e:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;;C:\;rogram Files\python;C:\Program Files\python\Scripts;e:\sofware\BtSoft\panel\script;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI\;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;E:\Program Files\cursor\resources\app\bin
USERNAME=EDY
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 19 days 10:55 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (6310M free)
TotalPageFile size 42752M (AvailPageFile size 10M)
current process WorkingSet (physical memory assigned to process): 238M, peak: 246M
current process commit charge ("private bytes"): 313M, peak: 323M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
