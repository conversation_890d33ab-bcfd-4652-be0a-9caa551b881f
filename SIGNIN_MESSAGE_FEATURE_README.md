# 签到成功提示语功能说明

## 功能概述

为 `userSignIn` 接口增加了根据不同类型的签到课程和签到次数返回相应成功提示语的功能。系统会根据 `signInType`（签到类型）、`pclass_type`（课程类型）、`activity_type`（活动类型）以及用户当月签到次数的组合，从 Nacos 配置中心获取对应的提示语并返回给用户。

## 功能特点

1. **灵活配置**：所有提示语配置存储在 Nacos 配置中心，支持热更新，无需重启服务
2. **多维度匹配**：支持根据签到类型、课程类型、活动类型和签到次数的组合来获取对应提示语
3. **智能计数**：自动统计用户当月同类型签到次数，提供个性化提示语
4. **降级处理**：当找不到匹配配置时，自动使用默认提示语
5. **向后兼容**：不影响现有签到功能，只是增加了提示语字段

## 课程分类结构

- **院外活动** (OUTSIDE_PCLASS)
  - 小型活动、中型活动、大型活动
  - 中型活动细分：高端妈妈班/豪华妈妈班/精品妈妈班等
- **院内活动** (INSIDE_PCLASS)
- **CS活动** (CS)

## 签到次数逻辑

- **院外和院内活动**：区分第一次签到和第二次及以上签到
- **CS活动**：区分第一次、第二次、第三次及以上签到
- 签到次数基于用户当月同类型（channel）签到历史计算

## 修改的文件

### 1. 核心文件
- `UserSignInResponse.java` - 添加了 `message` 字段用于返回提示语
- `SignInMessageConfigService.java` - 新增的配置读取服务类
- `PclassSignInController.java` - 修改控制器以设置 InternalResponse 的 message

### 2. 签到实现类
- `CsSignIn.java` - CS活动签到实现
- `InsidePclassSignIn.java` - 院内课程签到实现  
- `OutsidePclassSignIn.java` - 院外课程签到实现
- `EpsPclassSignIn.java` - 继承自 OutsidePclassSignIn，自动获得功能

## 配置说明

### Nacos 配置结构

在 Nacos 配置中心创建配置文件，建议配置信息：

- **Data ID**: `signin-message-config.yaml`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: YAML

### 配置内容示例

```yaml
signin:
  message:
    # 院外活动配置
    outside:
      small:
        first: "高端妈妈班:恭喜您首次签到小型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到小型豪华妈妈班活动！"
        second: "高端妈妈班:欢迎您再次参加小型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加小型豪华妈妈班活动！"
      medium:
        first: "高端妈妈班:恭喜您首次签到中型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到中型豪华妈妈班活动！"
        second: "高端妈妈班:欢迎您再次参加中型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加中型豪华妈妈班活动！"
      large:
        first: "高端妈妈班:恭喜您首次签到大型高端妈妈班活动！,豪华妈妈班:恭喜您首次签到大型豪华妈妈班活动！"
        second: "高端妈妈班:欢迎您再次参加大型高端妈妈班活动！,豪华妈妈班:欢迎您再次参加大型豪华妈妈班活动！"

    # 院内活动配置
    inside:
      first: "高端妈妈班:恭喜您首次签到院内高端妈妈班活动！,豪华妈妈班:恭喜您首次签到院内豪华妈妈班活动！"
      second: "高端妈妈班:欢迎您再次参加院内高端妈妈班活动！,豪华妈妈班:欢迎您再次参加院内豪华妈妈班活动！"

    # CS活动配置
    cs:
      first: "CS活动:恭喜您首次签到CS活动！,专家讲座:恭喜您首次签到专家讲座活动！"
      second: "CS活动:欢迎您再次参加CS活动！,专家讲座:欢迎您再次参加专家讲座活动！"
      third: "CS活动:感谢您的持续参与CS活动！,专家讲座:感谢您的持续参与专家讲座活动！"
```

### 配置格式说明

- **院外活动**：按课程类型（small/medium/large）和签到次数（first/second）分类
- **院内活动**：按签到次数（first/second）分类
- **CS活动**：按签到次数（first/second/third）分类
- 配置值格式：`activity_type1:message1,activity_type2:message2`
- 支持多个活动类型的配置，用逗号分隔
- 冒号前是活动类型，冒号后是对应的提示语

## 使用示例

### 请求示例
```json
{
  "activityId": 12345,
  "cellphone": "13800138000",
  "signInType": "INSIDE_PCLASS",
  "userName": "张三"
}
```

### 响应示例

**首次签到院外小型高端妈妈班活动**：
```json
{
  "status": "01",
  "message": "恭喜您首次签到小型高端妈妈班活动！",
  "body": {
    "id": 123,
    "activityCode": "CLASS001",
    "qrCode": "https://example.com/qrcode",
    "isAdd": false,
    "isSigned": false,
    "message": "恭喜您首次签到小型高端妈妈班活动！"
  }
}
```

**第二次签到院外小型高端妈妈班活动**：
```json
{
  "status": "01",
  "message": "欢迎您再次参加小型高端妈妈班活动！",
  "body": {
    "id": 124,
    "activityCode": "CLASS002",
    "qrCode": "https://example.com/qrcode",
    "isAdd": false,
    "isSigned": false,
    "message": "欢迎您再次参加小型高端妈妈班活动！"
  }
}
```

## 工作流程

1. 用户发起签到请求
2. 系统执行原有的签到验证和处理逻辑
3. 从课程信息中获取 `signInType`、`pclass_type` 和 `activity_type`
4. 调用 `ActivitySignInMapper.getSignInHistoryForCurrentMonth()` 获取用户当月签到历史
5. 根据签到历史计算当月同类型签到次数
6. 调用 `SignInMessageConfigService` 获取对应的提示语
7. 将提示语设置到 `UserSignInResponse.message` 字段
8. 控制器将提示语同时设置到 `InternalResponse.message` 字段
9. 返回包含提示语的响应

## 降级策略

- 当 Nacos 配置不可用时，使用默认提示语："签到成功！"
- 当找不到匹配的课程类型配置时，使用默认提示语
- 当找不到匹配的活动类型配置时，使用默认提示语
- 当配置格式错误时，记录错误日志并使用默认提示语

## 注意事项

1. **配置热更新**：修改 Nacos 配置后会自动生效，无需重启服务
2. **配置格式**：严格按照 `activity_type:message` 的格式配置，注意冒号和逗号的使用
3. **字符编码**：确保配置中的中文字符编码正确
4. **性能考虑**：配置读取有缓存机制，不会频繁访问 Nacos
5. **日志监控**：关注应用日志中的配置读取相关信息

## 测试建议

1. **正常场景测试**：验证不同课程类型和活动类型组合的提示语是否正确
2. **配置缺失测试**：验证配置缺失时是否正确使用默认提示语
3. **配置热更新测试**：验证修改 Nacos 配置后是否立即生效
4. **异常场景测试**：验证 Nacos 不可用时的降级处理
5. **性能测试**：验证增加提示语功能后对签到接口性能的影响

## 扩展说明

如需添加新的课程类型或活动类型：

1. 在 Nacos 配置中添加对应的配置项
2. 如果需要新的课程类型，在 `SignInMessageConfigService.getConfigMessagesByPclassType()` 方法中添加对应的 case
3. 无需修改签到实现类的代码，系统会自动读取新配置
