#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=23256, tid=34152
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Fri Jan 31 13:31:34 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.414642 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000022516686c60):  JavaThread "main"             [_thread_in_vm, id=34152, stack(0x000000ec09f00000,0x000000ec0a000000) (1024K)]

Stack: [0x000000ec09f00000,0x000000ec0a000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x8bba1e]
V  [jvm.dll+0x684b95]
V  [jvm.dll+0x1ea641]
V  [jvm.dll+0x1ea40e]
V  [jvm.dll+0x6874a8]
V  [jvm.dll+0x6872e2]
V  [jvm.dll+0x68554e]
V  [jvm.dll+0x272c66]
V  [jvm.dll+0x21d63b]
V  [jvm.dll+0x212b29]
V  [jvm.dll+0x5c2c23]
V  [jvm.dll+0x838460]
V  [jvm.dll+0x83858e]
V  [jvm.dll+0x47e88d]
V  [jvm.dll+0x4846c8]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.4
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.4
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;Ljava/nio/ByteBuffer;Ljava/security/ProtectionDomain;)Ljava/lang/Class;+38 java.base@21.0.4
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;Ljava/nio/ByteBuffer;Ljava/security/CodeSource;)Ljava/lang/Class;+8 java.base@21.0.4
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;)Ljava/lang/Class;+127 java.base@21.0.4
J 750 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@21.0.4 (143 bytes) @ 0x000002251aa864bc [0x000002251aa85f60+0x000000000000055c]
J 764 c1 jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@21.0.4 (22 bytes) @ 0x000002251aa90874 [0x000002251aa90760+0x0000000000000114]
J 763 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.4 (7 bytes) @ 0x000002251aa90454 [0x000002251aa90340+0x0000000000000114]
v  ~StubRoutines::call_stub 0x0000022521ea100d
j  jdk.internal.net.http.Http2ClientImpl.getConnectionFor(Ljdk/internal/net/http/HttpRequestImpl;Ljdk/internal/net/http/Exchange;)Ljava/util/concurrent/CompletableFuture;+1 java.net.http@21.0.4
j  jdk.internal.net.http.ExchangeImpl.get(Ljdk/internal/net/http/Exchange;Ljdk/internal/net/http/HttpConnection;)Ljava/util/concurrent/CompletableFuture;+53 java.net.http@21.0.4
j  jdk.internal.net.http.Exchange.establishExchange(Ljdk/internal/net/http/HttpConnection;)Ljava/util/concurrent/CompletableFuture;+103 java.net.http@21.0.4
j  jdk.internal.net.http.Exchange.responseAsyncImpl0(Ljdk/internal/net/http/HttpConnection;)Ljava/util/concurrent/CompletableFuture;+65 java.net.http@21.0.4
j  jdk.internal.net.http.Exchange.responseAsyncImpl(Ljdk/internal/net/http/HttpConnection;)Ljava/util/concurrent/CompletableFuture;+16 java.net.http@21.0.4
j  jdk.internal.net.http.Exchange.responseAsync()Ljava/util/concurrent/CompletableFuture;+2 java.net.http@21.0.4
j  jdk.internal.net.http.MultiExchange.responseAsyncImpl()Ljava/util/concurrent/CompletableFuture;+100 java.net.http@21.0.4
j  jdk.internal.net.http.MultiExchange.lambda$responseAsync0$2(Ljava/lang/Void;)Ljava/util/concurrent/CompletionStage;+1 java.net.http@21.0.4
j  jdk.internal.net.http.MultiExchange$$Lambda+0x000000080015d690.apply(Ljava/lang/Object;)Ljava/lang/Object;+8 java.net.http@21.0.4
j  java.util.concurrent.CompletableFuture$UniCompose.tryFire(I)Ljava/util/concurrent/CompletableFuture;+105 java.base@21.0.4
j  java.util.concurrent.CompletableFuture.postComplete()V+76 java.base@21.0.4
j  java.util.concurrent.CompletableFuture$AsyncSupply.run()V+57 java.base@21.0.4
j  jdk.internal.net.http.HttpClientImpl$DelegatingExecutor.execute(Ljava/lang/Runnable;)V+21 java.net.http@21.0.4
j  java.util.concurrent.CompletableFuture.completeAsync(Ljava/util/function/Supplier;Ljava/util/concurrent/Executor;)Ljava/util/concurrent/CompletableFuture;+26 java.base@21.0.4
j  jdk.internal.net.http.MultiExchange.responseAsync(Ljava/util/concurrent/Executor;)Ljava/util/concurrent/CompletableFuture;+29 java.net.http@21.0.4
j  jdk.internal.net.http.HttpClientImpl.sendAsync(Ljava/net/http/HttpRequest;Ljava/net/http/HttpResponse$BodyHandler;Ljava/net/http/HttpResponse$PushPromiseHandler;Ljava/util/concurrent/Executor;)Ljava/util/concurrent/CompletableFuture;+246 java.net.http@21.0.4
j  jdk.internal.net.http.HttpClientImpl.send(Ljava/net/http/HttpRequest;Ljava/net/http/HttpResponse$BodyHandler;)Ljava/net/http/HttpResponse;+21 java.net.http@21.0.4
j  jdk.internal.net.http.HttpClientFacade.send(Ljava/net/http/HttpRequest;Ljava/net/http/HttpResponse$BodyHandler;)Ljava/net/http/HttpResponse;+6 java.net.http@21.0.4
j  externalApp.ExternalAppUtil.sendIdeRequest(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)LexternalApp/ExternalAppUtil$Result;+168
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+37
v  ~StubRoutines::call_stub 0x0000022521ea100d

Compiled method (c1)     443  750   !   3       jdk.internal.loader.BuiltinClassLoader::loadClassOrNull (143 bytes)
 total in heap  [0x000002251aa85c90,0x000002251aa874f0] = 6240
 relocation     [0x000002251aa85de8,0x000002251aa85f50] = 360
 main code      [0x000002251aa85f60,0x000002251aa86e28] = 3784
 stub code      [0x000002251aa86e28,0x000002251aa86ee8] = 192
 metadata       [0x000002251aa86ee8,0x000002251aa86f50] = 104
 scopes data    [0x000002251aa86f50,0x000002251aa87178] = 552
 scopes pcs     [0x000002251aa87178,0x000002251aa87358] = 480
 dependencies   [0x000002251aa87358,0x000002251aa87378] = 32
 handler table  [0x000002251aa87378,0x000002251aa874c8] = 336
 nul chk table  [0x000002251aa874c8,0x000002251aa874f0] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader'
  # this:     rdx:rdx   = 'jdk/internal/loader/BuiltinClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9        = boolean
  #           [sp+0xd0]  (sp of caller)
  0x000002251aa85f60: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x000002251aa85f74: ;   {runtime_call ic_miss_stub}
  0x000002251aa85f74: 0f85 0688 | 4607 660f | 1f44 0000 
[Verified Entry Point]
  0x000002251aa85f80: 8984 2400 | 80ff ff55 | 4881 ecc0 | 0000 0090 | 4181 7f20 | 0100 0000 

  0x000002251aa85f98: ;   {runtime_call StubRoutines (final stubs)}
  0x000002251aa85f98: 7405 e8c1 | ed44 0748 | 8954 2468 

  0x000002251aa85fa4: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa85fa4: 48be 28ae | c138 2502 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | 0700 0085 
  0x000002251aa85fc4: ff0f 8439 | 0c00 0044 | 894c 2478 

  0x000002251aa85fd0: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa85fd0: 488b f248 | bf28 aec1 | 3825 0200 | 008b 7608 | 49ba 0000 | 0000 0800 | 0000 4903 | f248 3bb7 
  0x000002251aa85ff0: 2001 0000 | 750d 4883 | 8728 0100 | 0001 e960 | 0000 0048 | 3bb7 3001 | 0000 750d | 4883 8738 
  0x000002251aa86010: 0100 0001 | e94a 0000 | 0048 83bf | 2001 0000 | 0075 1748 | 89b7 2001 | 0000 48c7 | 8728 0100 
  0x000002251aa86030: 0001 0000 | 00e9 2900 | 0000 4883 | bf30 0100 | 0000 7517 | 4889 b730 | 0100 0048 | c787 3801 
  0x000002251aa86050: 0000 0100 | 0000 e908 | 0000 0048 | 8387 1001 | 0000 0149 | 8bf0 4c8b | c648 8bfa | 488b d748 
  0x000002251aa86070: 8974 2470 

  0x000002251aa86074: ;   {optimized virtual_call}
  0x000002251aa86074: 6666 90e8 

  0x000002251aa86078: ; ImmutableOopMap {[104]=Oop [112]=Oop }
                      ;*invokevirtual getClassLoadingLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@2 (line 651)
  0x000002251aa86078: 64e8 ffff 

  0x000002251aa8607c: ;   {other}
  0x000002251aa8607c: 0f1f 8400 | ec03 0000 | 4889 8424 | 8000 0000 | 488d 9424 | a000 0000 | 488b f048 | 8972 0848 
  0x000002251aa8609c: 8b06 4883 | c801 4889 | 02f0 480f | b116 0f84 | 1200 0000 | 482b c448 | 2507 f0ff | ff48 8902 
  0x000002251aa860bc: 0f85 680b | 0000 49ff | 8750 0500 | 0048 8b54 

  0x000002251aa860cc: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa860cc: 2468 48be | 28ae c138 | 2502 0000 | 4883 8648 | 0100 0001 

  0x000002251aa860e0: ;   {metadata(method data for {method} {0x000002253802a450} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa860e0: 48ba b023 | c138 2502 | 0000 8bb2 | cc00 0000 | 83c6 0289 | b2cc 0000 | 0081 e6fe | ff1f 0085 
  0x000002251aa86100: f60f 8436 

  0x000002251aa86104: ;   {metadata(method data for {method} {0x000002253802a450} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa86104: 0b00 0048 | bab0 23c1 | 3825 0200 | 0048 8382 | 1001 0000 | 0148 8b54 

  0x000002251aa8611c: ;   {static_call}
  0x000002251aa8611c: 2470 90e8 

  0x000002251aa86120: ; ImmutableOopMap {[104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*invokestatic checkName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@1 (line 1298)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@10 (line 653)
  0x000002251aa86120: 9cba fdff 

  0x000002251aa86124: ;   {other}
  0x000002251aa86124: 0f1f 8400 | 9404 0001 

  0x000002251aa8612c: ;   {metadata(method data for {method} {0x000002253802a450} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa8612c: 85c0 49b8 | b023 c138 | 2502 0000 | 48ba 2001 | 0000 0000 | 0000 750a | 48ba 3001 | 0000 0000 
  0x000002251aa8614c: 0000 498b | 3410 488d | 7601 4989 | 3410 0f85 | 0f00 0000 

  0x000002251aa86160: ;   {oop(nullptr)}
  0x000002251aa86160: 48be 0000 | 0000 0000 | 0000 e938 | 0000 0048 | 8b54 2468 

  0x000002251aa86174: ;   {metadata(method data for {method} {0x000002253802a450} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa86174: 49b8 b023 | c138 2502 | 0000 4983 | 8040 0100 | 0001 4c8b | 4424 7048 | 8b54 2468 | 0f1f 8000 
  0x000002251aa86194: ;   {optimized virtual_call}
  0x000002251aa86194: 0000 00e8 

  0x000002251aa86198: ; ImmutableOopMap {[104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual findLoadedClass0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@11 (line 1300)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@10 (line 653)
  0x000002251aa86198: c00c 0000 

  0x000002251aa8619c: ;   {other}
  0x000002251aa8619c: 0f1f 8400 | 0c05 0002 | 488b f048 

  0x000002251aa861a8: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa861a8: 85f6 49b8 | 28ae c138 | 2502 0000 | 48ba 8001 | 0000 0000 | 0000 750a | 48ba 9001 | 0000 0000 
  0x000002251aa861c8: 0000 498b | 3c10 488d | 7f01 4989 | 3c10 0f85 | 0308 0000 | 4889 b424 | 8800 0000 | 488b 5424 
  0x000002251aa861e8: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa861e8: 6849 b828 | aec1 3825 | 0200 0049 | 8380 a001 | 0000 014c | 8b44 2470 | 488b 5424 

  0x000002251aa86204: ;   {optimized virtual_call}
  0x000002251aa86204: 6866 90e8 

  0x000002251aa86208: ; ImmutableOopMap {[104]=Oop [112]=Oop [128]=Oop [136]=Oop [168]=Oop }
                      ;*invokevirtual findLoadedModule {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@22 (line 658)
  0x000002251aa86208: 5f0c 0000 

  0x000002251aa8620c: ;   {other}
  0x000002251aa8620c: 0f1f 8400 | 7c05 0003 

  0x000002251aa86214: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86214: 4885 c049 | b828 aec1 | 3825 0200 | 0049 b9d8 | 0100 0000 | 0000 0074 | 0a49 b9e8 | 0100 0000 
  0x000002251aa86234: 0000 004b | 8b14 0848 | 8d52 014b | 8914 0848 | 8b54 2468 | 0f84 a703 | 0000 483b | 004c 8bc0 
  0x000002251aa86254: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86254: 49b9 28ae | c138 2502 

  0x000002251aa8625c: ;   {metadata('jdk/internal/loader/BuiltinClassLoader$LoadedModule')}
  0x000002251aa8625c: 0000 49ba | 7006 0300 | 0800 0000 | 4d89 9108 | 0200 0049 | 8381 1002 | 0000 018b | 700c 48c1 
  0x000002251aa8627c: e603 483b 

  0x000002251aa86280: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86280: f249 b828 | aec1 3825 | 0200 0049 | b930 0200 | 0000 0000 | 0075 0a49 | b940 0200 | 0000 0000 
  0x000002251aa862a0: 004b 8b3c | 0848 8d7f | 014b 893c | 080f 852a 

  0x000002251aa862b0: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa862b0: 0200 0049 | b828 aec1 | 3825 0200 | 0049 8380 | 5002 0000 

  0x000002251aa862c4: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa862c4: 0149 b880 | 6ab3 3825 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002251aa862e4: feff 1f00 | 4585 c90f | 8472 0900 

  0x000002251aa862f0: ;   {oop(a 'java/lang/Class'{0x00000006238083a0} = 'jdk/internal/misc/VM')}
  0x000002251aa862f0: 0049 b8a0 | 8380 2306 | 0000 0045 | 8b80 9400 | 0000 4183 

  0x000002251aa86304: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa86304: f802 49b8 | 806a b338 | 2502 0000 | 49b9 1001 | 0000 0000 | 0000 7c0a | 49b9 2001 | 0000 0000 
  0x000002251aa86324: 0000 4b8b | 3408 488d | 7601 4b89 | 3408 0f8c | 1c00 0000 

  0x000002251aa86338: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa86338: 49b8 806a | b338 2502 | 0000 41ff | 8030 0100 | 0041 b801 | 0000 00e9 | 0600 0000 | 41b8 0000 
  0x000002251aa86358: 0000 4183 | e001 4585 

  0x000002251aa86360: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86360: c049 b828 | aec1 3825 | 0200 0049 | b970 0200 | 0000 0000 | 0075 0a49 | b960 0200 | 0000 0000 
  0x000002251aa86380: 004b 8b34 | 0848 8d76 | 014b 8934 | 080f 850d | 0000 0048 | 8bb4 2488 | 0000 00e9 | 3f02 0000 
  0x000002251aa863a0: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa863a0: 4c8b c249 | b928 aec1 | 3825 0200 | 0049 8381 | 8002 0000 

  0x000002251aa863b4: ;   {metadata(method data for {method} {0x0000022538121488} 'findClassInModuleOrNull' '(Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa863b4: 0149 b8a0 | b5c1 3825 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002251aa863d4: feff 1f00 | 4585 c90f | 84a3 0800 

  0x000002251aa863e0: ;   {metadata(method data for {method} {0x0000022538121488} 'findClassInModuleOrNull' '(Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa863e0: 0049 b8a0 | b5c1 3825 | 0200 0049 | 8380 1001 

  0x000002251aa863f0: ;   {metadata(method data for {method} {0x0000022538031100} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x000002251aa863f0: 0000 0149 | b800 0779 | 3825 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002251aa86410: 81e1 feff | 1f00 4585 | c90f 8486 

  0x000002251aa8641c: ;   {metadata(method data for {method} {0x0000022538031100} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x000002251aa8641c: 0800 0049 | b800 0779 | 3825 0200 | 0049 8380 | 1001 0000 

  0x000002251aa86430: ;   {metadata(method data for {method} {0x00000225380303f0} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x000002251aa86430: 0149 b878 | 0879 3825 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002251aa86450: feff 1f00 | 4585 c90f | 8469 0800 

  0x000002251aa8645c: ;   {metadata(method data for {method} {0x00000225380303f0} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x000002251aa8645c: 0049 b878 | 0879 3825 | 0200 0041 | ff80 1001 

  0x000002251aa8646c: ;   {metadata(method data for {method} {0x0000022538031100} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x000002251aa8646c: 0000 49b8 | 0007 7938 | 2502 0000 | 41ff 8020 

  0x000002251aa8647c: ;   {metadata(method data for {method} {0x0000022538121488} 'findClassInModuleOrNull' '(Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa8647c: 0100 0049 | b8a0 b5c1 | 3825 0200 | 0041 ff80 | 3001 0000 

  0x000002251aa86490: ;   {metadata(method data for {method} {0x0000022538121488} 'findClassInModuleOrNull' '(Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86490: 4c8b c249 | b9a0 b5c1 | 3825 0200 | 0049 8381 | 4001 0000 | 014c 8b44 | 2470 4c8b | c848 8bf2 
  0x000002251aa864b0: 488b d60f 

  0x000002251aa864b4: ;   {optimized virtual_call}
  0x000002251aa864b4: 1f40 00e8 

  0x000002251aa864b8: ; ImmutableOopMap {[104]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::findClassInModuleOrNull@9 (line 741)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@55 (line 665)
  0x000002251aa864b8: be09 0000 

  0x000002251aa864bc: ;   {other}
  0x000002251aa864bc: 0f1f 8400 | 2c08 0004 

  0x000002251aa864c4: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa864c4: 49b8 28ae | c138 2502 | 0000 41ff | 80b8 0200 | 0048 8bf0 | e902 0100 | 0048 3b06 

  0x000002251aa864e0: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa864e0: 4c8b c649 | b928 aec1 | 3825 0200 | 0049 8381 | d002 0000 

  0x000002251aa864f4: ;   {metadata(method data for {method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa864f4: 0149 b890 | bdc1 3825 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002251aa86514: feff 1f00 | 4585 c90f | 84cb 0700 | 004c 8bc6 

  0x000002251aa86524: ;   {metadata(method data for {method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86524: 49b9 90bd | c138 2502 | 0000 458b | 4008 49ba | 0000 0000 | 0800 0000 | 4d03 c24d | 3b81 2001 
  0x000002251aa86544: 0000 750d | 4983 8128 | 0100 0001 | e960 0000 | 004d 3b81 | 3001 0000 | 750d 4983 | 8138 0100 
  0x000002251aa86564: 0001 e94a | 0000 0049 | 83b9 2001 | 0000 0075 | 174d 8981 | 2001 0000 | 49c7 8128 | 0100 0001 
  0x000002251aa86584: 0000 00e9 | 2900 0000 | 4983 b930 | 0100 0000 | 7517 4d89 | 8130 0100 | 0049 c781 | 3801 0000 
  0x000002251aa865a4: 0100 0000 | e908 0000 | 0049 8381 | 1001 0000 | 014c 8b44 | 2470 41b9 | 0000 0000 | 488b d666 
  0x000002251aa865c4: 9048 b8ff | ffff ffff 

  0x000002251aa865cc: ;   {virtual_call}
  0x000002251aa865cc: ffff ffe8 

  0x000002251aa865d0: ; ImmutableOopMap {[104]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual loadClassOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@3 (line 700)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@66 (line 669)
  0x000002251aa865d0: cc67 4607 

  0x000002251aa865d4: ;   {other}
  0x000002251aa865d4: 0f1f 8400 | 4409 0005 

  0x000002251aa865dc: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa865dc: 488b f049 | b828 aec1 | 3825 0200 | 0041 ff80 | 0803 0000 | e9ea 0300 | 008b 7254 | 48c1 e603 
  0x000002251aa865fc: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa865fc: 4885 f649 | b828 aec1 | 3825 0200 | 0049 b930 | 0300 0000 | 0000 0075 | 0a49 b920 | 0300 0000 
  0x000002251aa8661c: 0000 004b | 8b3c 0848 | 8d7f 014b | 893c 080f | 850d 0000 | 0048 8bb4 | 2488 0000 | 00e9 0101 
  0x000002251aa8663c: 0000 483b | 064c 8bc6 

  0x000002251aa86644: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86644: 49b9 28ae | c138 2502 | 0000 4983 | 8140 0300 

  0x000002251aa86654: ;   {metadata(method data for {method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86654: 0001 49b8 | 90bd c138 | 2502 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002251aa86674: e1fe ff1f | 0045 85c9 | 0f84 9006 | 0000 4c8b 

  0x000002251aa86684: ;   {metadata(method data for {method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86684: c649 b990 | bdc1 3825 | 0200 0045 | 8b40 0849 | ba00 0000 | 0008 0000 | 004d 03c2 | 4d3b 8120 
  0x000002251aa866a4: 0100 0075 | 0d49 8381 | 2801 0000 | 01e9 6000 | 0000 4d3b | 8130 0100 | 0075 0d49 | 8381 3801 
  0x000002251aa866c4: 0000 01e9 | 4a00 0000 | 4983 b920 | 0100 0000 | 7517 4d89 | 8120 0100 | 0049 c781 | 2801 0000 
  0x000002251aa866e4: 0100 0000 | e929 0000 | 0049 83b9 | 3001 0000 | 0075 174d | 8981 3001 | 0000 49c7 | 8138 0100 
  0x000002251aa86704: 0001 0000 | 00e9 0800 | 0000 4983 | 8110 0100 | 0001 4c8b | 4424 7041 | b900 0000 | 0048 8bd6 
  0x000002251aa86724: 9048 b8ff | ffff ffff 

  0x000002251aa8672c: ;   {virtual_call}
  0x000002251aa8672c: ffff ffe8 

  0x000002251aa86730: ; ImmutableOopMap {[104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual loadClassOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@3 (line 700)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@86 (line 676)
  0x000002251aa86730: 4c86 4607 

  0x000002251aa86734: ;   {other}
  0x000002251aa86734: 0f1f 8400 | a40a 0006 | 488b f048 

  0x000002251aa86740: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86740: 85f6 49b8 | 28ae c138 | 2502 0000 | 48ba 7803 | 0000 0000 | 0000 750a | 48ba 8803 | 0000 0000 
  0x000002251aa86760: 0000 498b | 3c10 488d | 7f01 4989 | 3c10 0f85 | 6b02 0000 | 488b 5424 | 684c 8bc2 

  0x000002251aa8677c: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa8677c: 48bf 28ae | c138 2502 | 0000 458b | 4008 49ba | 0000 0000 | 0800 0000 | 4d03 c24c | 3b87 a803 
  0x000002251aa8679c: 0000 750d | 4883 87b0 | 0300 0001 | e960 0000 | 004c 3b87 | b803 0000 | 750d 4883 | 87c0 0300 
  0x000002251aa867bc: 0001 e94a | 0000 0048 | 83bf a803 | 0000 0075 | 174c 8987 | a803 0000 | 48c7 87b0 | 0300 0001 
  0x000002251aa867dc: 0000 00e9 | 2900 0000 | 4883 bfb8 | 0300 0000 | 7517 4c89 | 87b8 0300 | 0048 c787 | c003 0000 
  0x000002251aa867fc: 0100 0000 | e908 0000 | 0048 8387 | 9803 0000 

  0x000002251aa8680c: ;   {metadata(method data for {method} {0x000002253811fa88} 'hasClassPath' '()Z' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa8680c: 0149 b820 | bfc1 3825 | 0200 0041 | 8bb8 cc00 | 0000 83c7 | 0241 89b8 | cc00 0000 | 81e7 feff 
  0x000002251aa8682c: 1f00 85ff | 0f84 fd04 | 0000 448b | 4258 49c1 | e003 4d85 

  0x000002251aa86840: ;   {metadata(method data for {method} {0x000002253811fa88} 'hasClassPath' '()Z' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86840: c049 b820 | bfc1 3825 | 0200 0048 | bf10 0100 | 0000 0000 | 0074 0a48 | bf20 0100 | 0000 0000 
  0x000002251aa86860: 0049 8b1c | 3848 8d5b | 0149 891c | 380f 841c 

  0x000002251aa86870: ;   {metadata(method data for {method} {0x000002253811fa88} 'hasClassPath' '()Z' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86870: 0000 0049 | b820 bfc1 | 3825 0200 | 0041 ff80 | 3001 0000 | 41b8 0100 | 0000 e906 | 0000 0041 
  0x000002251aa86890: b800 0000 | 0041 83e0 | 0145 85c0 

  0x000002251aa8689c: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa8689c: 49b8 28ae | c138 2502 | 0000 48bf | d003 0000 | 0000 0000 | 740a 48bf | e003 0000 | 0000 0000 
  0x000002251aa868bc: 498b 1c38 | 488d 5b01 | 4989 1c38 | 0f84 1101 

  0x000002251aa868cc: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa868cc: 0000 49b8 | 28ae c138 | 2502 0000 | 4983 80f0 | 0300 0001 

  0x000002251aa868e0: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa868e0: 49b8 806a | b338 2502 | 0000 418b | b8cc 0000 | 0083 c702 | 4189 b8cc | 0000 0081 | e7fe ff1f 
  0x000002251aa86900: 0085 ff0f | 844b 0400 

  0x000002251aa86908: ;   {oop(a 'java/lang/Class'{0x00000006238083a0} = 'jdk/internal/misc/VM')}
  0x000002251aa86908: 0049 b8a0 | 8380 2306 | 0000 0045 | 8b80 9400 | 0000 4183 

  0x000002251aa8691c: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa8691c: f802 49b8 | 806a b338 | 2502 0000 | 48bf 1001 | 0000 0000 | 0000 7c0a | 48bf 2001 | 0000 0000 
  0x000002251aa8693c: 0000 498b | 1c38 488d | 5b01 4989 | 1c38 0f8c | 1c00 0000 

  0x000002251aa86950: ;   {metadata(method data for {method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa86950: 49b8 806a | b338 2502 | 0000 41ff | 8030 0100 | 0041 b801 | 0000 00e9 | 0600 0000 | 41b8 0000 
  0x000002251aa86970: 0000 4183 | e001 4585 

  0x000002251aa86978: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86978: c049 b828 | aec1 3825 | 0200 0048 | bf00 0400 | 0000 0000 | 0074 0a48 | bf10 0400 | 0000 0000 
  0x000002251aa86998: 0049 8b1c | 3848 8d5b | 0149 891c | 380f 8434 | 0000 004c | 8b44 2470 

  0x000002251aa869b0: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa869b0: 488b f248 | bf28 aec1 | 3825 0200 | 0048 8387 | 2004 0000 | 0148 8bf2 | 488b d60f 

  0x000002251aa869cc: ;   {optimized virtual_call}
  0x000002251aa869cc: 1f40 00e8 

  0x000002251aa869d0: ; ImmutableOopMap {[104]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual findClassOnClassPathOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@111 (line 681)
  0x000002251aa869d0: ac80 4607 

  0x000002251aa869d4: ;   {other}
  0x000002251aa869d4: 0f1f 8400 | 440d 0007 | 488b f044 | 8b4c 2478 

  0x000002251aa869e4: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa869e4: 4585 c948 | b828 aec1 | 3825 0200 | 0048 ba58 | 0400 0000 | 0000 0074 | 0a48 ba68 | 0400 0000 
  0x000002251aa86a04: 0000 0048 | 8b3c 1048 | 8d7f 0148 | 893c 100f | 84a8 0000 | 0048 85f6 

  0x000002251aa86a1c: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86a1c: 48b8 28ae | c138 2502 | 0000 48ba | 7804 0000 | 0000 0000 | 740a 48ba | 8804 0000 | 0000 0000 
  0x000002251aa86a3c: 488b 3c10 | 488d 7f01 | 4889 3c10 | 0f84 7300 | 0000 488b 

  0x000002251aa86a50: ;   {metadata(method data for {method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86a50: 5424 6848 | b828 aec1 | 3825 0200 | 0048 8380 | 9804 0000 

  0x000002251aa86a64: ;   {metadata(method data for {method} {0x000002253802a0e8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002251aa86a64: 0148 b888 | c3c1 3825 | 0200 008b | 90cc 0000 | 0083 c202 | 8990 cc00 | 0000 81e2 | feff 1f00 
  0x000002251aa86a84: 85d2 0f84 | e902 0000 

  0x000002251aa86a8c: ;   {metadata(method data for {method} {0x000002253802a0e8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002251aa86a8c: 4885 f648 | b888 c3c1 | 3825 0200 | 0048 ba20 | 0100 0000 | 0000 0074 | 0a48 ba10 | 0100 0000 
  0x000002251aa86aac: 0000 0048 | 8b3c 1048 | 8d7f 0148 | 893c 100f | 8443 0000 | 0048 8d84 | 24a0 0000 | 0048 8b10 
  0x000002251aa86acc: 4885 d20f | 840f 0000 | 0048 8b78 | 08f0 480f | b117 0f85 | b202 0000 | 49ff 8f50 | 0500 0048 
  0x000002251aa86aec: 8bc6 4881 | c4c0 0000 

  0x000002251aa86af4: ;   {poll_return}
  0x000002251aa86af4: 005d 493b | a750 0400 | 000f 87a9 | 0200 00c3 | 4889 b424 | 9800 0000 | 0f1f 4000 

  0x000002251aa86b10: ;   {no_reloc}
  0x000002251aa86b10: e9bc 0200 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 3049 3bbf | c801 0000 | 0f87 a902 
  0x000002251aa86b30: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | 0008 0000 | 0049 2bca 
  0x000002251aa86b50: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 4818 | 4889 4820 | 4889 4828 

  0x000002251aa86b6c: ;   {metadata(method data for {method} {0x000002253802a0e8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002251aa86b6c: 488b d048 | be88 c3c1 | 3825 0200 | 0048 8386 | 3001 0000 | 0148 8bd0 | 4889 8424 | 9000 0000 
  0x000002251aa86b8c: ;   {optimized virtual_call}
  0x000002251aa86b8c: 6666 90e8 

  0x000002251aa86b90: ; ImmutableOopMap {[128]=Oop [144]=Oop [152]=Oop [168]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::resolveClass@8 (line 1238)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@128 (line 688)
  0x000002251aa86b90: ec7e 4607 

  0x000002251aa86b94: ;   {other}
  0x000002251aa86b94: 0f1f 8400 | 040f 0008 | 488b 8424 

  0x000002251aa86ba0: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [168]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::resolveClass@11 (line 1238)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@128 (line 688)
  0x000002251aa86ba0: 9000 0000 

  0x000002251aa86ba4: ;   {section_word}
  0x000002251aa86ba4: 48ba a46b | a81a 2502 

  0x000002251aa86bac: ;   {runtime_call handle_exception_nofpu Runtime1 stub}
  0x000002251aa86bac: 0000 e84d | d052 0790 | 498b 8700 | 0500 004d | 33d2 4d89 | 9700 0500 | 004d 33d2 | 4d89 9708 
  0x000002251aa86bcc: 0500 0048 | 8bf0 488d | 8424 a000 | 0000 488b | 3848 85ff | 0f84 0f00 | 0000 488b | 5808 f048 
  0x000002251aa86bec: 0fb1 3b0f | 85f3 0100 | 0049 ff8f | 5005 0000 | 488b c6e9 | 1702 0000 

  0x000002251aa86c04: ;   {metadata({method} {0x0000022538121048} 'loadClassOrNull' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86c04: 49ba 4010 | 1238 2502 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002251aa86c18: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86c18: ffff ffe8 

  0x000002251aa86c1c: ; ImmutableOopMap {rdx=Oop r8=Oop [104]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@-1 (line 651)
  0x000002251aa86c1c: e008 5307 | e9a6 f3ff 

  0x000002251aa86c24: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002251aa86c24: ffe8 d6b7 

  0x000002251aa86c28: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@7 (line 651)
  0x000002251aa86c28: 5207 4889 | 7424 0848 

  0x000002251aa86c30: ;   {runtime_call monitorenter_nofpu Runtime1 stub}
  0x000002251aa86c30: 8914 24e8 

  0x000002251aa86c34: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@7 (line 651)
  0x000002251aa86c34: c8e7 5207 | e98c f4ff 

  0x000002251aa86c3c: ;   {metadata({method} {0x000002253802a450} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa86c3c: ff49 ba48 | a402 3825 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002251aa86c54: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86c54: e8a7 0853 

  0x000002251aa86c58: ; ImmutableOopMap {[104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::findLoadedClass@-1 (line 1298)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@10 (line 653)
  0x000002251aa86c58: 07e9 a9f4 

  0x000002251aa86c5c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002251aa86c5c: ffff e89d 

  0x000002251aa86c60: ; ImmutableOopMap {rax=Oop rdx=Oop [104]=Oop [112]=Oop [128]=Oop [136]=Oop [168]=Oop }
                      ;*invokevirtual loader {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@34 (line 662)
                      ;   {metadata({method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa86c60: b752 0749 | bab0 1619 | 3825 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002251aa86c78: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86c78: ffff e881 

  0x000002251aa86c7c: ; ImmutableOopMap {rax=Oop rdx=Oop [104]=Oop [112]=Oop [128]=Oop [136]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.misc.VM::isModuleSystemInited@-1 (line 91)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@45 (line 664)
  0x000002251aa86c7c: 0853 07e9 | 6df6 ffff 

  0x000002251aa86c84: ;   {metadata({method} {0x0000022538121488} 'findClassInModuleOrNull' '(Ljdk/internal/loader/BuiltinClassLoader$LoadedModule;Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86c84: 49ba 8014 | 1238 2502 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002251aa86c98: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86c98: ffff ffe8 

  0x000002251aa86c9c: ; ImmutableOopMap {rdx=Oop rax=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::findClassInModuleOrNull@-1 (line 740)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@55 (line 665)
  0x000002251aa86c9c: 6008 5307 | e93c f7ff 

  0x000002251aa86ca4: ;   {metadata({method} {0x0000022538031100} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x000002251aa86ca4: ff49 baf8 | 1003 3825 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002251aa86cbc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86cbc: e83f 0853 

  0x000002251aa86cc0: ; ImmutableOopMap {rdx=Oop rax=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::getSecurityManager@-1 (line 504)
                      ; - jdk.internal.loader.BuiltinClassLoader::findClassInModuleOrNull@0 (line 740)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@55 (line 665)
  0x000002251aa86cc0: 07e9 59f7 

  0x000002251aa86cc4: ;   {metadata({method} {0x00000225380303f0} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x000002251aa86cc4: ffff 49ba | e803 0338 | 2502 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002251aa86cdc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86cdc: ffe8 1e08 

  0x000002251aa86ce0: ; ImmutableOopMap {rdx=Oop rax=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::allowSecurityManager@-1 (line 212)
                      ; - java.lang.System::getSecurityManager@0 (line 504)
                      ; - jdk.internal.loader.BuiltinClassLoader::findClassInModuleOrNull@0 (line 740)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@55 (line 665)
  0x000002251aa86ce0: 5307 e976 

  0x000002251aa86ce4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002251aa86ce4: f7ff ffe8 

  0x000002251aa86ce8: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual loadClassOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@66 (line 669)
  0x000002251aa86ce8: 14b7 5207 

  0x000002251aa86cec: ;   {metadata({method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86cec: 49ba 5011 | 1238 2502 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002251aa86d00: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86d00: ffff ffe8 

  0x000002251aa86d04: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@-1 (line 700)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@66 (line 669)
  0x000002251aa86d04: f807 5307 | e914 f8ff 

  0x000002251aa86d0c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002251aa86d0c: ffe8 eeb6 

  0x000002251aa86d10: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*invokevirtual loadClassOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@86 (line 676)
                      ;   {metadata({method} {0x0000022538121158} 'loadClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86d10: 5207 49ba | 5011 1238 | 2502 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002251aa86d28: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86d28: ffe8 d207 

  0x000002251aa86d2c: ; ImmutableOopMap {rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@-1 (line 700)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@86 (line 676)
  0x000002251aa86d2c: 5307 e94f 

  0x000002251aa86d30: ;   {metadata({method} {0x000002253811fa88} 'hasClassPath' '()Z' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa86d30: f9ff ff49 | ba80 fa11 | 3825 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002251aa86d48: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86d48: ffff e8b1 

  0x000002251aa86d4c: ; ImmutableOopMap {rdx=Oop rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::hasClassPath@-1 (line 223)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@97 (line 680)
  0x000002251aa86d4c: 0753 07e9 | e2fa ffff 

  0x000002251aa86d54: ;   {metadata({method} {0x00000225381916b8} 'isModuleSystemInited' '()Z' in 'jdk/internal/misc/VM')}
  0x000002251aa86d54: 49ba b016 | 1938 2502 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002251aa86d68: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86d68: ffff ffe8 

  0x000002251aa86d6c: ; ImmutableOopMap {rdx=Oop rsi=Oop [104]=Oop [112]=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.misc.VM::isModuleSystemInited@-1 (line 91)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@103 (line 680)
  0x000002251aa86d6c: 9007 5307 | e994 fbff 

  0x000002251aa86d74: ;   {metadata({method} {0x000002253802a0e8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002251aa86d74: ff49 bae0 | a002 3825 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002251aa86d8c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa86d8c: e86f 0753 

  0x000002251aa86d90: ; ImmutableOopMap {rsi=Oop [128]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::resolveClass@-1 (line 1237)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@128 (line 688)
  0x000002251aa86d90: 07e9 f6fc | ffff 488d | 8424 a000 | 0000 4889 

  0x000002251aa86da0: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002251aa86da0: 0424 e859 | ec52 07e9 | 3ffd ffff 

  0x000002251aa86dac: ;   {internal_word}
  0x000002251aa86dac: 49ba f66a | a81a 2502 | 0000 4d89 | 9768 0400 

  0x000002251aa86dbc: ;   {runtime_call SafepointBlob}
  0x000002251aa86dbc: 00e9 3ee9 

  0x000002251aa86dc0: ;   {metadata(nullptr)}
  0x000002251aa86dc0: 4607 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002251aa86dd0: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002251aa86dd0: 0ae8 aaf6 

  0x000002251aa86dd4: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::resolveClass@4 (line 1238)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@128 (line 688)
  0x000002251aa86dd4: 5207 e935 | fdff ff48 

  0x000002251aa86ddc: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002251aa86ddc: 8bd2 e81d 

  0x000002251aa86de0: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::resolveClass@4 (line 1238)
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClassOrNull@128 (line 688)
  0x000002251aa86de0: bf52 07e9 | 84fd ffff | 488d 8424 | a000 0000 | 4889 0424 

  0x000002251aa86df4: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002251aa86df4: e807 ec52 | 07e9 fefd | ffff 498b | 8700 0500 | 0049 c787 | 0005 0000 | 0000 0000 | 49c7 8708 
  0x000002251aa86e14: 0500 0000 | 0000 0048 | 81c4 c000 

  0x000002251aa86e20: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002251aa86e20: 0000 5de9 | d8a6 5207 
[Stub Code]
  0x000002251aa86e28: ;   {no_reloc}
  0x000002251aa86e28: 0f1f 4400 

  0x000002251aa86e2c: ;   {static_stub}
  0x000002251aa86e2c: 0048 bb00 | 0000 0000 

  0x000002251aa86e34: ;   {runtime_call}
  0x000002251aa86e34: 0000 00e9 | fbff ffff 

  0x000002251aa86e3c: ;   {static_stub}
  0x000002251aa86e3c: 9048 bb00 | 0000 0000 

  0x000002251aa86e44: ;   {runtime_call}
  0x000002251aa86e44: 0000 00e9 | fbff ffff 

  0x000002251aa86e4c: ;   {static_stub}
  0x000002251aa86e4c: 9048 bb00 | 0000 0000 

  0x000002251aa86e54: ;   {runtime_call}
  0x000002251aa86e54: 0000 00e9 | fbff ffff 

  0x000002251aa86e5c: ;   {static_stub}
  0x000002251aa86e5c: 48bb 08a5 | 0238 2502 

  0x000002251aa86e64: ;   {runtime_call I2C/C2I adapters}
  0x000002251aa86e64: 0000 e97c 

  0x000002251aa86e68: ;   {static_stub}
  0x000002251aa86e68: d346 0748 | bb58 1212 | 3825 0200 

  0x000002251aa86e74: ;   {runtime_call I2C/C2I adapters}
  0x000002251aa86e74: 00e9 6dd3 

  0x000002251aa86e78: ;   {static_stub}
  0x000002251aa86e78: 4607 48bb | 2018 1238 | 2502 0000 

  0x000002251aa86e84: ;   {runtime_call I2C/C2I adapters}
  0x000002251aa86e84: e9e2 3e46 

  0x000002251aa86e88: ;   {static_stub}
  0x000002251aa86e88: 0748 bb00 | 0000 0000 

  0x000002251aa86e90: ;   {runtime_call}
  0x000002251aa86e90: 0000 00e9 | fbff ffff 

  0x000002251aa86e98: ;   {static_stub}
  0x000002251aa86e98: 48bb 0000 | 0000 0000 

  0x000002251aa86ea0: ;   {runtime_call}
  0x000002251aa86ea0: 0000 e9fb 

  0x000002251aa86ea4: ;   {static_stub}
  0x000002251aa86ea4: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002251aa86eb0: ;   {runtime_call}
  0x000002251aa86eb0: 00e9 fbff 

  0x000002251aa86eb4: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002251aa86eb4: ffff e845 

  0x000002251aa86eb8: ;   {external_word}
  0x000002251aa86eb8: d352 0748 | b930 f5c2 | a7fe 7f00 | 0048 83e4 

  0x000002251aa86ec8: ;   {runtime_call}
  0x000002251aa86ec8: f048 b890 | 8285 a7fe | 7f00 00ff 

  0x000002251aa86ed4: ;   {section_word}
  0x000002251aa86ed4: d0f4 49ba | d66e a81a | 2502 0000 

  0x000002251aa86ee0: ;   {runtime_call DeoptimizationBlob}
  0x000002251aa86ee0: 4152 e9b9 | da46 07f4 
[/MachCode]


Compiled method (c1)     474  764       3       jdk.internal.loader.BuiltinClassLoader::loadClass (22 bytes)
 total in heap  [0x000002251aa90590,0x000002251aa90b38] = 1448
 relocation     [0x000002251aa906e8,0x000002251aa90748] = 96
 main code      [0x000002251aa90760,0x000002251aa909f8] = 664
 stub code      [0x000002251aa909f8,0x000002251aa90a50] = 88
 metadata       [0x000002251aa90a50,0x000002251aa90a58] = 8
 scopes data    [0x000002251aa90a58,0x000002251aa90aa0] = 72
 scopes pcs     [0x000002251aa90aa0,0x000002251aa90b20] = 128
 dependencies   [0x000002251aa90b20,0x000002251aa90b28] = 8
 nul chk table  [0x000002251aa90b28,0x000002251aa90b38] = 16

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader'
  # this:     rdx:rdx   = 'jdk/internal/loader/BuiltinClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9        = boolean
  #           [sp+0x50]  (sp of caller)
  0x000002251aa90760: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x000002251aa90774: ;   {runtime_call ic_miss_stub}
  0x000002251aa90774: 0f85 06e0 | 4507 660f | 1f44 0000 
[Verified Entry Point]
  0x000002251aa90780: 8984 2400 | 80ff ff55 | 4883 ec40 | 4181 7f20 | 0100 0000 

  0x000002251aa90794: ;   {runtime_call StubRoutines (final stubs)}
  0x000002251aa90794: 7405 e8c5 

  0x000002251aa90798: ;   {metadata(method data for {method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa90798: 4544 0748 | be60 03c4 | 3825 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 fe07 
  0x000002251aa907b8: 0000 85ff | 0f84 ac01 | 0000 488b 

  0x000002251aa907c4: ;   {metadata(method data for {method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa907c4: f248 bf60 | 03c4 3825 | 0200 008b | 7608 49ba | 0000 0000 | 0800 0000 | 4903 f248 | 3bb7 2001 
  0x000002251aa907e4: 0000 750d | 4883 8728 | 0100 0001 | e960 0000 | 0048 3bb7 | 3001 0000 | 750d 4883 | 8738 0100 
  0x000002251aa90804: 0001 e94a | 0000 0048 | 83bf 2001 | 0000 0075 | 1748 89b7 | 2001 0000 | 48c7 8728 | 0100 0001 
  0x000002251aa90824: 0000 00e9 | 2900 0000 | 4883 bf30 | 0100 0000 | 7517 4889 | b730 0100 | 0048 c787 | 3801 0000 
  0x000002251aa90844: 0100 0000 | e908 0000 | 0048 8387 | 1001 0000 | 0149 8bf0 | 4c8b c648 | 8974 2420 | 0f1f 4400 
  0x000002251aa90864: 0048 b840 | 4d05 0008 

  0x000002251aa9086c: ;   {virtual_call}
  0x000002251aa9086c: 0000 00e8 

  0x000002251aa90870: ; ImmutableOopMap {[32]=Oop }
                      ;*invokevirtual loadClassOrNull {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClass@3 (line 639)
  0x000002251aa90870: ecc4 4507 

  0x000002251aa90874: ;   {other}
  0x000002251aa90874: 0f1f 8400 | e402 0000 

  0x000002251aa9087c: ;   {metadata(method data for {method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa9087c: 4885 c048 | ba60 03c4 | 3825 0200 | 0048 be58 | 0100 0000 | 0000 0074 | 0a48 be48 | 0100 0000 
  0x000002251aa9089c: 0000 0048 | 8b3c 3248 | 8d7f 0148 | 893c 320f | 8413 0000 | 0048 83c4 

  0x000002251aa908b4: ;   {poll_return}
  0x000002251aa908b4: 405d 493b | a750 0400 | 000f 87cc | 0000 00c3 | 4c8b 4424 | 200f 1f80 | 0000 0000 

  0x000002251aa908d0: ;   {no_reloc}
  0x000002251aa908d0: e9df 0000 | 0000 0000 | 0000 80ba | 4101 0000 | 040f 85dc | 0000 0049 | 8b87 b801 | 0000 488d 
  0x000002251aa908f0: 7828 493b | bfc8 0100 | 000f 87c4 | 0000 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca 
  0x000002251aa90910: 49ba 0000 | 0000 0800 | 0000 492b | ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8948 
  0x000002251aa90930: 1848 8948 | 2048 8bd0 

  0x000002251aa90938: ;   {metadata(method data for {method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa90938: 48be 6003 | c438 2502 | 0000 4883 | 8668 0100 | 0001 488b | d048 8944 | 2428 0f1f 

  0x000002251aa90954: ;   {optimized virtual_call}
  0x000002251aa90954: 4400 00e8 

  0x000002251aa90958: ; ImmutableOopMap {[40]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClass@16 (line 641)
  0x000002251aa90958: 24e1 4507 

  0x000002251aa9095c: ;   {other}
  0x000002251aa9095c: 0f1f 8400 | cc03 0001 | 488b 4424 | 28e9 7f00 

  0x000002251aa9096c: ;   {metadata({method} {0x0000022538120e88} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'jdk/internal/loader/BuiltinClassLoader')}
  0x000002251aa9096c: 0000 49ba | 800e 1238 | 2502 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002251aa90984: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa90984: ffe8 766b 

  0x000002251aa90988: ; ImmutableOopMap {rdx=Oop r8=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClass@-1 (line 639)
  0x000002251aa90988: 5207 e933 

  0x000002251aa9098c: ;   {internal_word}
  0x000002251aa9098c: feff ff49 | bab6 08a9 | 1a25 0200 | 004d 8997 | 6804 0000 

  0x000002251aa909a0: ;   {runtime_call SafepointBlob}
  0x000002251aa909a0: e95b 4d46 

  0x000002251aa909a4: ;   {metadata(nullptr)}
  0x000002251aa909a4: 0748 ba00 | 0000 0000 | 0000 00b8 | 000f 050a 

  0x000002251aa909b4: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002251aa909b4: e8c7 5a52 

  0x000002251aa909b8: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.loader.BuiltinClassLoader::loadClass@11 (line 641)
  0x000002251aa909b8: 07e9 12ff 

  0x000002251aa909bc: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002251aa909bc: ffff e83d 

  0x000002251aa909c0: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClass@11 (line 641)
  0x000002251aa909c0: 1a52 0748 

  0x000002251aa909c4: ;   {runtime_call fast_new_instance_init_check Runtime1 stub}
  0x000002251aa909c4: 8bd2 e835 

  0x000002251aa909c8: ; ImmutableOopMap {r8=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.loader.BuiltinClassLoader::loadClass@11 (line 641)
  0x000002251aa909c8: 2652 07e9 | 65ff ffff | 498b 8700 | 0500 0049 | c787 0005 | 0000 0000 | 0000 49c7 | 8708 0500 
  0x000002251aa909e8: 0000 0000 | 0048 83c4 

  0x000002251aa909f0: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002251aa909f0: 405d e909 | 0b52 07f4 
[Stub Code]
  0x000002251aa909f8: ;   {no_reloc}
  0x000002251aa909f8: 0f1f 4400 

  0x000002251aa909fc: ;   {static_stub}
  0x000002251aa909fc: 0048 bb00 | 0000 0000 

  0x000002251aa90a04: ;   {runtime_call}
  0x000002251aa90a04: 0000 00e9 | fbff ffff 

  0x000002251aa90a0c: ;   {static_stub}
  0x000002251aa90a0c: 9048 bb00 | 0000 0000 

  0x000002251aa90a14: ;   {runtime_call}
  0x000002251aa90a14: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x000002251aa90a1c: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002251aa90a1c: e8df 3752 

  0x000002251aa90a20: ;   {external_word}
  0x000002251aa90a20: 0748 b930 | f5c2 a7fe | 7f00 0048 

  0x000002251aa90a2c: ;   {runtime_call}
  0x000002251aa90a2c: 83e4 f048 | b890 8285 | a7fe 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x000002251aa90a3c: ;   {section_word}
  0x000002251aa90a3c: 49ba 3c0a | a91a 2502 | 0000 4152 

  0x000002251aa90a48: ;   {runtime_call DeoptimizationBlob}
  0x000002251aa90a48: e953 3f46 | 07f4 f4f4 
[/MachCode]


Compiled method (c1)     480  763       3       java.lang.ClassLoader::loadClass (7 bytes)
 total in heap  [0x000002251aa90190,0x000002251aa90580] = 1008
 relocation     [0x000002251aa902e8,0x000002251aa90328] = 64
 main code      [0x000002251aa90340,0x000002251aa904d0] = 400
 stub code      [0x000002251aa904d0,0x000002251aa90518] = 72
 metadata       [0x000002251aa90518,0x000002251aa90520] = 8
 scopes data    [0x000002251aa90520,0x000002251aa90538] = 24
 scopes pcs     [0x000002251aa90538,0x000002251aa90578] = 64
 dependencies   [0x000002251aa90578,0x000002251aa90580] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000022538028808} 'loadClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  #           [sp+0x40]  (sp of caller)
  0x000002251aa90340: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x000002251aa90354: ;   {runtime_call ic_miss_stub}
  0x000002251aa90354: 0f85 26e4 | 4507 660f | 1f44 0000 
[Verified Entry Point]
  0x000002251aa90360: 8984 2400 | 80ff ff55 | 4883 ec30 | 4181 7f20 | 0100 0000 

  0x000002251aa90374: ;   {runtime_call StubRoutines (final stubs)}
  0x000002251aa90374: 7405 e8e5 

  0x000002251aa90378: ;   {metadata(method data for {method} {0x0000022538028808} 'loadClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa90378: 4944 0749 | b960 ffc3 | 3825 0200 | 0041 8bb1 | cc00 0000 | 83c6 0241 | 89b1 cc00 | 0000 81e6 
  0x000002251aa90398: fe07 0000 | 85f6 0f84 | cb00 0000 

  0x000002251aa903a4: ;   {metadata(method data for {method} {0x0000022538028808} 'loadClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa903a4: 4c8b ca48 | be60 ffc3 | 3825 0200 | 0045 8b49 | 0849 ba00 | 0000 0008 | 0000 004d | 03ca 4c3b 
  0x000002251aa903c4: 8e20 0100 | 0075 0d48 | 8386 2801 | 0000 01e9 | 6000 0000 | 4c3b 8e30 | 0100 0075 | 0d48 8386 
  0x000002251aa903e4: 3801 0000 | 01e9 4a00 | 0000 4883 | be20 0100 | 0000 7517 | 4c89 8e20 | 0100 0048 | c786 2801 
  0x000002251aa90404: 0000 0100 | 0000 e929 | 0000 0048 | 83be 3001 | 0000 0075 | 174c 898e | 3001 0000 | 48c7 8638 
  0x000002251aa90424: 0100 0001 | 0000 00e9 | 0800 0000 | 4883 8610 | 0100 0001 | 41b9 0000 | 0000 0f1f | 8000 0000 
  0x000002251aa90444: 0048 b840 | 4d05 0008 

  0x000002251aa9044c: ;   {virtual_call}
  0x000002251aa9044c: 0000 00e8 

  0x000002251aa90450: ; ImmutableOopMap {}
                      ;*invokevirtual loadClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@3 (line 526)
  0x000002251aa90450: ccc8 4507 

  0x000002251aa90454: ;   {other}
  0x000002251aa90454: 0f1f 8400 | c402 0000 | 4883 c430 

  0x000002251aa90460: ;   {poll_return}
  0x000002251aa90460: 5d49 3ba7 | 5004 0000 | 0f87 2200 

  0x000002251aa9046c: ;   {metadata({method} {0x0000022538028808} 'loadClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002251aa9046c: 0000 c349 | ba00 8802 | 3825 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002251aa90484: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002251aa90484: ffff e875 

  0x000002251aa90488: ; ImmutableOopMap {rdx=Oop r8=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::loadClass@-1 (line 526)
  0x000002251aa90488: 7052 07e9 | 14ff ffff 

  0x000002251aa90490: ;   {internal_word}
  0x000002251aa90490: 49ba 6104 | a91a 2502 | 0000 4d89 | 9768 0400 

  0x000002251aa904a0: ;   {runtime_call SafepointBlob}
  0x000002251aa904a0: 00e9 5a52 | 4607 498b | 8700 0500 | 0049 c787 | 0005 0000 | 0000 0000 | 49c7 8708 | 0500 0000 
  0x000002251aa904c0: 0000 0048 | 83c4 305d 

  0x000002251aa904c8: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002251aa904c8: e933 1052 | 07f4 f4f4 
[Stub Code]
  0x000002251aa904d0: ;   {no_reloc}
  0x000002251aa904d0: 0f1f 4400 

  0x000002251aa904d4: ;   {static_stub}
  0x000002251aa904d4: 0048 bb00 | 0000 0000 

  0x000002251aa904dc: ;   {runtime_call}
  0x000002251aa904dc: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x000002251aa904e4: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002251aa904e4: e817 3d52 

  0x000002251aa904e8: ;   {external_word}
  0x000002251aa904e8: 0748 b930 | f5c2 a7fe | 7f00 0048 

  0x000002251aa904f4: ;   {runtime_call}
  0x000002251aa904f4: 83e4 f048 | b890 8285 | a7fe 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x000002251aa90504: ;   {section_word}
  0x000002251aa90504: 49ba 0405 | a91a 2502 | 0000 4152 

  0x000002251aa90510: ;   {runtime_call DeoptimizationBlob}
  0x000002251aa90510: e98b 4446 | 07f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000022537ac9490, length=12, elements={
0x0000022516686c60, 0x00000225372691b0, 0x00000225372b87a0, 0x00000225372e3760,
0x00000225372e41c0, 0x00000225372e4c20, 0x00000225372e5680, 0x00000225372e6410,
0x00000225372e7bf0, 0x000002253747fd00, 0x0000022537496b00, 0x000002253793a1d0
}

Java Threads: ( => current thread )
=>0x0000022516686c60 JavaThread "main"                              [_thread_in_vm, id=34152, stack(0x000000ec09f00000,0x000000ec0a000000) (1024K)]
  0x00000225372691b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=43116, stack(0x000000ec0a700000,0x000000ec0a800000) (1024K)]
  0x00000225372b87a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=27868, stack(0x000000ec0a800000,0x000000ec0a900000) (1024K)]
  0x00000225372e3760 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=43416, stack(0x000000ec0a900000,0x000000ec0aa00000) (1024K)]
  0x00000225372e41c0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=24832, stack(0x000000ec0aa00000,0x000000ec0ab00000) (1024K)]
  0x00000225372e4c20 JavaThread "Service Thread"             daemon [_thread_blocked, id=30588, stack(0x000000ec0ab00000,0x000000ec0ac00000) (1024K)]
  0x00000225372e5680 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=8800, stack(0x000000ec0ac00000,0x000000ec0ad00000) (1024K)]
  0x00000225372e6410 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=27980, stack(0x000000ec0ad00000,0x000000ec0ae00000) (1024K)]
  0x00000225372e7bf0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12876, stack(0x000000ec0ae00000,0x000000ec0af00000) (1024K)]
  0x000002253747fd00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=42752, stack(0x000000ec0af00000,0x000000ec0b000000) (1024K)]
  0x0000022537496b00 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=42196, stack(0x000000ec0b000000,0x000000ec0b100000) (1024K)]
  0x000002253793a1d0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=42748, stack(0x000000ec0b100000,0x000000ec0b200000) (1024K)]
Total: 12

Other Threads:
  0x0000022537231fc0 VMThread "VM Thread"                           [id=27920, stack(0x000000ec0a600000,0x000000ec0a700000) (1024K)]
  0x0000022534686050 WatcherThread "VM Periodic Task Thread"        [id=38516, stack(0x000000ec0a500000,0x000000ec0a600000) (1024K)]
  0x0000022534520980 WorkerThread "GC Thread#0"                     [id=42764, stack(0x000000ec0a000000,0x000000ec0a100000) (1024K)]
  0x0000022516701670 ConcurrentGCThread "G1 Main Marker"            [id=12180, stack(0x000000ec0a100000,0x000000ec0a200000) (1024K)]
  0x0000022516702080 WorkerThread "G1 Conc#0"                       [id=20244, stack(0x000000ec0a200000,0x000000ec0a300000) (1024K)]
  0x00000225345c7f70 ConcurrentGCThread "G1 Refine#0"               [id=27844, stack(0x000000ec0a300000,0x000000ec0a400000) (1024K)]
  0x00000225345c8790 ConcurrentGCThread "G1 Service"                [id=43356, stack(0x000000ec0a400000,0x000000ec0a500000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffea7ee4748] Metaspace_lock - owner thread: 0x0000022516686c60

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 8192K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 0 survivors (0K)
 Metaspace       used 14763K, committed 14976K, reserved 1114112K
  class space    used 1356K, committed 1472K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 125|0x0000000623000000, 0x00000006233ed870, 0x0000000623400000| 98%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000002252b5e0000,0x000002252c5d0000] _byte_map_base: 0x00000225285c2000

Marking Bits: (CMBitMap*) 0x00000225166f0f80
 Bits: [0x000002252c5d0000, 0x00000225344e0000)

Polling page: 0x0000022514d30000

Metaspace:

Usage:
  Non-class:     13.09 MB used.
      Class:      1.32 MB used.
       Both:     14.42 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      13.19 MB ( 21%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      14.62 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  2.55 MB
       Class:  14.61 MB
        Both:  17.16 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 138.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 234.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 247.
num_chunk_merges: 0.
num_chunk_splits: 144.
num_chunks_enlarged: 74.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=244Kb max_used=244Kb free=119755Kb
 bounds [0x0000022522440000, 0x00000225226b0000, 0x0000022529970000]
CodeHeap 'profiled nmethods': size=120000Kb used=1275Kb max_used=1275Kb free=118724Kb
 bounds [0x000002251a970000, 0x000002251abe0000, 0x0000022521ea0000]
CodeHeap 'non-nmethods': size=5760Kb used=1395Kb max_used=1410Kb free=4364Kb
 bounds [0x0000022521ea0000, 0x0000022522110000, 0x0000022522440000]
 total_blobs=1318 nmethods=828 adapters=395
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.406 Thread 0x00000225372e7bf0 nmethod 815 0x000002252247c310 code [0x000002252247c4a0, 0x000002252247c570]
Event: 0.406 Thread 0x00000225372e7bf0  816       1       jdk.internal.jimage.decompressor.CompressedResourceHeader::getUncompressedSize (5 bytes)
Event: 0.406 Thread 0x00000225372e7bf0 nmethod 816 0x000002252247c610 code [0x000002252247c7a0, 0x000002252247c868]
Event: 0.406 Thread 0x00000225372e7bf0  817       1       java.nio.HeapByteBuffer::isDirect (2 bytes)
Event: 0.406 Thread 0x00000225372e7bf0 nmethod 817 0x000002252247c910 code [0x000002252247caa0, 0x000002252247cb68]
Event: 0.407 Thread 0x00000225372e7bf0  820   !   3       java.lang.invoke.MethodHandle::setVarargs (50 bytes)
Event: 0.408 Thread 0x00000225372e7bf0 nmethod 820 0x000002251aaa3a10 code [0x000002251aaa3cc0, 0x000002251aaa4870]
Event: 0.408 Thread 0x00000225372e7bf0  821       3       java.lang.invoke.MemberName::isVarargs (23 bytes)
Event: 0.408 Thread 0x00000225372e7bf0 nmethod 821 0x000002251aaa4c90 code [0x000002251aaa4e40, 0x000002251aaa51b0]
Event: 0.408 Thread 0x00000225372e7bf0  822       3       java.lang.invoke.MethodHandles$Lookup::isArrayClone (50 bytes)
Event: 0.409 Thread 0x00000225372e7bf0 nmethod 822 0x000002251aaa5290 code [0x000002251aaa54a0, 0x000002251aaa5b78]
Event: 0.409 Thread 0x00000225372e7bf0  823       3       sun.invoke.util.VerifyAccess::isMemberAccessible (338 bytes)
Event: 0.409 Thread 0x00000225372e7bf0 nmethod 823 0x000002251aaa5d90 code [0x000002251aaa6120, 0x000002251aaa7400]
Event: 0.409 Thread 0x00000225372e7bf0  824       3       java.lang.StringBuilder::<init> (6 bytes)
Event: 0.409 Thread 0x00000225372e7bf0 nmethod 824 0x000002251aaa7990 code [0x000002251aaa7b40, 0x000002251aaa7c78]
Event: 0.409 Thread 0x00000225372e7bf0  825       3       jdk.internal.org.objectweb.asm.MethodVisitor::<init> (78 bytes)
Event: 0.410 Thread 0x00000225372e7bf0 nmethod 825 0x000002251aaa7d10 code [0x000002251aaa7f40, 0x000002251aaa8720]
Event: 0.412 Thread 0x00000225372e7bf0  826       3       jdk.internal.misc.Unsafe::getLongUnaligned (173 bytes)
Event: 0.412 Thread 0x00000225372e7bf0 nmethod 826 0x000002251aaa8a10 code [0x000002251aaa8c00, 0x000002251aaa9028]
Event: 0.412 Thread 0x00000225372e7bf0  827   !   3       java.util.concurrent.ConcurrentHashMap::computeIfAbsent (576 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.014 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (12 events):
Event: 0.218 Thread 0x0000022516686c60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000225224526fc relative=0x000000000000007c
Event: 0.218 Thread 0x0000022516686c60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000225224526fc method=java.lang.CharacterDataLatin1.toLowerCase(I)I @ 16 c2
Event: 0.218 Thread 0x0000022516686c60 DEOPT PACKING pc=0x00000225224526fc sp=0x000000ec09ffb2f0
Event: 0.218 Thread 0x0000022516686c60 DEOPT UNPACKING pc=0x0000022521ef46a2 sp=0x000000ec09ffb280 mode 2
Event: 0.271 Thread 0x0000022516686c60 DEOPT PACKING pc=0x000002251a9b71a3 sp=0x000000ec09ffe0b0
Event: 0.271 Thread 0x0000022516686c60 DEOPT UNPACKING pc=0x0000022521ef4e42 sp=0x000000ec09ffd548 mode 0
Event: 0.283 Thread 0x0000022516686c60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002252245de30 relative=0x0000000000000090
Event: 0.283 Thread 0x0000022516686c60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002252245de30 method=jdk.internal.misc.Unsafe.convEndian(ZI)I @ 4 c2
Event: 0.283 Thread 0x0000022516686c60 DEOPT PACKING pc=0x000002252245de30 sp=0x000000ec09ffe0b0
Event: 0.283 Thread 0x0000022516686c60 DEOPT UNPACKING pc=0x0000022521ef46a2 sp=0x000000ec09ffdfe0 mode 2
Event: 0.334 Thread 0x0000022516686c60 DEOPT PACKING pc=0x000002251a98d878 sp=0x000000ec09ffe760
Event: 0.334 Thread 0x0000022516686c60 DEOPT UNPACKING pc=0x0000022521ef4e42 sp=0x000000ec09ffdb80 mode 0

Classes loaded (20 events):
Event: 0.401 Loading class java/util/Base64$Encoder
Event: 0.401 Loading class java/util/Base64$Encoder done
Event: 0.402 Loading class java/lang/MatchException
Event: 0.402 Loading class java/lang/MatchException done
Event: 0.403 Loading class java/net/URLPermission
Event: 0.403 Loading class java/net/URLPermission done
Event: 0.406 Loading class java/util/concurrent/CompletableFuture$UniCompose
Event: 0.406 Loading class java/util/concurrent/CompletableFuture$UniCompletion
Event: 0.406 Loading class java/util/concurrent/CompletableFuture$UniCompletion done
Event: 0.406 Loading class java/util/concurrent/CompletableFuture$UniCompose done
Event: 0.406 Loading class java/util/concurrent/ForkJoinTask$Aux
Event: 0.406 Loading class java/util/concurrent/ForkJoinTask$Aux done
Event: 0.407 Loading class java/util/concurrent/CompletableFuture$UniComposeExceptionally
Event: 0.407 Loading class java/util/concurrent/CompletableFuture$UniComposeExceptionally done
Event: 0.407 Loading class java/util/concurrent/CompletableFuture$AsyncSupply
Event: 0.407 Loading class java/util/concurrent/CompletableFuture$AsyncSupply done
Event: 0.408 Loading class java/util/LinkedList$ListItr
Event: 0.408 Loading class java/util/LinkedList$ListItr done
Event: 0.410 Loading class java/lang/ExceptionInInitializerError
Event: 0.410 Loading class java/lang/ExceptionInInitializerError done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.293 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237087b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237087b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.294 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237172a8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237172a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.295 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062371cd40}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x000000062371cd40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.295 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237226d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237226d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.308 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237563b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237563b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.309 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062375f930}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062375f930) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.312 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623775408}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000623775408) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.317 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623797408}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623797408) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.317 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062379aaf8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062379aaf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.317 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062379eea0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x000000062379eea0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.318 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237a64b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x00000006237a64b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.318 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237acf48}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x00000006237acf48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.318 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b3b00}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x00000006237b3b00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.319 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b8188}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000006237b8188) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.323 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237e4f68}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006237e4f68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.324 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237f4608}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237f4608) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.327 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230269f0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006230269f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.327 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x000000062302d2c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062302d2c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.328 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230306e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230306e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.394 Thread 0x0000022516686c60 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623272418}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623272418) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (6 events):
Event: 0.116 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.116 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.127 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.128 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.265 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.265 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (18 events):
Event: 0.013 Thread 0x0000022516686c60 Thread added: 0x0000022516686c60
Event: 0.062 Thread 0x0000022516686c60 Thread added: 0x00000225372691b0
Event: 0.063 Thread 0x0000022516686c60 Thread added: 0x00000225372b87a0
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e3760
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e41c0
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e4c20
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e5680
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e6410
Event: 0.064 Thread 0x0000022516686c60 Thread added: 0x00000225372e7bf0
Event: 0.089 Thread 0x0000022516686c60 Thread added: 0x000002253747fd00
Event: 0.095 Thread 0x0000022516686c60 Thread added: 0x0000022537496b00
Event: 0.103 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.106 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.112 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.173 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.183 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.386 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.390 Thread 0x0000022516686c60 Thread added: 0x000002253793a1d0


Dynamic libraries:
0x00007ff665af0000 - 0x00007ff665afa000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007fff2b5d0000 - 0x00007fff2b7e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff2aa50000 - 0x00007fff2ab14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff288a0000 - 0x00007fff28c5a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff28e00000 - 0x00007fff28f11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff14a40000 - 0x00007fff14a58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007fff23f60000 - 0x00007fff23f7b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007fff2b3e0000 - 0x00007fff2b58e000 	C:\WINDOWS\System32\USER32.dll
0x00007fff28fa0000 - 0x00007fff28fc6000 	C:\WINDOWS\System32\win32u.dll
0x00007fff29670000 - 0x00007fff29699000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff28ce0000 - 0x00007fff28dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff29230000 - 0x00007fff292ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffefb160000 - 0x00007ffefb3f2000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007fff29fb0000 - 0x00007fff2a057000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff2ab40000 - 0x00007fff2ab71000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000072030000 - 0x000000007203d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007fff2abd0000 - 0x00007fff2ac82000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff2a610000 - 0x00007fff2a6b7000 	C:\WINDOWS\System32\sechost.dll
0x00007fff29200000 - 0x00007fff29228000 	C:\WINDOWS\System32\bcrypt.dll
0x00007fff2a4e0000 - 0x00007fff2a5f4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffedfa30000 - 0x00007ffedfb35000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007fff29720000 - 0x00007fff29f98000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff296a0000 - 0x00007fff296fe000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007fff281f0000 - 0x00007fff281fa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff24070000 - 0x00007fff2407c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffef9fe0000 - 0x00007ffefa06d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffea7220000 - 0x00007ffea7fd7000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007fff292d0000 - 0x00007fff29341000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff1a710000 - 0x00007fff1a744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff28770000 - 0x00007fff287bd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff28750000 - 0x00007fff28763000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff27810000 - 0x00007fff27828000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff24050000 - 0x00007fff2405a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007fff26160000 - 0x00007fff26392000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff2ac90000 - 0x00007fff2b01f000 	C:\WINDOWS\System32\combase.dll
0x00007fff29350000 - 0x00007fff29427000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff12590000 - 0x00007fff125c2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff28f20000 - 0x00007fff28f9b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff14a20000 - 0x00007fff14a3f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007fff14960000 - 0x00007fff14978000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007fff26740000 - 0x00007fff27048000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff26600000 - 0x00007fff2673f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007fff2b020000 - 0x00007fff2b11a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff287d0000 - 0x00007fff287fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff14a10000 - 0x00007fff14a20000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007fff222b0000 - 0x00007fff223e6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff27c90000 - 0x00007fff27cf9000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff145b0000 - 0x00007fff145c6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007fff0ccf0000 - 0x00007fff0ccfe000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007fff28fd0000 - 0x00007fff29136000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff281b0000 - 0x00007fff281dd000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff28170000 - 0x00007fff281a7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff27ef0000 - 0x00007fff27f0b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff27770000 - 0x00007fff277a5000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff27d80000 - 0x00007fff27da8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff27ee0000 - 0x00007fff27eec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff28200000 - 0x00007fff2822d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff2a600000 - 0x00007fff2a609000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe9c2d0000 - 0x00007ffe9c2d8000 	C:\WINDOWS\system32\wshunix.dll
0x00007fff0cce0000 - 0x00007fff0cce9000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 20, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 67444K (0% of 33293192K total physical memory with 2287408K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 13696K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1051K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 24 days 4:01 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (2233M free)
TotalPageFile size 42752M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 65M, peak: 65M
current process commit charge ("private bytes"): 614M, peak: 615M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
